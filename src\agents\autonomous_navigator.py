"""
Autonomous Navigation Agent for Cipher-Spy.

This agent intelligently navigates websites, interacts with page elements,
and triggers API calls through DOM manipulation and user simulation.
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from urllib.parse import urlparse, urljoin

from playwright.async_api import Page, ElementHandle, Locator
from ..utils.logging import get_logger
from ..core.exceptions import CrawlingException


class AutonomousNavigator:
    """
    Intelligent web navigation agent that autonomously explores websites
    and triggers API calls through realistic user interactions.
    """

    def __init__(self, page: Page, scope_manager=None):
        self.page = page
        self.scope_manager = scope_manager
        self.logger = get_logger(__name__)

        # Navigation state
        self.visited_states: Set[str] = set()
        self.interaction_history: List[Dict[str, Any]] = []
        self.discovered_elements: Dict[str, Any] = {}

        # Element selectors for common interactive elements
        self.interactive_selectors = [
            'button',
            'a[href]',
            'input[type="submit"]',
            'input[type="button"]',
            '[role="button"]',
            '[onclick]',
            '.btn',
            '.button',
            '[data-testid*="button"]',
            '[class*="button"]'
        ]

        # Form selectors
        self.form_selectors = [
            'form',
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="number"]',
            'textarea',
            'select'
        ]

        # API-triggering patterns
        self.api_patterns = [
            r'api',
            r'ajax',
            r'fetch',
            r'load',
            r'submit',
            r'search',
            r'filter',
            r'sort',
            r'create',
            r'update',
            r'delete'
        ]

    async def explore_page(self, max_interactions: int = 20) -> Dict[str, Any]:
        """
        Autonomously explore the current page and trigger API calls.

        Args:
            max_interactions: Maximum number of interactions to perform

        Returns:
            Dict containing exploration results
        """
        try:
            self.logger.info(f"Starting autonomous exploration of {self.page.url}")

            # Get initial page state
            initial_state = await self._capture_page_state()

            # Discover interactive elements
            interactive_elements = await self._discover_interactive_elements()
            self.logger.info(f"Discovered {len(interactive_elements)} interactive elements")

            # Discover forms
            forms = await self._discover_forms()
            self.logger.info(f"Discovered {len(forms)} forms")

            # Perform intelligent interactions
            interactions_performed = 0
            api_calls_triggered = []

            for element_info in interactive_elements[:max_interactions]:
                if interactions_performed >= max_interactions:
                    break

                try:
                    # Check if we should interact with this element
                    if await self._should_interact_with_element(element_info):
                        api_calls_before = await self._count_network_requests()

                        # Perform interaction
                        interaction_result = await self._interact_with_element(element_info)

                        if interaction_result['success']:
                            interactions_performed += 1

                            # Wait for potential API calls
                            await asyncio.sleep(2)

                            # Check for new API calls
                            api_calls_after = await self._count_network_requests()
                            if api_calls_after > api_calls_before:
                                api_calls_triggered.append({
                                    'element': element_info,
                                    'interaction': interaction_result,
                                    'api_calls_triggered': api_calls_after - api_calls_before
                                })

                                self.logger.info(f"Interaction triggered {api_calls_after - api_calls_before} API calls")

                except Exception as e:
                    self.logger.warning(f"Failed to interact with element: {e}")
                    continue

            # Fill and submit forms
            for form_info in forms:
                if interactions_performed >= max_interactions:
                    break

                try:
                    api_calls_before = await self._count_network_requests()

                    form_result = await self._interact_with_form(form_info)

                    if form_result['success']:
                        interactions_performed += 1

                        # Wait for form submission API calls
                        await asyncio.sleep(3)

                        api_calls_after = await self._count_network_requests()
                        if api_calls_after > api_calls_before:
                            api_calls_triggered.append({
                                'form': form_info,
                                'interaction': form_result,
                                'api_calls_triggered': api_calls_after - api_calls_before
                            })

                except Exception as e:
                    self.logger.warning(f"Failed to interact with form: {e}")
                    continue

            # Capture final page state
            final_state = await self._capture_page_state()

            return {
                'initial_state': initial_state,
                'final_state': final_state,
                'interactions_performed': interactions_performed,
                'api_calls_triggered': api_calls_triggered,
                'interactive_elements_found': len(interactive_elements),
                'forms_found': len(forms),
                'exploration_successful': True
            }

        except Exception as e:
            self.logger.error(f"Error during autonomous exploration: {e}")
            return {
                'exploration_successful': False,
                'error': str(e),
                'interactions_performed': interactions_performed,
                'api_calls_triggered': api_calls_triggered
            }

    async def _discover_interactive_elements(self) -> List[Dict[str, Any]]:
        """Discover all interactive elements on the page."""
        elements = []

        for selector in self.interactive_selectors:
            try:
                locators = self.page.locator(selector)
                count = await locators.count()

                for i in range(count):
                    try:
                        element = locators.nth(i)

                        # Check if element is visible and enabled
                        if await element.is_visible() and await element.is_enabled():
                            element_info = await self._analyze_element(element, selector)
                            if element_info:
                                elements.append(element_info)

                    except Exception as e:
                        self.logger.debug(f"Error analyzing element {i} for selector {selector}: {e}")
                        continue

            except Exception as e:
                self.logger.debug(f"Error with selector {selector}: {e}")
                continue

        return elements

    async def _discover_forms(self) -> List[Dict[str, Any]]:
        """Discover all forms on the page."""
        forms = []

        try:
            form_locators = self.page.locator('form')
            count = await form_locators.count()

            for i in range(count):
                try:
                    form = form_locators.nth(i)

                    if await form.is_visible():
                        form_info = await self._analyze_form(form)
                        if form_info:
                            forms.append(form_info)

                except Exception as e:
                    self.logger.debug(f"Error analyzing form {i}: {e}")
                    continue

        except Exception as e:
            self.logger.debug(f"Error discovering forms: {e}")

        return forms

    async def _analyze_element(self, element: Locator, selector: str) -> Optional[Dict[str, Any]]:
        """Analyze an interactive element to determine interaction strategy."""
        try:
            # Get element attributes
            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
            text_content = await element.text_content() or ""

            # Get relevant attributes
            attributes = {}
            for attr in ['id', 'class', 'data-testid', 'aria-label', 'title', 'href', 'onclick']:
                try:
                    value = await element.get_attribute(attr)
                    if value:
                        attributes[attr] = value
                except:
                    pass

            # Determine if this element might trigger API calls
            api_likelihood = self._calculate_api_likelihood(text_content, attributes)

            return {
                'selector': selector,
                'tag_name': tag_name,
                'text_content': text_content.strip()[:100],
                'attributes': attributes,
                'api_likelihood': api_likelihood,
                'element_locator': element
            }

        except Exception as e:
            self.logger.debug(f"Error analyzing element: {e}")
            return None

    async def _analyze_form(self, form: Locator) -> Optional[Dict[str, Any]]:
        """Analyze a form to determine interaction strategy."""
        try:
            # Get form attributes
            action = await form.get_attribute('action') or ""
            method = await form.get_attribute('method') or "GET"

            # Find form fields
            fields = []

            for field_selector in self.form_selectors[1:]:  # Skip 'form' itself
                try:
                    field_locators = form.locator(field_selector)
                    count = await field_locators.count()

                    for i in range(count):
                        field = field_locators.nth(i)

                        if await field.is_visible() and await field.is_enabled():
                            field_info = await self._analyze_form_field(field)
                            if field_info:
                                fields.append(field_info)

                except Exception as e:
                    self.logger.debug(f"Error analyzing form fields: {e}")
                    continue

            return {
                'action': action,
                'method': method.upper(),
                'fields': fields,
                'form_locator': form,
                'api_likelihood': self._calculate_form_api_likelihood(action, fields)
            }

        except Exception as e:
            self.logger.debug(f"Error analyzing form: {e}")
            return None

    async def _analyze_form_field(self, field: Locator) -> Optional[Dict[str, Any]]:
        """Analyze a form field."""
        try:
            tag_name = await field.evaluate('el => el.tagName.toLowerCase()')
            field_type = await field.get_attribute('type') or ""
            name = await field.get_attribute('name') or ""
            placeholder = await field.get_attribute('placeholder') or ""
            required = await field.get_attribute('required') is not None

            return {
                'tag_name': tag_name,
                'type': field_type,
                'name': name,
                'placeholder': placeholder,
                'required': required,
                'field_locator': field
            }

        except Exception as e:
            self.logger.debug(f"Error analyzing form field: {e}")
            return None

    def _calculate_api_likelihood(self, text: str, attributes: Dict[str, str]) -> float:
        """Calculate likelihood that interacting with this element will trigger API calls."""
        score = 0.0

        # Check text content for API-related keywords
        text_lower = text.lower()
        for pattern in self.api_patterns:
            if re.search(pattern, text_lower):
                score += 0.2

        # Check attributes for API-related patterns
        for attr_value in attributes.values():
            if attr_value:
                attr_lower = attr_value.lower()
                for pattern in self.api_patterns:
                    if re.search(pattern, attr_lower):
                        score += 0.1

        # Boost score for common interactive elements
        if any(keyword in text_lower for keyword in ['search', 'submit', 'send', 'create', 'update', 'delete', 'load', 'refresh']):
            score += 0.3

        # Boost score for buttons and links
        if attributes.get('href') or 'button' in text_lower:
            score += 0.1

        return min(score, 1.0)

    def _calculate_form_api_likelihood(self, action: str, fields: List[Dict[str, Any]]) -> float:
        """Calculate likelihood that submitting this form will trigger API calls."""
        score = 0.5  # Forms generally trigger API calls

        # Check action URL
        if action and any(pattern in action.lower() for pattern in self.api_patterns):
            score += 0.3

        # Check field types
        for field in fields:
            if field['type'] in ['email', 'password', 'search']:
                score += 0.1

        return min(score, 1.0)

    async def _should_interact_with_element(self, element_info: Dict[str, Any]) -> bool:
        """Determine if we should interact with this element."""
        # Skip if API likelihood is too low
        if element_info['api_likelihood'] < 0.2:
            return False

        # Skip if we've already interacted with similar elements
        element_signature = f"{element_info['tag_name']}:{element_info['text_content'][:50]}"
        if element_signature in self.visited_states:
            return False

        # Skip dangerous actions
        dangerous_keywords = ['delete', 'remove', 'logout', 'signout', 'exit', 'close']
        text_lower = element_info['text_content'].lower()
        if any(keyword in text_lower for keyword in dangerous_keywords):
            return False

        return True

    async def _interact_with_element(self, element_info: Dict[str, Any]) -> Dict[str, Any]:
        """Interact with an element and record the result."""
        try:
            element = element_info['element_locator']

            # Record interaction
            interaction = {
                'timestamp': datetime.utcnow(),
                'element_info': element_info,
                'action': 'click',
                'success': False
            }

            # Scroll element into view
            await element.scroll_into_view_if_needed()
            await asyncio.sleep(0.5)

            # Click the element
            await element.click(timeout=5000)

            # Wait for potential navigation or dynamic content
            await asyncio.sleep(2)

            # Mark as visited
            element_signature = f"{element_info['tag_name']}:{element_info['text_content'][:50]}"
            self.visited_states.add(element_signature)

            interaction['success'] = True
            self.interaction_history.append(interaction)

            self.logger.info(f"Successfully clicked element: {element_info['text_content'][:50]}")

            return interaction

        except Exception as e:
            interaction['error'] = str(e)
            self.logger.warning(f"Failed to interact with element: {e}")
            return interaction

    async def _interact_with_form(self, form_info: Dict[str, Any]) -> Dict[str, Any]:
        """Fill and submit a form."""
        try:
            interaction = {
                'timestamp': datetime.utcnow(),
                'form_info': form_info,
                'action': 'form_submit',
                'success': False
            }

            # Fill form fields with test data
            for field in form_info['fields']:
                try:
                    await self._fill_form_field(field)
                except Exception as e:
                    self.logger.debug(f"Failed to fill field {field['name']}: {e}")
                    continue

            # Submit form
            form = form_info['form_locator']

            # Look for submit button
            submit_button = form.locator('input[type="submit"], button[type="submit"], button:has-text("submit")')

            if await submit_button.count() > 0:
                await submit_button.first.click()
            else:
                # Try to submit form directly
                await form.evaluate('form => form.submit()')

            # Wait for submission
            await asyncio.sleep(3)

            interaction['success'] = True
            self.interaction_history.append(interaction)

            self.logger.info(f"Successfully submitted form with {len(form_info['fields'])} fields")

            return interaction

        except Exception as e:
            interaction['error'] = str(e)
            self.logger.warning(f"Failed to submit form: {e}")
            return interaction

    async def _fill_form_field(self, field_info: Dict[str, Any]) -> None:
        """Fill a form field with appropriate test data."""
        field = field_info['field_locator']
        field_type = field_info['type']
        name = field_info['name'].lower()

        # Generate appropriate test data
        test_value = ""

        if field_type == 'email' or 'email' in name:
            test_value = "<EMAIL>"
        elif field_type == 'password' or 'password' in name:
            test_value = "TestPassword123!"
        elif field_type == 'number' or 'amount' in name or 'price' in name:
            test_value = "100"
        elif field_type == 'search' or 'search' in name:
            test_value = "test"
        elif 'name' in name:
            test_value = "Test User"
        elif 'address' in name:
            test_value = "123 Test Street"
        elif 'phone' in name:
            test_value = "555-0123"
        else:
            test_value = "test"

        # Fill the field
        await field.fill(test_value)
        await asyncio.sleep(0.5)

    async def _capture_page_state(self) -> Dict[str, Any]:
        """Capture current page state for comparison."""
        try:
            return {
                'url': self.page.url,
                'title': await self.page.title(),
                'timestamp': datetime.utcnow(),
                'dom_hash': await self._calculate_dom_hash()
            }
        except Exception as e:
            self.logger.error(f"Error capturing page state: {e}")
            return {'error': str(e)}

    async def _calculate_dom_hash(self) -> str:
        """Calculate a hash of the DOM structure for change detection."""
        try:
            # Get simplified DOM structure
            dom_structure = await self.page.evaluate('''
                () => {
                    const getStructure = (element) => {
                        return {
                            tag: element.tagName,
                            children: Array.from(element.children).map(getStructure)
                        };
                    };
                    return JSON.stringify(getStructure(document.body));
                }
            ''')

            # Simple hash
            return str(hash(dom_structure))

        except Exception as e:
            self.logger.debug(f"Error calculating DOM hash: {e}")
            return "unknown"

    async def _count_network_requests(self) -> int:
        """Count network requests (placeholder - would integrate with NetworkInterceptor)."""
        # This would integrate with the NetworkInterceptor to get actual counts
        # For now, return a placeholder
        return len(self.interaction_history)

    def get_interaction_summary(self) -> Dict[str, Any]:
        """Get summary of all interactions performed."""
        return {
            'total_interactions': len(self.interaction_history),
            'successful_interactions': len([i for i in self.interaction_history if i['success']]),
            'unique_states_visited': len(self.visited_states),
            'interaction_history': self.interaction_history
        }
