#!/usr/bin/env python3
"""
Setup script for Playwright browsers.

Installs the necessary browser binaries for Playwright automation.
"""

import subprocess
import sys
from pathlib import Path


def install_playwright_browsers():
    """Install Playwright browser binaries."""
    print("🚀 Installing Playwright browsers...")
    
    try:
        # Install Playwright browsers
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Playwright browsers installed successfully")
            print(result.stdout)
        else:
            print("❌ Failed to install Playwright browsers")
            print(result.stderr)
            return False
        
        # Install system dependencies
        print("🔧 Installing system dependencies...")
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install-deps"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ System dependencies installed successfully")
        else:
            print("⚠️ Some system dependencies may not have installed correctly")
            print(result.stderr)
        
        return True
        
    except Exception as e:
        print(f"💥 Error installing Playwright: {e}")
        return False


def main():
    """Main setup function."""
    print("🔬 Cipher-Spy Playwright Setup")
    print("="*40)
    
    success = install_playwright_browsers()
    
    if success:
        print("\n🎉 Setup completed successfully!")
        print("\nYou can now run the crawler test:")
        print("python test_crawler.py")
        return 0
    else:
        print("\n❌ Setup failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
