"""
Cipher-Spy: AI-Driven Red Team Swarm

A locally deployed, AI-driven red team swarm designed for professional 
penetration testers. Empowers human red teamers with autonomous AI agents 
that collaboratively perform web reconnaissance, vulnerability analysis, 
and exploit planning in a controlled environment.

Key Features:
- Multi-agent orchestration with LangGraph
- Autonomous web crawling with Playwright
- Technology stack fingerprinting
- Graph-RAG exploit knowledge integration
- Human-in-the-loop exploit approval
- Local-first deployment with Docker

Author: Cipher-Spy Development Team
License: MIT
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Cipher-Spy Development Team"
__license__ = "MIT"

# Core imports for easy access
from .config.settings import Settings
from .core.exceptions import CipherSpyException

__all__ = [
    "__version__",
    "__author__", 
    "__license__",
    "Settings",
    "CipherSpyException"
]
