"""
Extension Bridge Service

Handles communication and data processing between the Chrome extension
and the Cipher-Spy backend system.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

from ..models.extension_models import ExtensionSession, CapturedRequest
from .traffic_processor import TrafficProcessor


logger = logging.getLogger(__name__)


@dataclass
class ExtensionBridge:
    """
    Bridge service for Chrome extension integration.
    
    Manages sessions, processes captured traffic, and coordinates
    with the main Cipher-Spy analysis pipeline.
    """
    
    # Active sessions
    sessions: Dict[str, ExtensionSession] = field(default_factory=dict)
    
    # Traffic processor
    traffic_processor: TrafficProcessor = field(default_factory=TrafficProcessor)
    
    # Configuration
    max_sessions: int = 100
    session_timeout: int = 3600  # 1 hour
    
    def __post_init__(self):
        """Initialize the bridge service."""
        logger.info("Extension bridge service initialized")
    
    async def create_session(self, session_id: str, metadata: Optional[Dict] = None) -> ExtensionSession:
        """
        Create a new extension session.
        
        Args:
            session_id: Unique session identifier
            metadata: Optional session metadata
            
        Returns:
            Created session
        """
        # Clean up old sessions if needed
        await self._cleanup_old_sessions()
        
        # Create new session
        session = ExtensionSession(
            session_id=session_id,
            start_time=datetime.now(),
            metadata=metadata or {}
        )
        
        self.sessions[session_id] = session
        logger.info(f"Created extension session: {session_id}")
        
        return session
    
    async def get_session(self, session_id: str) -> Optional[ExtensionSession]:
        """
        Get an existing session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session if found, None otherwise
        """
        return self.sessions.get(session_id)
    
    async def add_request(self, session_id: str, request_data: Dict[str, Any]) -> bool:
        """
        Add a captured request to a session.
        
        Args:
            session_id: Session identifier
            request_data: Request data from extension
            
        Returns:
            True if added successfully, False otherwise
        """
        session = await self.get_session(session_id)
        if not session:
            logger.warning(f"Session not found: {session_id}")
            return False
        
        try:
            # Create captured request object
            captured_request = CapturedRequest(
                request_id=request_data.get('id', ''),
                url=request_data.get('url', ''),
                method=request_data.get('method', 'GET'),
                headers=request_data.get('headers', {}),
                body=request_data.get('body'),
                timestamp=datetime.now(),
                response_data=request_data.get('response', {}),
                metadata=request_data.get('metadata', {})
            )
            
            session.requests.append(captured_request)
            session.last_activity = datetime.now()
            
            # Process request in background
            asyncio.create_task(self._process_request_async(session, captured_request))
            
            logger.debug(f"Added request to session {session_id}: {captured_request.url}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding request to session {session_id}: {e}")
            return False
    
    async def analyze_session(self, session_id: str) -> Dict[str, Any]:
        """
        Analyze all requests in a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Analysis results
        """
        session = await self.get_session(session_id)
        if not session:
            return {"error": "Session not found"}
        
        try:
            # Convert to format expected by traffic processor
            requests_data = [
                {
                    "url": req.url,
                    "method": req.method,
                    "headers": req.headers,
                    "body": req.body,
                    "timestamp": req.timestamp.isoformat(),
                    "response": req.response_data
                }
                for req in session.requests
            ]
            
            # Analyze with traffic processor
            analysis = await self.traffic_processor.analyze_session(requests_data)
            
            # Store results
            session.analysis_results['session_analysis'] = analysis
            session.last_activity = datetime.now()
            
            logger.info(f"Analyzed session {session_id}: {len(requests_data)} requests")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing session {session_id}: {e}")
            return {"error": str(e)}
    
    async def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """
        Get a summary of session data.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session summary
        """
        session = await self.get_session(session_id)
        if not session:
            return {"error": "Session not found"}
        
        # Calculate summary statistics
        api_requests = [
            req for req in session.requests 
            if self._is_api_request(req.url)
        ]
        
        unique_domains = set(
            self._extract_domain(req.url) 
            for req in session.requests
        )
        
        summary = {
            "session_id": session_id,
            "start_time": session.start_time.isoformat(),
            "duration": (datetime.now() - session.start_time).total_seconds(),
            "total_requests": len(session.requests),
            "api_requests": len(api_requests),
            "unique_domains": len(unique_domains),
            "domains": list(unique_domains),
            "has_analysis": bool(session.analysis_results),
            "metadata": session.metadata
        }
        
        return summary
    
    async def export_session(self, session_id: str, format_type: str = "json") -> Dict[str, Any]:
        """
        Export session data in specified format.
        
        Args:
            session_id: Session identifier
            format_type: Export format (json, har, openapi)
            
        Returns:
            Exported data
        """
        session = await self.get_session(session_id)
        if not session:
            return {"error": "Session not found"}
        
        try:
            if format_type == "json":
                return await self._export_json(session)
            elif format_type == "har":
                return await self._export_har(session)
            elif format_type == "openapi":
                return await self._export_openapi(session)
            else:
                return {"error": f"Unsupported format: {format_type}"}
                
        except Exception as e:
            logger.error(f"Error exporting session {session_id}: {e}")
            return {"error": str(e)}
    
    async def cleanup_session(self, session_id: str) -> bool:
        """
        Clean up a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if cleaned up successfully
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Cleaned up session: {session_id}")
            return True
        return False
    
    async def _process_request_async(self, session: ExtensionSession, request: CapturedRequest):
        """Process a request asynchronously."""
        try:
            # Basic processing - can be extended
            if self._is_api_request(request.url):
                session.metadata.setdefault('api_count', 0)
                session.metadata['api_count'] += 1
                
        except Exception as e:
            logger.error(f"Error processing request async: {e}")
    
    async def _cleanup_old_sessions(self):
        """Clean up old or expired sessions."""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session in self.sessions.items():
            age = (current_time - session.last_activity).total_seconds()
            if age > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            await self.cleanup_session(session_id)
        
        # Also limit total sessions
        if len(self.sessions) > self.max_sessions:
            # Remove oldest sessions
            sorted_sessions = sorted(
                self.sessions.items(),
                key=lambda x: x[1].last_activity
            )
            
            for session_id, _ in sorted_sessions[:len(self.sessions) - self.max_sessions]:
                await self.cleanup_session(session_id)
    
    def _is_api_request(self, url: str) -> bool:
        """Check if URL appears to be an API request."""
        api_indicators = ['/api/', '.json', '/graphql', '/rest/', '/v1/', '/v2/']
        return any(indicator in url.lower() for indicator in api_indicators)
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            from urllib.parse import urlparse
            return urlparse(url).netloc
        except:
            return "unknown"
    
    async def _export_json(self, session: ExtensionSession) -> Dict[str, Any]:
        """Export session as JSON."""
        return {
            "session_id": session.session_id,
            "start_time": session.start_time.isoformat(),
            "requests": [
                {
                    "id": req.request_id,
                    "url": req.url,
                    "method": req.method,
                    "headers": req.headers,
                    "body": req.body,
                    "timestamp": req.timestamp.isoformat(),
                    "response": req.response_data
                }
                for req in session.requests
            ],
            "analysis": session.analysis_results,
            "metadata": session.metadata
        }
    
    async def _export_har(self, session: ExtensionSession) -> Dict[str, Any]:
        """Export session as HAR format."""
        # Simplified HAR format
        return {
            "log": {
                "version": "1.2",
                "creator": {"name": "Cipher-Spy", "version": "1.0.0"},
                "entries": [
                    {
                        "startedDateTime": req.timestamp.isoformat(),
                        "request": {
                            "method": req.method,
                            "url": req.url,
                            "headers": [{"name": k, "value": v} for k, v in req.headers.items()],
                            "postData": {"text": req.body} if req.body else {}
                        },
                        "response": req.response_data
                    }
                    for req in session.requests
                ]
            }
        }
    
    async def _export_openapi(self, session: ExtensionSession) -> Dict[str, Any]:
        """Export session as OpenAPI specification."""
        # Basic OpenAPI structure
        api_requests = [req for req in session.requests if self._is_api_request(req.url)]
        
        paths = {}
        for req in api_requests:
            path = req.url.split('?')[0]  # Remove query params
            if path not in paths:
                paths[path] = {}
            
            method = req.method.lower()
            paths[path][method] = {
                "summary": f"{req.method} {path}",
                "responses": {
                    "200": {"description": "Success"}
                }
            }
        
        return {
            "openapi": "3.0.0",
            "info": {
                "title": f"Discovered API - Session {session.session_id}",
                "version": "1.0.0"
            },
            "paths": paths
        }


# Global instance
extension_bridge = ExtensionBridge()
