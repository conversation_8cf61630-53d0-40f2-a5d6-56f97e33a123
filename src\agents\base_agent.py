"""
Base agent class for Cipher-Spy agents.

Provides common functionality and interface for all agents in the red team swarm.
Handles logging, error handling, state management, and LangChain integration.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime

# Optional LangChain imports
try:
    from langchain.agents import AgentExecutor
    from langchain.tools import BaseTool
    from langchain.schema import BaseMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    AgentExecutor = None
    BaseTool = None
    BaseMessage = None
    LANGCHAIN_AVAILABLE = False

from ..core.state import ScanState, AgentStatus
from ..core.exceptions import CipherSpyException
from ..config.settings import get_settings


class BaseAgent(ABC):
    """
    Abstract base class for all Cipher-Spy agents.

    Provides common functionality including:
    - State management and updates
    - Logging and error handling
    - Tool management
    - Progress tracking
    - LangChain integration
    """

    def __init__(
        self,
        agent_id: str,
        agent_type: str,
        tools: Optional[List] = None,  # Made generic to avoid BaseTool dependency
        **kwargs
    ):
        """
        Initialize base agent.

        Args:
            agent_id: Unique identifier for this agent instance
            agent_type: Type/category of agent (e.g., "crawler", "fingerprinter")
            tools: List of tools available to this agent
            **kwargs: Additional configuration parameters
        """
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.tools = tools or []
        self.settings = get_settings()
        self.logger = logging.getLogger(f"{__name__}.{agent_type}")

        # Agent state
        self.status = AgentStatus.IDLE
        self.current_task: Optional[str] = None
        self.progress = 0.0
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.error_message: Optional[str] = None

        # Configuration
        self.config = kwargs

        # LangChain components (to be initialized by subclasses)
        self.llm = None
        self.agent_executor: Optional[AgentExecutor] = None

        self.logger.info(f"Initialized {agent_type} agent: {agent_id}")

    @abstractmethod
    async def execute(self, state: ScanState) -> ScanState:
        """
        Execute the agent's main functionality.

        Args:
            state: Current scan state

        Returns:
            ScanState: Updated scan state

        Raises:
            CipherSpyException: If execution fails
        """
        pass

    @abstractmethod
    def get_tools(self) -> List:
        """
        Get the tools available to this agent.

        Returns:
            List: Available tools (BaseTool if LangChain available)
        """
        pass

    def update_state(
        self,
        state: ScanState,
        status: Optional[AgentStatus] = None,
        task: Optional[str] = None,
        progress: Optional[float] = None,
        error: Optional[str] = None
    ) -> None:
        """
        Update agent status in the scan state.

        Args:
            state: Scan state to update
            status: New agent status
            task: Current task description
            progress: Progress percentage (0.0 to 1.0)
            error: Error message if any
        """
        try:
            state.update_agent_status(
                agent_id=self.agent_id,
                status=status or self.status,
                task=task or self.current_task,
                progress=progress if progress is not None else self.progress,
                error=error or self.error_message
            )

            # Update local state
            if status:
                self.status = status
            if task:
                self.current_task = task
            if progress is not None:
                self.progress = progress
            if error:
                self.error_message = error

        except Exception as e:
            self.logger.error(f"Failed to update agent state: {e}")

    async def start_execution(self, state: ScanState) -> None:
        """
        Start agent execution and update state.

        Args:
            state: Current scan state
        """
        self.started_at = datetime.utcnow()
        self.status = AgentStatus.RUNNING
        self.progress = 0.0
        self.error_message = None

        self.update_state(
            state,
            status=AgentStatus.RUNNING,
            task=f"Starting {self.agent_type} execution",
            progress=0.0
        )

        self.logger.info(f"Agent {self.agent_id} started execution")

    async def complete_execution(self, state: ScanState, success: bool = True) -> None:
        """
        Complete agent execution and update state.

        Args:
            state: Current scan state
            success: Whether execution was successful
        """
        self.completed_at = datetime.utcnow()
        self.status = AgentStatus.COMPLETED if success else AgentStatus.FAILED
        self.progress = 1.0 if success else self.progress

        self.update_state(
            state,
            status=self.status,
            task=f"{self.agent_type} execution {'completed' if success else 'failed'}",
            progress=self.progress
        )

        status_msg = "completed successfully" if success else "failed"
        self.logger.info(f"Agent {self.agent_id} {status_msg}")

    async def handle_error(self, state: ScanState, error: Exception) -> None:
        """
        Handle execution errors and update state.

        Args:
            state: Current scan state
            error: Exception that occurred
        """
        self.error_message = str(error)
        self.status = AgentStatus.FAILED

        self.update_state(
            state,
            status=AgentStatus.FAILED,
            error=self.error_message
        )

        self.logger.error(f"Agent {self.agent_id} failed: {error}", exc_info=True)

    def log_progress(self, message: str, progress: Optional[float] = None) -> None:
        """
        Log progress message.

        Args:
            message: Progress message
            progress: Progress percentage (0.0 to 1.0)
        """
        if progress is not None:
            self.progress = progress

        self.logger.info(f"[{self.agent_id}] {message} ({self.progress:.1%})")

    def validate_state(self, state: ScanState) -> None:
        """
        Validate that the state contains required information for this agent.

        Args:
            state: Scan state to validate

        Raises:
            CipherSpyException: If state is invalid
        """
        if not state.target:
            raise CipherSpyException(
                f"Agent {self.agent_id} requires target information",
                status_code=400
            )

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value with fallback to settings.

        Args:
            key: Configuration key
            default: Default value if not found

        Returns:
            Configuration value
        """
        # Check agent-specific config first
        if key in self.config:
            return self.config[key]

        # Check global settings
        if hasattr(self.settings, key):
            return getattr(self.settings, key)

        return default

    def should_continue(self, state: ScanState) -> bool:
        """
        Check if agent should continue execution.

        Args:
            state: Current scan state

        Returns:
            bool: True if should continue, False otherwise
        """
        return (
            state.should_continue and
            self.status not in [AgentStatus.FAILED, AgentStatus.COMPLETED] and
            not state.human_intervention_required
        )

    async def cleanup(self) -> None:
        """
        Cleanup agent resources.

        Override in subclasses to cleanup specific resources.
        """
        self.logger.info(f"Cleaning up agent {self.agent_id}")

    def __str__(self) -> str:
        return f"{self.agent_type}Agent({self.agent_id})"

    def __repr__(self) -> str:
        return (
            f"{self.__class__.__name__}("
            f"agent_id='{self.agent_id}', "
            f"agent_type='{self.agent_type}', "
            f"status={self.status}, "
            f"progress={self.progress:.1%}"
            f")"
        )
