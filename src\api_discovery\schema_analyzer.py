#!/usr/bin/env python3
"""
Schema Analyzer for Cipher-Spy Universal API Discovery

Advanced response schema analysis system that infers data structures,
relationships, and patterns from API responses to generate comprehensive
schema documentation and type definitions.
"""

import json
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict, Counter


@dataclass
class FieldAnalysis:
    """Analysis result for a single field."""
    field_path: str
    data_type: str
    nullable: bool
    occurrence_frequency: float
    example_values: List[Any]
    value_patterns: List[str]
    constraints: Dict[str, Any]
    relationships: List[str]
    business_category: str


@dataclass
class SchemaAnalysis:
    """Complete schema analysis result."""
    endpoint_name: str
    sample_count: int
    confidence_score: float
    fields: Dict[str, FieldAnalysis]
    data_structure: Dict[str, Any]
    relationships: Dict[str, List[str]]
    business_entities: List[str]
    key_metrics: List[str]
    patterns: Dict[str, Any]
    timestamp: str


class SchemaAnalyzer:
    """
    Advanced schema analyzer for API response data.
    
    This analyzer performs deep analysis of API response structures to:
    - Infer data types and constraints
    - Identify relationships between fields
    - Categorize business entities and metrics
    - Detect patterns and anomalies
    - Generate comprehensive schema documentation
    """

    def __init__(self):
        """Initialize the schema analyzer."""
        
        # Business field categorization patterns
        self.business_patterns = {
            'identifiers': [
                r'.*id$', r'.*_id$', r'.*Id$', r'.*ID$',
                r'^id$', r'^uuid$', r'^guid$', r'.*key$'
            ],
            'names': [
                r'.*name$', r'.*Name$', r'.*title$', r'.*Title$',
                r'.*label$', r'.*Label$'
            ],
            'descriptions': [
                r'.*description$', r'.*Description$', r'.*desc$',
                r'.*summary$', r'.*Summary$', r'.*content$'
            ],
            'timestamps': [
                r'.*time$', r'.*Time$', r'.*date$', r'.*Date$',
                r'.*created.*', r'.*updated.*', r'.*modified.*',
                r'.*timestamp$', r'.*Timestamp$'
            ],
            'metrics': [
                r'.*count$', r'.*Count$', r'.*total$', r'.*Total$',
                r'.*sum$', r'.*Sum$', r'.*average$', r'.*avg$',
                r'.*rate$', r'.*Rate$', r'.*percentage$', r'.*percent$',
                r'.*score$', r'.*Score$', r'.*rating$', r'.*Rating$'
            ],
            'financial': [
                r'.*price$', r'.*Price$', r'.*cost$', r'.*Cost$',
                r'.*amount$', r'.*Amount$', r'.*value$', r'.*Value$',
                r'.*fee$', r'.*Fee$', r'.*revenue$', r'.*profit$'
            ],
            'status': [
                r'.*status$', r'.*Status$', r'.*state$', r'.*State$',
                r'.*active$', r'.*enabled$', r'.*visible$'
            ],
            'urls': [
                r'.*url$', r'.*Url$', r'.*URL$', r'.*link$',
                r'.*href$', r'.*uri$', r'.*URI$'
            ]
        }
        
        # Data type inference patterns
        self.type_patterns = {
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'url': r'^https?://[^\s/$.?#].[^\s]*$',
            'uuid': r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
            'iso_datetime': r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',
            'date': r'^\d{4}-\d{2}-\d{2}$',
            'phone': r'^[\+]?[1-9][\d]{0,15}$',
            'ip_address': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$',
            'hex_color': r'^#[0-9a-fA-F]{6}$',
            'base64': r'^[A-Za-z0-9+/]*={0,2}$'
        }

    def analyze_responses(self, responses: List[Dict[str, Any]], endpoint_name: str) -> SchemaAnalysis:
        """
        Analyze multiple API responses to infer comprehensive schema.
        
        Args:
            responses: List of API response data
            endpoint_name: Name of the endpoint
            
        Returns:
            SchemaAnalysis: Comprehensive schema analysis
        """
        if not responses:
            return self._create_empty_analysis(endpoint_name)
        
        print(f"   🔍 Analyzing {len(responses)} response samples...")
        
        # Extract all fields from all responses
        all_fields = {}
        for i, response in enumerate(responses):
            self._extract_fields_recursive(response, all_fields, sample_index=i)
        
        # Analyze each field
        field_analyses = {}
        for field_path, field_data in all_fields.items():
            analysis = self._analyze_field(field_path, field_data, len(responses))
            field_analyses[field_path] = analysis
        
        # Perform structural analysis
        data_structure = self._analyze_data_structure(responses)
        relationships = self._identify_relationships(field_analyses)
        business_entities = self._identify_business_entities(field_analyses)
        key_metrics = self._identify_key_metrics(field_analyses)
        patterns = self._identify_patterns(responses, field_analyses)
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(field_analyses, len(responses))
        
        return SchemaAnalysis(
            endpoint_name=endpoint_name,
            sample_count=len(responses),
            confidence_score=confidence_score,
            fields=field_analyses,
            data_structure=data_structure,
            relationships=relationships,
            business_entities=business_entities,
            key_metrics=key_metrics,
            patterns=patterns,
            timestamp=datetime.now().isoformat()
        )

    def _extract_fields_recursive(self, data: Any, fields_dict: Dict, path: str = "", sample_index: int = 0, max_depth: int = 10):
        """Recursively extract all fields from response data."""
        if max_depth <= 0:
            return
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # Initialize field data if not exists
                if current_path not in fields_dict:
                    fields_dict[current_path] = {
                        'values': [],
                        'types': [],
                        'null_count': 0,
                        'sample_indices': [],
                        'nested_structure': None
                    }
                
                # Record value and metadata
                fields_dict[current_path]['sample_indices'].append(sample_index)
                
                if value is None:
                    fields_dict[current_path]['null_count'] += 1
                    fields_dict[current_path]['types'].append('null')
                else:
                    fields_dict[current_path]['values'].append(value)
                    fields_dict[current_path]['types'].append(type(value).__name__)
                    
                    # Store structure for complex types
                    if isinstance(value, dict):
                        fields_dict[current_path]['nested_structure'] = 'object'
                    elif isinstance(value, list):
                        fields_dict[current_path]['nested_structure'] = 'array'
                
                # Recurse into nested structures
                if isinstance(value, (dict, list)):
                    self._extract_fields_recursive(value, fields_dict, current_path, sample_index, max_depth - 1)
        
        elif isinstance(data, list):
            # Analyze array elements
            for i, item in enumerate(data[:5]):  # Analyze first 5 items
                array_path = f"{path}[{i}]" if path else f"[{i}]"
                self._extract_fields_recursive(item, fields_dict, array_path, sample_index, max_depth - 1)

    def _analyze_field(self, field_path: str, field_data: Dict, total_samples: int) -> FieldAnalysis:
        """Analyze a single field to determine its characteristics."""
        
        # Calculate occurrence frequency
        occurrence_frequency = len(field_data['sample_indices']) / total_samples
        
        # Determine primary data type
        type_counter = Counter(field_data['types'])
        primary_type = type_counter.most_common(1)[0][0] if type_counter else 'unknown'
        
        # Check if nullable
        nullable = field_data['null_count'] > 0
        
        # Get example values (limit to avoid memory issues)
        example_values = field_data['values'][:10] if field_data['values'] else []
        
        # Identify value patterns
        value_patterns = self._identify_value_patterns(field_data['values'])
        
        # Infer constraints
        constraints = self._infer_constraints(field_data['values'], primary_type)
        
        # Identify relationships
        relationships = self._identify_field_relationships(field_path, field_data)
        
        # Categorize business purpose
        business_category = self._categorize_business_field(field_path, field_data['values'])
        
        return FieldAnalysis(
            field_path=field_path,
            data_type=primary_type,
            nullable=nullable,
            occurrence_frequency=occurrence_frequency,
            example_values=example_values,
            value_patterns=value_patterns,
            constraints=constraints,
            relationships=relationships,
            business_category=business_category
        )

    def _identify_value_patterns(self, values: List[Any]) -> List[str]:
        """Identify patterns in field values."""
        patterns = []
        
        if not values:
            return patterns
        
        # Check string patterns
        string_values = [str(v) for v in values if isinstance(v, str)]
        if string_values:
            for pattern_name, pattern_regex in self.type_patterns.items():
                if any(re.match(pattern_regex, v, re.IGNORECASE) for v in string_values):
                    patterns.append(pattern_name)
        
        # Check numeric patterns
        numeric_values = [v for v in values if isinstance(v, (int, float))]
        if numeric_values:
            if all(isinstance(v, int) for v in numeric_values):
                patterns.append('integer')
            if all(v >= 0 for v in numeric_values):
                patterns.append('non_negative')
            if all(0 <= v <= 1 for v in numeric_values if isinstance(v, (int, float))):
                patterns.append('percentage_like')
        
        # Check boolean patterns
        if all(isinstance(v, bool) for v in values):
            patterns.append('boolean')
        
        return patterns

    def _infer_constraints(self, values: List[Any], data_type: str) -> Dict[str, Any]:
        """Infer constraints from field values."""
        constraints = {}
        
        if not values:
            return constraints
        
        if data_type in ['int', 'float', 'number']:
            numeric_values = [v for v in values if isinstance(v, (int, float))]
            if numeric_values:
                constraints['min_value'] = min(numeric_values)
                constraints['max_value'] = max(numeric_values)
                constraints['average'] = sum(numeric_values) / len(numeric_values)
        
        elif data_type == 'str':
            string_values = [v for v in values if isinstance(v, str)]
            if string_values:
                lengths = [len(v) for v in string_values]
                constraints['min_length'] = min(lengths)
                constraints['max_length'] = max(lengths)
                constraints['average_length'] = sum(lengths) / len(lengths)
                
                # Check for enum-like behavior
                unique_values = set(string_values)
                if len(unique_values) <= 10 and len(string_values) > len(unique_values):
                    constraints['possible_enum'] = True
                    constraints['enum_values'] = list(unique_values)
        
        elif data_type == 'list':
            list_values = [v for v in values if isinstance(v, list)]
            if list_values:
                lengths = [len(v) for v in list_values]
                constraints['min_items'] = min(lengths)
                constraints['max_items'] = max(lengths)
                constraints['average_items'] = sum(lengths) / len(lengths)
        
        return constraints

    def _identify_field_relationships(self, field_path: str, field_data: Dict) -> List[str]:
        """Identify relationships this field might have with other fields."""
        relationships = []
        
        field_lower = field_path.lower()
        
        # Foreign key relationships
        if field_lower.endswith('_id') or field_lower.endswith('id'):
            relationships.append('foreign_key')
        
        # Parent-child relationships
        if '.' in field_path:
            relationships.append('nested_field')
            parent_path = '.'.join(field_path.split('.')[:-1])
            relationships.append(f'child_of:{parent_path}')
        
        # Array relationships
        if '[' in field_path:
            relationships.append('array_element')
        
        return relationships

    def _categorize_business_field(self, field_path: str, values: List[Any]) -> str:
        """Categorize field based on business purpose."""
        field_lower = field_path.lower()
        
        # Check against business patterns
        for category, patterns in self.business_patterns.items():
            for pattern in patterns:
                if re.match(pattern, field_lower):
                    return category
        
        # Check value-based categorization
        if values:
            sample_values = [str(v) for v in values[:5] if v is not None]
            
            # Check for URLs
            if any(v.startswith(('http://', 'https://')) for v in sample_values if isinstance(v, str)):
                return 'urls'
            
            # Check for email patterns
            if any('@' in str(v) and '.' in str(v) for v in sample_values):
                return 'emails'
        
        return 'general'

    def _analyze_data_structure(self, responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze overall data structure patterns."""
        structure = {
            'response_type': 'unknown',
            'consistent_structure': True,
            'root_fields': [],
            'nested_levels': 0,
            'array_fields': [],
            'pagination_indicators': []
        }
        
        if not responses:
            return structure
        
        # Determine response type
        first_response = responses[0]
        if isinstance(first_response, list):
            structure['response_type'] = 'array'
        elif isinstance(first_response, dict):
            structure['response_type'] = 'object'
            structure['root_fields'] = list(first_response.keys())
        
        # Check structure consistency
        if len(responses) > 1:
            first_keys = set(first_response.keys()) if isinstance(first_response, dict) else set()
            for response in responses[1:]:
                if isinstance(response, dict):
                    if set(response.keys()) != first_keys:
                        structure['consistent_structure'] = False
                        break
        
        # Identify pagination indicators
        pagination_fields = ['page', 'limit', 'offset', 'total', 'count', 'next', 'prev', 'has_more']
        if isinstance(first_response, dict):
            for field in pagination_fields:
                if field in first_response:
                    structure['pagination_indicators'].append(field)
        
        # Calculate nesting depth
        structure['nested_levels'] = self._calculate_max_depth(first_response)
        
        return structure

    def _calculate_max_depth(self, data: Any, current_depth: int = 0) -> int:
        """Calculate maximum nesting depth of data structure."""
        if not isinstance(data, (dict, list)):
            return current_depth
        
        max_depth = current_depth
        
        if isinstance(data, dict):
            for value in data.values():
                depth = self._calculate_max_depth(value, current_depth + 1)
                max_depth = max(max_depth, depth)
        elif isinstance(data, list) and data:
            for item in data[:3]:  # Check first 3 items
                depth = self._calculate_max_depth(item, current_depth + 1)
                max_depth = max(max_depth, depth)
        
        return max_depth

    def _identify_relationships(self, field_analyses: Dict[str, FieldAnalysis]) -> Dict[str, List[str]]:
        """Identify relationships between fields."""
        relationships = defaultdict(list)
        
        # Group fields by potential relationships
        id_fields = []
        name_fields = []
        
        for field_path, analysis in field_analyses.items():
            if analysis.business_category == 'identifiers':
                id_fields.append(field_path)
            elif analysis.business_category == 'names':
                name_fields.append(field_path)
        
        # Create relationships
        for id_field in id_fields:
            for name_field in name_fields:
                if id_field.replace('_id', '') in name_field or id_field.replace('id', '') in name_field:
                    relationships[id_field].append(f'related_to:{name_field}')
                    relationships[name_field].append(f'related_to:{id_field}')
        
        return dict(relationships)

    def _identify_business_entities(self, field_analyses: Dict[str, FieldAnalysis]) -> List[str]:
        """Identify business entities from field analysis."""
        entities = set()
        
        for field_path, analysis in field_analyses.items():
            # Extract entity names from field paths
            path_parts = field_path.replace('_', ' ').replace('.', ' ').split()
            
            for part in path_parts:
                if len(part) > 2 and part.lower() not in ['id', 'name', 'type', 'data']:
                    entities.add(part.lower())
        
        # Common business entities
        common_entities = ['user', 'product', 'order', 'customer', 'item', 'category', 'post', 'comment']
        found_entities = [entity for entity in common_entities if any(entity in field.lower() for field in field_analyses.keys())]
        
        entities.update(found_entities)
        
        return list(entities)[:10]  # Return top 10

    def _identify_key_metrics(self, field_analyses: Dict[str, FieldAnalysis]) -> List[str]:
        """Identify key business metrics from field analysis."""
        metrics = []
        
        for field_path, analysis in field_analyses.items():
            if analysis.business_category in ['metrics', 'financial']:
                metrics.append(field_path)
            elif analysis.data_type in ['int', 'float'] and 'count' in field_path.lower():
                metrics.append(field_path)
        
        return metrics

    def _identify_patterns(self, responses: List[Dict[str, Any]], field_analyses: Dict[str, FieldAnalysis]) -> Dict[str, Any]:
        """Identify data patterns and anomalies."""
        patterns = {
            'data_consistency': {},
            'value_distributions': {},
            'temporal_patterns': {},
            'anomalies': []
        }
        
        # Analyze data consistency
        consistent_fields = sum(1 for analysis in field_analyses.values() if analysis.occurrence_frequency > 0.9)
        total_fields = len(field_analyses)
        patterns['data_consistency']['consistency_ratio'] = consistent_fields / total_fields if total_fields > 0 else 0
        
        # Analyze value distributions
        for field_path, analysis in field_analyses.items():
            if analysis.data_type in ['int', 'float'] and analysis.example_values:
                numeric_values = [v for v in analysis.example_values if isinstance(v, (int, float))]
                if numeric_values:
                    patterns['value_distributions'][field_path] = {
                        'min': min(numeric_values),
                        'max': max(numeric_values),
                        'range': max(numeric_values) - min(numeric_values),
                        'unique_count': len(set(numeric_values))
                    }
        
        # Identify anomalies
        for field_path, analysis in field_analyses.items():
            if analysis.occurrence_frequency < 0.1:
                patterns['anomalies'].append(f"Rare field: {field_path} (appears in {analysis.occurrence_frequency:.1%} of samples)")
        
        return patterns

    def _calculate_confidence_score(self, field_analyses: Dict[str, FieldAnalysis], sample_count: int) -> float:
        """Calculate overall confidence score for the schema analysis."""
        if not field_analyses:
            return 0.0
        
        # Factors that increase confidence:
        # 1. More samples
        # 2. Consistent field occurrence
        # 3. Clear data types
        # 4. Identifiable patterns
        
        sample_factor = min(1.0, sample_count / 10)  # Max confidence at 10+ samples
        
        consistency_scores = [analysis.occurrence_frequency for analysis in field_analyses.values()]
        consistency_factor = sum(consistency_scores) / len(consistency_scores)
        
        pattern_factor = sum(1 for analysis in field_analyses.values() if analysis.value_patterns) / len(field_analyses)
        
        confidence = (sample_factor * 0.3 + consistency_factor * 0.5 + pattern_factor * 0.2)
        
        return min(1.0, confidence)

    def _create_empty_analysis(self, endpoint_name: str) -> SchemaAnalysis:
        """Create empty analysis for endpoints with no responses."""
        return SchemaAnalysis(
            endpoint_name=endpoint_name,
            sample_count=0,
            confidence_score=0.0,
            fields={},
            data_structure={},
            relationships={},
            business_entities=[],
            key_metrics=[],
            patterns={},
            timestamp=datetime.now().isoformat()
        )
