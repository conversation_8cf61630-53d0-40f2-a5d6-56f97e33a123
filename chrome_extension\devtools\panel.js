/**
 * Cipher-Spy DevTools Panel Script
 * 
 * Handles the DevTools panel interface for advanced network analysis.
 */

// Global state
let capturedRequests = [];
let analysisData = {};
let discoveryData = {};
let currentTab = 'requests';

// DOM elements
const elements = {
  // Tab buttons
  tabButtons: document.querySelectorAll('.tab-button'),
  tabPanes: document.querySelectorAll('.tab-pane'),
  
  // Controls
  clearData: document.getElementById('clearData'),
  exportData: document.getElementById('exportData'),
  
  // Filters
  urlFilter: document.getElementById('urlFilter'),
  methodFilter: document.getElementById('methodFilter'),
  apiOnly: document.getElementById('apiOnly'),
  
  // Requests
  requestsList: document.getElementById('requestsList'),
  
  // Analysis
  totalRequests: document.getElementById('totalRequests'),
  apiRequests: document.getElementById('apiRequests'),
  uniqueDomains: document.getElementById('uniqueDomains'),
  errorRate: document.getElementById('errorRate'),
  technologiesList: document.getElementById('technologiesList'),
  patternsList: document.getElementById('patternsList'),
  
  // Discovery
  triggerDiscovery: document.getElementById('triggerDiscovery'),
  refreshDiscovery: document.getElementById('refreshDiscovery'),
  discoveryProgress: document.querySelector('.progress-fill'),
  discoveryStatus: document.getElementById('discoveryStatus'),
  discoveredEndpoints: document.getElementById('discoveredEndpoints'),
  
  // Documentation
  generateDocs: document.getElementById('generateDocs'),
  docsFormat: document.getElementById('docsFormat'),
  docsContent: document.getElementById('docsContent')
};

// Initialize panel
function initializePanel() {
  console.log('Initializing Cipher-Spy DevTools panel');
  
  setupEventListeners();
  setupTabSwitching();
  loadInitialData();
  startPeriodicUpdates();
}

// Event listeners
function setupEventListeners() {
  // Controls
  elements.clearData.addEventListener('click', clearAllData);
  elements.exportData.addEventListener('click', exportData);
  
  // Filters
  elements.urlFilter.addEventListener('input', applyFilters);
  elements.methodFilter.addEventListener('change', applyFilters);
  elements.apiOnly.addEventListener('change', applyFilters);
  
  // Discovery
  elements.triggerDiscovery.addEventListener('click', triggerDiscovery);
  elements.refreshDiscovery.addEventListener('click', refreshDiscovery);
  
  // Documentation
  elements.generateDocs.addEventListener('click', generateDocumentation);
}

// Tab switching
function setupTabSwitching() {
  elements.tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.dataset.tab;
      switchTab(tabName);
    });
  });
}

function switchTab(tabName) {
  // Update buttons
  elements.tabButtons.forEach(btn => {
    btn.classList.toggle('active', btn.dataset.tab === tabName);
  });
  
  // Update panes
  elements.tabPanes.forEach(pane => {
    pane.classList.toggle('active', pane.id === `${tabName}-tab`);
  });
  
  currentTab = tabName;
  
  // Load tab-specific data
  switch (tabName) {
    case 'requests':
      updateRequestsList();
      break;
    case 'analysis':
      updateAnalysisView();
      break;
    case 'discovery':
      updateDiscoveryView();
      break;
    case 'docs':
      updateDocsView();
      break;
  }
}

// Data loading
async function loadInitialData() {
  try {
    // Load captured requests from background script
    const response = await chrome.runtime.sendMessage({ action: 'getRequests' });
    if (response && response.requests) {
      capturedRequests = response.requests;
      updateAllViews();
    }
  } catch (error) {
    console.error('Error loading initial data:', error);
  }
}

// Periodic updates
function startPeriodicUpdates() {
  setInterval(async () => {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getRequests' });
      if (response && response.requests) {
        capturedRequests = response.requests;
        updateCurrentView();
      }
    } catch (error) {
      // Ignore errors during periodic updates
    }
  }, 2000);
}

// Update views
function updateAllViews() {
  updateRequestsList();
  updateAnalysisView();
  updateDiscoveryView();
}

function updateCurrentView() {
  switch (currentTab) {
    case 'requests':
      updateRequestsList();
      break;
    case 'analysis':
      updateAnalysisView();
      break;
    case 'discovery':
      updateDiscoveryView();
      break;
  }
}

function updateRequestsList() {
  const filteredRequests = applyRequestFilters(capturedRequests);
  
  if (filteredRequests.length === 0) {
    elements.requestsList.innerHTML = '<div class="empty-state">No requests match the current filters.</div>';
    return;
  }
  
  const requestsHtml = filteredRequests.map(request => {
    const url = new URL(request.url);
    const path = url.pathname + url.search;
    const time = new Date(request.timestamp).toLocaleTimeString();
    const status = request.response ? request.response.statusCode : 'Pending';
    const statusClass = getStatusClass(status);
    
    return `
      <div class="request-row" data-request-id="${request.id}">
        <div class="col-method">
          <span class="method-badge method-${request.method}">${request.method}</span>
        </div>
        <div class="col-url" title="${request.url}">${path}</div>
        <div class="col-status">
          <span class="status-badge ${statusClass}">${status}</span>
        </div>
        <div class="col-type">${request.type}</div>
        <div class="col-time">${time}</div>
      </div>
    `;
  }).join('');
  
  elements.requestsList.innerHTML = requestsHtml;
  
  // Add click handlers
  elements.requestsList.querySelectorAll('.request-row').forEach(row => {
    row.addEventListener('click', () => {
      const requestId = row.dataset.requestId;
      showRequestDetails(requestId);
    });
  });
}

function updateAnalysisView() {
  // Update statistics
  const apiRequests = capturedRequests.filter(isApiRequest);
  const uniqueDomains = new Set(capturedRequests.map(req => new URL(req.url).hostname));
  const errorRequests = capturedRequests.filter(req => 
    req.response && req.response.statusCode >= 400
  );
  
  elements.totalRequests.textContent = capturedRequests.length;
  elements.apiRequests.textContent = apiRequests.length;
  elements.uniqueDomains.textContent = uniqueDomains.size;
  elements.errorRate.textContent = capturedRequests.length > 0 
    ? `${(errorRequests.length / capturedRequests.length * 100).toFixed(1)}%`
    : '0%';
  
  // Update technologies
  const technologies = detectTechnologies(capturedRequests);
  if (technologies.length > 0) {
    elements.technologiesList.innerHTML = technologies.map(tech => 
      `<span class="tech-item">${tech}</span>`
    ).join('');
  } else {
    elements.technologiesList.innerHTML = '<div class="empty-state">No technologies detected yet.</div>';
  }
  
  // Update patterns
  const patterns = detectPatterns(capturedRequests);
  if (patterns.length > 0) {
    elements.patternsList.innerHTML = patterns.map(pattern => 
      `<span class="pattern-item">${pattern}</span>`
    ).join('');
  } else {
    elements.patternsList.innerHTML = '<div class="empty-state">No patterns detected yet.</div>';
  }
}

function updateDiscoveryView() {
  // Update discovered endpoints
  if (discoveryData.endpoints && discoveryData.endpoints.length > 0) {
    const endpointsHtml = discoveryData.endpoints.map(endpoint => `
      <div class="endpoint-item">
        <div class="endpoint-url">${endpoint.url}</div>
        <div class="endpoint-details">
          ${endpoint.method} • ${endpoint.parameters?.length || 0} parameters
        </div>
      </div>
    `).join('');
    
    elements.discoveredEndpoints.innerHTML = endpointsHtml;
  } else {
    elements.discoveredEndpoints.innerHTML = '<div class="empty-state">No APIs discovered yet. Click "Start API Discovery" to begin.</div>';
  }
}

function updateDocsView() {
  // Update documentation content based on current format
  const format = elements.docsFormat.value;
  
  if (discoveryData.documentation && discoveryData.documentation[format]) {
    elements.docsContent.innerHTML = `<pre>${discoveryData.documentation[format]}</pre>`;
  } else {
    elements.docsContent.innerHTML = '<div class="empty-state">No documentation generated yet. Discover APIs first, then generate documentation.</div>';
  }
}

// Filtering
function applyRequestFilters(requests) {
  let filtered = requests;
  
  // URL filter
  const urlFilter = elements.urlFilter.value.toLowerCase();
  if (urlFilter) {
    filtered = filtered.filter(req => req.url.toLowerCase().includes(urlFilter));
  }
  
  // Method filter
  const methodFilter = elements.methodFilter.value;
  if (methodFilter) {
    filtered = filtered.filter(req => req.method === methodFilter);
  }
  
  // API only filter
  if (elements.apiOnly.checked) {
    filtered = filtered.filter(isApiRequest);
  }
  
  return filtered;
}

function applyFilters() {
  updateRequestsList();
}

// Utility functions
function isApiRequest(request) {
  const url = request.url.toLowerCase();
  const apiPatterns = ['/api/', '.json', '/graphql', '/rest/'];
  return apiPatterns.some(pattern => url.includes(pattern));
}

function getStatusClass(status) {
  if (typeof status !== 'number') return '';
  
  if (status >= 200 && status < 300) return 'status-2xx';
  if (status >= 300 && status < 400) return 'status-3xx';
  if (status >= 400 && status < 500) return 'status-4xx';
  if (status >= 500) return 'status-5xx';
  return '';
}

function detectTechnologies(requests) {
  const technologies = new Set();
  
  const techPatterns = {
    'React': [/react/i, /_react/i],
    'Vue': [/vue\.js/i, /vuejs/i],
    'Angular': [/angular/i, /ng-/i],
    'jQuery': [/jquery/i],
    'GraphQL': [/graphql/i],
    'REST API': [/\/api\//i, /\/rest\//i],
    'WebSocket': [/ws:\/\//i, /wss:\/\//i]
  };
  
  requests.forEach(request => {
    const url = request.url;
    
    Object.entries(techPatterns).forEach(([tech, patterns]) => {
      if (patterns.some(pattern => pattern.test(url))) {
        technologies.add(tech);
      }
    });
  });
  
  return Array.from(technologies);
}

function detectPatterns(requests) {
  const patterns = new Set();
  
  // API versioning
  if (requests.some(req => /\/v\d+\//.test(req.url))) {
    patterns.add('API Versioning');
  }
  
  // RESTful patterns
  const methods = new Set(requests.map(req => req.method));
  if (methods.size > 2) {
    patterns.add('RESTful API');
  }
  
  // Pagination patterns
  if (requests.some(req => /[?&](page|limit|offset)=/.test(req.url))) {
    patterns.add('Pagination');
  }
  
  // Authentication patterns
  if (requests.some(req => /[?&](token|key|auth)=/.test(req.url))) {
    patterns.add('Authentication');
  }
  
  return Array.from(patterns);
}

// Actions
async function clearAllData() {
  try {
    await chrome.runtime.sendMessage({ action: 'clearRequests' });
    capturedRequests = [];
    analysisData = {};
    discoveryData = {};
    updateAllViews();
  } catch (error) {
    console.error('Error clearing data:', error);
  }
}

async function exportData() {
  try {
    const response = await chrome.runtime.sendMessage({ action: 'exportRequests' });
    if (response && response.data) {
      const blob = new Blob([JSON.stringify(response.data, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `cipher-spy-devtools-export-${new Date().toISOString().slice(0, 19)}.json`;
      a.click();
      
      URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error('Error exporting data:', error);
  }
}

async function triggerDiscovery() {
  try {
    elements.discoveryStatus.textContent = 'Starting API discovery...';
    elements.discoveryProgress.style.width = '10%';
    
    const response = await chrome.runtime.sendMessage({ action: 'analyzeRequests' });
    
    elements.discoveryProgress.style.width = '50%';
    elements.discoveryStatus.textContent = 'Analyzing captured requests...';
    
    // Simulate progress
    setTimeout(() => {
      elements.discoveryProgress.style.width = '100%';
      elements.discoveryStatus.textContent = 'Discovery completed!';
      
      // Reset after a delay
      setTimeout(() => {
        elements.discoveryProgress.style.width = '0%';
        elements.discoveryStatus.textContent = 'Ready to start discovery';
      }, 3000);
    }, 2000);
    
  } catch (error) {
    console.error('Error triggering discovery:', error);
    elements.discoveryStatus.textContent = 'Discovery failed';
  }
}

async function refreshDiscovery() {
  updateDiscoveryView();
}

async function generateDocumentation() {
  try {
    elements.docsContent.innerHTML = '<div class="empty-state">Generating documentation...</div>';
    
    const mockDocs = {
      openapi: `openapi: 3.0.0
info:
  title: Discovered API
  version: 1.0.0
paths:
  /api/example:
    get:
      summary: Example endpoint`,
      markdown: `# Discovered API Documentation

## Endpoints

### GET /api/example
Example endpoint discovered through traffic analysis.`,
      html: `<h1>Discovered API Documentation</h1>
<h2>Endpoints</h2>
<h3>GET /api/example</h3>
<p>Example endpoint discovered through traffic analysis.</p>`
    };
    
    discoveryData.documentation = mockDocs;
    updateDocsView();
    
  } catch (error) {
    console.error('Error generating documentation:', error);
  }
}

function showRequestDetails(requestId) {
  const request = capturedRequests.find(req => req.id === requestId);
  if (!request) return;
  
  console.log('Request details:', request);
}

// Make initializePanel available globally
window.initializePanel = initializePanel;

// Initialize when script loads
document.addEventListener('DOMContentLoaded', initializePanel);
