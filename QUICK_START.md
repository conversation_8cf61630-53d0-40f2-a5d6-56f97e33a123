# Cipher-Spy Crawler Quick Start Guide

Get the Autonomous Web Reconnaissance Agent running in minutes!

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
# Install Python packages
pip install -r requirements.txt

# Install Playwright browsers
python setup_playwright.py
```

### 2. Test Installation
```bash
# Test Playwright is working
python minimal_test.py

# If that works, try a quick crawl
python quick_crawl.py
```

### 3. Run Full Demo (if basic tests pass)
```bash
# Interactive demonstration on pump.fun
python demo_crawler.py

# Or use the interactive menu
python run.py
```

## 🎯 What You'll See

The crawler will autonomously:

1. **Navigate pump.fun** - Intelligently explore the website
2. **Interact with Elements** - Click buttons, fill forms, handle modals
3. **Capture API Traffic** - Record all HTTP requests and responses
4. **Generate Documentation** - Create comprehensive API docs
5. **Produce Reports** - Detailed JSON reports and cURL commands

## 📊 Expected Output

```
🎯 Cipher-Spy Autonomous Web Reconnaissance Demo
============================================================
Target: https://pump.fun
Objective: Discover and document all accessible API endpoints
============================================================

🚀 Starting autonomous reconnaissance...

📊 RECONNAISSANCE RESULTS
============================================================
📄 Pages Discovered: 15
🔗 API Endpoints Found: 8
⏱️  Scan Duration: 45.32 seconds

📄 PAGE INVENTORY (15 pages)
----------------------------------------
 1. https://pump.fun/
    📝 Title: pump.fun
    📊 Status: 200
    📸 Screenshot: data/screenshots/pump.fun_2024...

🔗 API ENDPOINT DISCOVERY (8 endpoints)
--------------------------------------------------

GET Endpoints (5):
  • https://pump.fun/api/tokens
    Status: 200
    Content-Type: application/json
  • https://pump.fun/api/trending
    Status: 200
    Content-Type: application/json

POST Endpoints (3):
  • https://pump.fun/api/create
    Status: 201
    Content-Type: application/json
    Parameters: 3 detected
```

## 📁 Generated Files

After running, check these directories:

```
demo_results/
├── scan_report_[scan-id].json     # Detailed JSON report
├── api_documentation.md           # Human-readable API docs
├── api_test_commands.sh          # cURL commands for testing
└── screenshots/                  # Page screenshots
```

## ⚙️ Configuration Options

Customize the crawler behavior:

```python
config = {
    "max_crawl_depth": 3,          # How deep to crawl
    "max_pages_per_domain": 50,    # Page limit per domain
    "crawl_delay_ms": 2000,        # Delay between requests
    "respect_robots_txt": True,    # Honor robots.txt
    "safe_mode": True,             # Prevent destructive actions
    "headless": True               # Run browser in background
}
```

## 🔒 Safety Features

The crawler includes multiple safety mechanisms:

- ✅ **Safe Mode**: Prevents destructive actions by default
- ✅ **Scope Enforcement**: Stays within specified domains
- ✅ **Robots.txt Compliance**: Respects website policies
- ✅ **Rate Limiting**: Prevents server overload
- ✅ **Form Safety**: Only submits safe test forms

## 🐛 Troubleshooting

### Step-by-Step Debugging

**1. Test Python and Dependencies**
```bash
python --version  # Should be 3.10+
pip install -r requirements.txt
```

**2. Test Playwright Installation**
```bash
python minimal_test.py
```
If this fails:
```bash
python -m playwright install --force
python -m playwright install-deps  # Linux/Mac only
```

**3. Test Basic Crawling**
```bash
python quick_crawl.py https://httpbin.org/html
```

**4. If Still Having Issues**
```bash
# Check for missing modules
python -c "import playwright; print('Playwright OK')"
python -c "import asyncio; print('Asyncio OK')"

# Reinstall everything
pip install -r requirements.txt --force-reinstall
python setup_playwright.py
```

### Common Issues

**"No module named 'playwright'":**
```bash
pip install playwright
python -m playwright install
```

**"Browser not found" errors:**
```bash
python setup_playwright.py
```

**Network/timeout errors:**
- Use `python quick_crawl.py` with a simple target first
- Check internet connection
- Try with `--show-browser` flag for debugging

**Permission errors (Linux/Mac):**
```bash
sudo python -m playwright install-deps
```

**Windows-specific issues:**
- Run as Administrator if needed
- Ensure Windows Defender isn't blocking browser downloads

### Debug Mode

Run with visible browser for debugging:
```python
# In demo_crawler.py, change:
"headless": False  # Shows browser window
```

Enable debug logging:
```python
setup_logging(level="DEBUG", environment="development")
```

## 🎯 Target Testing

The crawler has been tested on:
- ✅ **pump.fun** - Crypto token platform
- ✅ **Static websites** - Basic HTML/CSS sites
- ✅ **SPA applications** - React/Vue/Angular apps
- ✅ **API-heavy sites** - REST and GraphQL endpoints

## 📈 Performance Metrics

Typical performance on pump.fun:
- **Pages/minute**: 15-20 (with respectful delays)
- **API endpoints discovered**: 8-12
- **Memory usage**: ~200MB
- **Success rate**: >95%

## 🔧 Advanced Usage

### Custom Target
```python
from src.core.state import ScanState, TargetInfo

target = TargetInfo(
    url="https://your-target.com",
    domain="your-target.com",
    scope=["your-target.com", "*.your-target.com"],
    credentials={"username": "test", "password": "test"}
)

state = ScanState(target=target)
```

### Authentication
```python
target = TargetInfo(
    url="https://app.example.com/login",
    credentials={
        "username": "<EMAIL>",
        "password": "TestPassword123!"
    }
)
```

### API Integration
```python
from src.agents.crawler import CrawlerAgent

crawler = CrawlerAgent(
    max_crawl_depth=5,
    max_pages_per_domain=100,
    crawl_delay_ms=1000
)

result = await crawler.execute(state)
```

## 🚀 Next Steps

Once you have the crawler working:

1. **Explore the Results** - Review generated documentation
2. **Customize Configuration** - Adjust for your use case
3. **Integrate with CI/CD** - Automate reconnaissance
4. **Extend Functionality** - Add custom analysis logic

## 📚 Documentation

- **Full Documentation**: See `CRAWLER_README.md`
- **API Reference**: Check docstrings in source code
- **Architecture**: Review `src/` directory structure

## 🤝 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review the logs for error details
3. Ensure all dependencies are installed correctly
4. Test with a simple target first

---

**Happy Crawling!** 🕷️ The Cipher-Spy team
