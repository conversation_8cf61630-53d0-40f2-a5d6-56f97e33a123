/* Cipher-Spy Network Monitor - Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 380px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  color: #333;
  background: #f8f9fa;
}

.container {
  padding: 16px;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e1e5e9;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon {
  font-size: 20px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #1a73e8;
}

.status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dc3545;
}

.status-indicator.active {
  background: #28a745;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

/* Controls */
.controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover {
  background: #1557b0;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  border: 1px solid #dee2e6;
  color: #495057;
}

.btn-outline:hover {
  background: #e9ecef;
}

.btn-text {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
}

.btn-text:hover {
  color: #495057;
}

.btn-icon {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.btn-icon:hover {
  background: #e9ecef;
}

/* Statistics */
.stats {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #1a73e8;
}

.stat-label {
  font-size: 11px;
  color: #6c757d;
  margin-top: 2px;
}

/* Session Info */
.session-info {
  margin-bottom: 16px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.session-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.session-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 12px;
  color: #6c757d;
}

.value {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  font-family: monospace;
}

/* Sections */
.section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

/* Request List */
.request-list {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  background: white;
}

.request-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 12px;
}

.request-item:last-child {
  border-bottom: none;
}

.request-method {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  font-size: 10px;
  margin-right: 8px;
}

.method-GET { background: #d4edda; color: #155724; }
.method-POST { background: #d1ecf1; color: #0c5460; }
.method-PUT { background: #fff3cd; color: #856404; }
.method-DELETE { background: #f8d7da; color: #721c24; }

.request-url {
  color: #495057;
  word-break: break-all;
}

.request-time {
  color: #6c757d;
  font-size: 10px;
  margin-top: 2px;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-size: 12px;
}

/* Actions */
.actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Settings */
.settings-toggle {
  text-align: center;
  margin-bottom: 12px;
}

.settings-panel {
  border-top: 1px solid #e1e5e9;
  padding-top: 16px;
  margin-top: 16px;
}

.setting-group {
  margin-bottom: 12px;
}

.setting-label {
  display: block;
  font-size: 12px;
  color: #495057;
  margin-bottom: 4px;
  cursor: pointer;
}

.setting-label input[type="checkbox"] {
  margin-right: 6px;
}

.setting-input,
.setting-textarea {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  font-family: inherit;
}

.setting-textarea {
  resize: vertical;
  min-height: 60px;
}

.setting-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e1e5e9;
  margin-top: 16px;
}

.version {
  font-size: 11px;
  color: #6c757d;
}

.help-link {
  font-size: 11px;
  color: #1a73e8;
  text-decoration: none;
}

.help-link:hover {
  text-decoration: underline;
}

/* Scrollbar Styling */
.request-list::-webkit-scrollbar {
  width: 6px;
}

.request-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.request-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.request-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animations */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-indicator.active {
  animation: pulse 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  body {
    width: 320px;
  }
  
  .container {
    padding: 12px;
  }
  
  .stats {
    gap: 8px;
  }
  
  .stat-number {
    font-size: 18px;
  }
}
