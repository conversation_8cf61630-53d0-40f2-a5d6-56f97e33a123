# Cipher-Spy Installation Guide

Quick setup guide for the Autonomous Web Reconnaissance Agent.

## 🚀 Quick Install (Recommended)

### 1. Install Core Dependencies
```bash
# Install Python packages
pip install -r requirements.txt

# Setup Playwright browsers
python setup_playwright.py
```

### 2. Run the Demo
```bash
# Interactive menu
python run.py
# Choose option 1 (Demo)

# Or direct command
python cipher-spy.py demo
```

## 📦 Detailed Installation

### Prerequisites
- Python 3.10+ 
- 4GB+ RAM
- Internet connection for browser downloads

### Step 1: Clone Repository
```bash
git clone <cipher-spy-repo>
cd cipher-spy
```

### Step 2: Install Dependencies
```bash
# Core dependencies (required)
pip install -r requirements.txt

# Optional: Development tools
pip install -r requirements-dev.txt
```

### Step 3: Setup Playwright
```bash
# Install browser binaries
python setup_playwright.py

# Or manually:
python -m playwright install
python -m playwright install-deps
```

### Step 4: Test Installation
```bash
# Run component tests
python test_crawler.py

# Run interactive demo
python demo_crawler.py
```

## 🧠 Optional: Graphiti Knowledge Graph

For advanced exploit correlation and vulnerability mapping:

### Install Graphiti
```bash
# Automated setup
python setup_graphiti.py

# Or manual install
pip install -r requirements-graphiti.txt
```

### Configure Environment
Create `.env` file:
```bash
# Optional: OpenAI API for advanced features
OPENAI_API_KEY=your_key_here

# Neo4j database (for knowledge graph)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password
```

### Start Neo4j (Optional)
```bash
# Using Docker
docker run -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/password \
  neo4j:latest

# Or use Docker Compose
docker-compose up neo4j
```

## 🎯 Verification

### Test Core Functionality
```bash
# Quick test
python cipher-spy.py crawl https://httpbin.org --depth 1 --pages 5

# Full demo
python cipher-spy.py demo --target https://pump.fun
```

### Expected Output
```
🕷️  Starting Cipher-Spy Autonomous Crawler
🎯 Target: https://httpbin.org
🆔 Scan ID: abc123...
🚀 Starting basic crawler...

📊 CRAWLING RESULTS
==================================================
✅ Scan completed in 15.32 seconds
📄 Pages discovered: 5
🔗 Endpoints discovered: 3
📊 Final status: completed
```

## 🐛 Troubleshooting

### Common Issues

**1. Playwright Installation Failed**
```bash
# Force reinstall
python -m playwright install --force

# Install system dependencies
python -m playwright install-deps
```

**2. Permission Errors (Linux/Mac)**
```bash
# Fix permissions
chmod +x *.py
sudo python -m playwright install-deps
```

**3. Import Errors**
```bash
# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Check Python version
python --version  # Should be 3.10+
```

**4. Network Timeouts**
- Increase delays in configuration
- Check target website availability
- Verify internet connection

**5. Memory Issues**
- Reduce `max_pages_per_domain` setting
- Enable `headless` mode
- Close other applications

### Debug Mode
```bash
# Verbose logging
python cipher-spy.py crawl https://example.com --verbose

# Show browser window
python cipher-spy.py crawl https://example.com --show-browser

# Debug logging
python cipher-spy.py crawl https://example.com --debug
```

## 📁 Directory Structure

After installation:
```
cipher-spy/
├── src/                    # Source code
├── demo_results/          # Demo output (created on first run)
├── data/screenshots/      # Screenshots (created on first run)
├── *.py                   # Entry point scripts
├── requirements*.txt      # Dependencies
└── *.md                   # Documentation
```

## ⚙️ Configuration

### Environment Variables
```bash
# .env file
LOG_LEVEL=INFO
ENVIRONMENT=development
SAFE_MODE=true
CRAWL_DELAY_MS=1000
MAX_PAGES_PER_DOMAIN=100
```

### Custom Configuration
```json
// config.json
{
  "crawler": {
    "max_crawl_depth": 3,
    "max_pages_per_domain": 50,
    "crawl_delay_ms": 2000,
    "safe_mode": true,
    "headless": true
  }
}
```

## 🚀 Next Steps

1. **Run the Demo**: `python run.py` → Option 1
2. **Test Custom Target**: `python run.py` → Option 2  
3. **Read Documentation**: `CRAWLER_README.md`
4. **Setup Graphiti**: `python run.py` → Option 6
5. **Start API Server**: `python run.py` → Option 3

## 📞 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review error logs for details
3. Ensure all dependencies are installed
4. Test with a simple target first (e.g., httpbin.org)

---

**Ready to start autonomous reconnaissance!** 🕷️
