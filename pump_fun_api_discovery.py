#!/usr/bin/env python3
"""
Pump.fun API Discovery Script

Comprehensive autonomous API discovery and reverse engineering for pump.fun.
This script demonstrates the full capabilities of the Cipher-Spy API discovery agent.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.agents.api_discovery_agent import APIDiscoveryAgent
from src.utils.logging import setup_logging


class PumpFunAPIDiscovery:
    """Specialized API discovery for pump.fun platform."""

    def __init__(self):
        self.target_url = "https://pump.fun"
        self.results_dir = Path("pump_fun_api_results")
        self.results_dir.mkdir(exist_ok=True)

        # Configuration optimized for pump.fun
        self.config = {
            'headless': False,  # Show browser for navigation demo
            'crawl_delay_ms': 2000,  # Be respectful but efficient
            'max_interactions': 50,  # More comprehensive exploration
            'max_navigation_depth': 3,  # How deep to navigate
            'respect_robots_txt': True,
            'safe_mode': True,
            'autonomous_navigation': True,
            'explore_all_links': True
        }

    async def run_discovery(self) -> Dict[str, Any]:
        """Run comprehensive API discovery on pump.fun."""
        print("🎯 Cipher-Spy API Discovery: pump.fun")
        print("="*60)
        print("🚀 Initializing autonomous API discovery agent...")

        try:
            # Initialize discovery agent
            agent = APIDiscoveryAgent(self.target_url, self.config)

            print(f"🎯 Target: {self.target_url}")
            print(f"⚙️  Configuration: {self.config}")
            print()

            # Run discovery
            print("🔍 Starting comprehensive API discovery...")
            start_time = datetime.now()

            results = await agent.discover_apis()

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            print(f"\n✅ API discovery completed in {duration:.2f} seconds")

            # Process and display results
            await self._process_results(results, duration)

            return results

        except Exception as e:
            print(f"\n💥 API discovery failed: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}

    async def _process_results(self, results: Dict[str, Any], duration: float):
        """Process and display discovery results."""
        print("\n📊 API DISCOVERY RESULTS")
        print("="*60)

        # Summary
        summary = results.get('summary', {})
        print(f"📄 Total API Endpoints: {summary.get('total_endpoints_discovered', 0)}")
        print(f"🔐 Authentication Required: {'Yes' if summary.get('authentication_required') else 'No'}")
        print(f"🤖 Interactions Performed: {summary.get('interactions_performed', 0)}")
        print(f"📡 API Calls Triggered: {summary.get('api_calls_triggered', 0)}")
        print(f"⏱️  Discovery Duration: {duration:.2f} seconds")

        # API Analysis
        api_analysis = results.get('api_analysis', {})
        if api_analysis and not api_analysis.get('error'):
            print(f"\n🔗 API ENDPOINT ANALYSIS")
            print("-" * 40)

            categorized = api_analysis.get('categorized_endpoints', {})
            for category, endpoints in categorized.items():
                if endpoints:
                    print(f"\n{category.upper()} ({len(endpoints)} endpoints):")
                    for endpoint in endpoints[:5]:  # Show first 5
                        method = endpoint.get('method', 'GET')
                        url = endpoint.get('url', '')
                        status = endpoint.get('response_status', 'N/A')
                        print(f"  • {method} {url} → {status}")

                    if len(endpoints) > 5:
                        print(f"    ... and {len(endpoints) - 5} more")

            # API Patterns
            patterns = api_analysis.get('patterns', {})
            if patterns:
                print(f"\n📈 API PATTERNS")
                print("-" * 20)
                print(f"Base URLs: {patterns.get('base_urls', [])}")
                print(f"HTTP Methods: {patterns.get('methods_used', [])}")

                common_paths = patterns.get('common_paths', {})
                if common_paths:
                    print("Common Path Components:")
                    for path, count in sorted(common_paths.items(), key=lambda x: x[1], reverse=True)[:10]:
                        print(f"  • /{path} (used {count} times)")

        # Authentication Analysis
        auth_analysis = results.get('authentication_analysis', {})
        if auth_analysis and not auth_analysis.get('error'):
            print(f"\n🔐 AUTHENTICATION ANALYSIS")
            print("-" * 30)

            auth_endpoints = auth_analysis.get('auth_endpoints', [])
            if auth_endpoints:
                print(f"Authentication Endpoints ({len(auth_endpoints)}):")
                for endpoint in auth_endpoints:
                    method = endpoint.get('method', 'GET')
                    url = endpoint.get('url', '')
                    print(f"  • {method} {url}")

            token_patterns = auth_analysis.get('token_patterns', {})
            for token_type, tokens in token_patterns.items():
                if tokens:
                    print(f"{token_type.replace('_', ' ').title()}: {len(tokens)} found")

            auth_flows = auth_analysis.get('auth_flows', [])
            if auth_flows:
                print("Authentication Flows:")
                for flow in auth_flows:
                    print(f"  • {flow.get('type', 'unknown')}: {flow.get('description', 'N/A')}")

        # Exploration Results
        exploration = results.get('exploration_results', {})
        if exploration and exploration.get('exploration_successful'):
            print(f"\n🤖 AUTONOMOUS EXPLORATION")
            print("-" * 30)
            print(f"Interactive Elements Found: {exploration.get('interactive_elements_found', 0)}")
            print(f"Forms Found: {exploration.get('forms_found', 0)}")
            print(f"Successful Interactions: {exploration.get('interactions_performed', 0)}")

            api_triggers = exploration.get('api_calls_triggered', [])
            if api_triggers:
                print(f"\nAPI-Triggering Interactions ({len(api_triggers)}):")
                for trigger in api_triggers[:5]:
                    element_info = trigger.get('element', {})
                    api_count = trigger.get('api_calls_triggered', 0)
                    element_text = element_info.get('text_content', 'Unknown')[:50]
                    print(f"  • '{element_text}' → {api_count} API calls")

        # Network Statistics
        network_stats = results.get('network_statistics', {})
        if network_stats:
            print(f"\n📊 NETWORK STATISTICS")
            print("-" * 25)
            print(f"Total Requests: {network_stats.get('total_requests', 0)}")
            print(f"Total Responses: {network_stats.get('total_responses', 0)}")
            print(f"Unique Domains: {network_stats.get('unique_domains', 0)}")
            print(f"HTTP Methods: {network_stats.get('methods_used', [])}")
            print(f"Status Codes: {network_stats.get('status_codes_seen', [])}")

        # Save detailed results
        await self._save_results(results, duration)

    async def _save_results(self, results: Dict[str, Any], duration: float):
        """Save detailed results to files."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save main results
        results_file = self.results_dir / f"pump_fun_api_discovery_{timestamp}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Detailed results saved: {results_file}")

        # Generate API documentation
        await self._generate_api_docs(results, timestamp)

        # Generate cURL commands
        await self._generate_curl_commands(results, timestamp)

        # Generate Postman collection
        await self._generate_postman_collection(results, timestamp)

    async def _generate_api_docs(self, results: Dict[str, Any], timestamp: str):
        """Generate human-readable API documentation."""
        docs = []
        docs.append("# Pump.fun API Documentation")
        docs.append(f"Generated by Cipher-Spy on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        docs.append("")
        docs.append("## Overview")
        docs.append(f"This documentation was automatically generated through autonomous API discovery.")
        docs.append("")

        # Add endpoint documentation
        api_analysis = results.get('api_analysis', {})
        schemas = api_analysis.get('schemas', {})

        if schemas:
            docs.append("## API Endpoints")
            docs.append("")

            for endpoint_key, schema in schemas.items():
                docs.append(f"### {endpoint_key}")
                docs.append("")
                docs.append(f"**Method:** {schema.get('method', 'GET')}")
                docs.append(f"**URL:** {schema.get('url', 'N/A')}")

                if schema.get('parameters'):
                    docs.append("**Parameters:**")
                    for param, value in schema.get('parameters', {}).items():
                        docs.append(f"- `{param}`: {value}")

                if schema.get('status_codes'):
                    docs.append(f"**Status Codes:** {', '.join(map(str, schema.get('status_codes', [])))}")

                if schema.get('content_types'):
                    docs.append(f"**Content Types:** {', '.join(schema.get('content_types', []))}")

                docs.append("")

        # Save documentation
        docs_file = self.results_dir / f"pump_fun_api_docs_{timestamp}.md"
        with open(docs_file, 'w') as f:
            f.write('\n'.join(docs))

        print(f"📚 API documentation saved: {docs_file}")

    async def _generate_curl_commands(self, results: Dict[str, Any], timestamp: str):
        """Generate cURL commands for testing."""
        commands = []
        commands.append("#!/bin/bash")
        commands.append("# cURL commands for pump.fun API endpoints")
        commands.append(f"# Generated by Cipher-Spy on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        commands.append("")

        api_analysis = results.get('api_analysis', {})
        schemas = api_analysis.get('schemas', {})

        for endpoint_key, schema in schemas.items():
            method = schema.get('method', 'GET')
            url = schema.get('url', '')

            if url:
                commands.append(f"# {endpoint_key}")
                curl_cmd = f"curl -X {method}"

                # Add headers
                headers = schema.get('headers', {})
                for header, value in headers.items():
                    if header.lower() not in ['host', 'content-length']:
                        curl_cmd += f" -H '{header}: {value}'"

                curl_cmd += f" '{url}'"
                commands.append(curl_cmd)
                commands.append("")

        # Save cURL commands
        curl_file = self.results_dir / f"pump_fun_curl_commands_{timestamp}.sh"
        with open(curl_file, 'w') as f:
            f.write('\n'.join(commands))

        curl_file.chmod(0o755)
        print(f"🔧 cURL commands saved: {curl_file}")

    async def _generate_postman_collection(self, results: Dict[str, Any], timestamp: str):
        """Generate Postman collection for API testing."""
        collection = {
            "info": {
                "name": "Pump.fun API Collection",
                "description": "Auto-generated API collection from Cipher-Spy discovery",
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "item": []
        }

        api_analysis = results.get('api_analysis', {})
        schemas = api_analysis.get('schemas', {})

        for endpoint_key, schema in schemas.items():
            method = schema.get('method', 'GET')
            url = schema.get('url', '')

            if url:
                item = {
                    "name": endpoint_key,
                    "request": {
                        "method": method,
                        "header": [],
                        "url": {
                            "raw": url,
                            "protocol": "https",
                            "host": ["pump", "fun"],
                            "path": url.split('/')
                        }
                    }
                }

                # Add headers
                headers = schema.get('headers', {})
                for header, value in headers.items():
                    item["request"]["header"].append({
                        "key": header,
                        "value": value
                    })

                collection["item"].append(item)

        # Save Postman collection
        postman_file = self.results_dir / f"pump_fun_postman_{timestamp}.json"
        with open(postman_file, 'w') as f:
            json.dump(collection, f, indent=2)

        print(f"📮 Postman collection saved: {postman_file}")


async def main():
    """Main entry point."""
    # Setup logging
    setup_logging(level="INFO", environment="development")

    print("🔬 Cipher-Spy: Autonomous API Discovery Agent")
    print("🎯 Target: pump.fun")
    print("="*60)

    discovery = PumpFunAPIDiscovery()

    try:
        results = await discovery.run_discovery()

        if results.get('error'):
            print(f"\n❌ Discovery failed: {results['error']}")
            return 1
        else:
            print(f"\n🎉 API discovery completed successfully!")
            print(f"📁 Results saved to: {discovery.results_dir}")
            return 0

    except KeyboardInterrupt:
        print("\n👋 Discovery interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
