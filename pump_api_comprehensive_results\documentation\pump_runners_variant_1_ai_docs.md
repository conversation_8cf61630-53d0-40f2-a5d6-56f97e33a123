# Pump Runners API Endpoint Documentation

## Overview
The `/api/runners` endpoint retrieves a curated list of trending or featured content runners on the pump.fun platform. Each runner represents a content stream or project with associated cryptocurrency token information.

## Base URL
```
https://pump.fun/api
```

## Endpoint Details
```
GET /runners
```

## Use Cases
1. **Content Discovery**: Fetch trending content streams and projects on the platform
2. **Market Analysis**: Monitor new and popular tokens with their associated content
3. **Portfolio Tracking**: Track featured projects and their associated token metrics
4. **Content Integration**: Embed featured content listings in third-party applications
5. **Market Data Aggregation**: Collect real-time market caps and engagement metrics

## Request Parameters
This endpoint doesn't require any parameters. It returns a default set of 10 featured runners.

## Response Schema

### Root Response
| Field | Type | Description |
|-------|------|-------------|
| `Array` | array | Array of runner objects (max length: 10) |

### Runner Object
| Field | Type | Description |
|-------|------|-------------|
| `coin` | object | Detailed token information |
| `description` | string | Human-readable description of the runner |
| `modifiedBy` | string | Last modifier of the runner entry |

### Coin Object
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `mint` | string | Token mint address | "4Fr2LL7tJ52Y6a8g63nHzNqWDjy9Te1H55m8rrX7pump" |
| `name` | string | Token name | "Traveling To All 50 States LIVE" |
| `symbol` | string | Token symbol | "50STATES" |
| `market_cap` | number | Current market capitalization | 3087.57 |
| `usd_market_cap` | number | USD market cap equivalent | 491294.56 |
| `total_supply` | number | Total token supply | 1000000000000000 |
| `is_currently_live` | boolean | Stream active status | false |

## Code Examples

### Python
```python
import requests

def get_pump_runners():
    url = "https://pump.fun/api/runners"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching runners: {e}")
        return None

runners = get_pump_runners()
```

### cURL
```bash
curl -X GET "https://pump.fun/api/runners" \
  -H "Accept: application/json"
```

## Error Handling

### Common HTTP Status Codes
| Code | Description | Solution |
|------|-------------|----------|
| 200 | Successful request | - |
| 429 | Too many requests | Implement rate limiting |
| 500 | Server error | Retry with exponential backoff |
| 503 | Service unavailable | Retry after delay |

## Rate Limiting
- Implement exponential backoff for retries
- Recommended maximum: 60 requests per minute
- Cache responses when possible (recommended TTL: 60 seconds)

## Integration Tips

1. **Response Caching**
   ```python
   from functools import lru_cache
   
   @lru_cache(maxsize=1, ttl=60)
   def cached_get_runners():
       return get_pump_runners()
   ```

2. **Error Handling**
   ```python
   def safe_get_runners():
       max_retries = 3
       retry_delay = 1
       
       for attempt in range(max_retries):
           try:
               return get_pump_runners()
           except Exception as e:
               if attempt == max_retries - 1:
                   raise
               time.sleep(retry_delay * (2 ** attempt))
   ```

3. **Data Validation**
   ```python
   def validate_runner(runner):
       required_fields = ['coin', 'description', 'modifiedBy']
       return all(field in runner for field in required_fields)
   ```

## Best Practices
1. Always implement error handling
2. Cache responses when possible
3. Validate response data before processing
4. Monitor rate limits
5. Implement retry logic with exponential backoff

## Notes
- Response time averages 335ms
- Response size is approximately 12KB
- Endpoint returns up to 10 runners
- Data is sorted by relevance/trending status

For additional support or questions, please refer to the [pump.fun documentation](https://pump.fun/docs) or contact the API team.