# Trending Coins API Endpoint

## Overview
The `trending_coins_variant_1` endpoint provides real-time access to trending cryptocurrency tokens on pump.fun, sorted by market capitalization. This endpoint returns detailed information about the top tokens, including their market metrics, social links, and contract details.

## Base URL
```
https://frontend-api-v3.pump.fun
```

## Endpoint
```
GET /coins
```

## Use Cases
1. **Market Analysis**: Track trending tokens and their market performance
2. **Portfolio Tracking**: Monitor specific tokens' market caps and trading activity
3. **Social Integration**: Access token-related social media links and community metrics
4. **Trading Bot Integration**: Fetch real-time market data for automated trading systems

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| offset | integer | No | 0 | Number of items to skip |
| limit | integer | No | 50 | Number of items to return (max: 50) |
| sort | string | No | "market_cap" | Field to sort by |
| order | string | No | "DESC" | Sort order ("ASC" or "DESC") |
| includeNsfw | boolean | No | false | Include NSFW tokens |

## Response Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| mint | string | Token mint address | "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump" |
| name | string | Token name | "Fartcoin" |
| symbol | string | Token symbol | "FART" |
| market_cap | number | Market capitalization | 8238000.000000001 |
| usd_market_cap | number | USD market cap | 1310830560.0000002 |
| virtual_sol_reserves | number | SOL reserves | 115005359177 |
| total_supply | number | Total token supply | 1000000000000000 |

## Code Examples

### cURL
```bash
curl -X GET 'https://frontend-api-v3.pump.fun/coins?offset=0&limit=50&sort=market_cap&order=DESC&includeNsfw=false' \
  -H 'Accept: application/json'
```

### Python
```python
import requests

url = "https://frontend-api-v3.pump.fun/coins"
params = {
    "offset": 0,
    "limit": 50,
    "sort": "market_cap",
    "order": "DESC",
    "includeNsfw": False
}

response = requests.get(url, params=params)
data = response.json()

# Access first token's data
first_token = data[0]
print(f"Top token: {first_token['name']} (Market Cap: ${first_token['usd_market_cap']})")
```

## Error Handling

| Status Code | Description | Solution |
|-------------|-------------|----------|
| 400 | Invalid parameters | Check parameter values and types |
| 429 | Rate limit exceeded | Implement backoff strategy |
| 500 | Server error | Retry with exponential backoff |

## Rate Limiting
- Maximum 100 requests per minute per IP
- Implement exponential backoff for retry logic
- Cache responses when possible (recommended cache time: 60 seconds)

## Integration Tips

1. **Caching**
   - Cache responses to minimize API calls
   - Update data every 60 seconds for real-time applications

2. **Error Handling**
   - Implement retry logic with exponential backoff
   - Handle network timeouts gracefully

3. **Performance**
   - Use pagination for large datasets
   - Only request needed fields
   - Monitor response times and implement timeout handling

4. **Data Validation**
   - Always validate response data before processing
   - Handle null values appropriately

## Best Practices

1. Always check response status codes
2. Implement proper error handling
3. Use appropriate timeout values (recommended: 5 seconds)
4. Cache responses when possible
5. Monitor API usage and implement rate limiting on client side

## Notes
- Response time averages around 500ms
- Response size is approximately 64KB
- Endpoint returns up to 50 tokens per request
- Data is sorted by market cap by default

For additional support or questions, please refer to the pump.fun documentation or contact the API support team.