# for_you_coins_variant_1

## Endpoint Information
- **URL**: https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=48&includeNsfw=false
- **Method**: GET
- **Parameters**: {
  "offset": 0,
  "limit": 48,
  "includeNsfw": "false"
}

## Response Information
- **Response Time**: 160ms
- **Response Size**: 53873 bytes
- **Content Type**: application/json; charset=utf-8

## Response Schema
```json
{
  "type": "array",
  "length": 48,
  "items": {
    "type": "object",
    "properties": {
      "mint": {
        "type": "string",
        "example": "92Ck5HsinGuykorKpn8Mm6V1HHDLRAToEYXiToqLpump"
      },
      "name": {
        "type": "string",
        "example": "Trench Game Vlogs"
      },
      "symbol": {
        "type": "string",
        "example": "TGV"
      },
      "description": {
        "type": "string",
        "example": ""
      },
      "image_uri": {
        "type": "string",
        "example": "https://ipfs.io/ipfs/bafybeidzmrfqncyeiwnimg6lkczd"
      },
      "metadata_uri": {
        "type": "string",
        "example": "https://ipfs.io/ipfs/bafkreicyo7mpxtfh7h47bdvu4gtx"
      },
      "twitter": {
        "type": "string",
        "example": "https://x.com/i/communities/1932984484657324456"
      },
      "telegram": {
        "type": "null"
      },
      "bonding_curve": {
        "type": "string",
        "example": "FLdSqJw4kF96jbzobpBw4U6CGawMLv29tLzZds9cAGT2"
      },
      "associated_bonding_curve": {
        "type": "string",
        "example": "9dmcoXysTMKzfRABV1wSNbXQ692Wt3YysvgHHLvLjiqd"
      }
    },
    "total_keys": 30
  }
}
```

## Sample Response
```json
[
  {
    "mint": "92Ck5HsinGuykorKpn8Mm6V1HHDLRAToEYXiToqLpump",
    "name": "Trench Game Vlogs",
    "symbol": "TGV",
    "description": "",
    "image_uri": "https://ipfs.io/ipfs/bafybeidzmrfqncyeiwnimg6lkczdpkiaqi24rnj2g2wzp3l27txko3jiva",
    "metadata_uri": "https://ipfs.io/ipfs/bafkreicyo7mpxtfh7h47bdvu4gtxq3b7mu7432viaah4fycqo6aqvjxfpm",
    "twitter": "https://x.com/i/communities/1932984484657324456",
    "telegram": null,
    "bonding_curve": "FLdSqJw4kF96jbzobpBw4U6CGawMLv29tLzZds9cAGT2",
    "associated_bonding_curve": "9dmcoXysTMKzfRABV1wSNbXQ692Wt3YysvgHHLvLjiqd",
    "creator": "DBtXAGoeNWMpt3k9yvyog5RnC9eqwyrNF6m96hENMzE",
    "created_timestamp": 1749722284672,
    "raydium_pool": null,
    "complete": false,
    "virtual_sol_reserves": 31243383464,
    "virtual_token_reserves": 1030298147113309,
    "total_supply": 1000000000000000,
    "website": null,
    "show_name": true,
    "king_of_the_hill_timestamp": null,
    "market_cap": 30.324604146,
    "reply_count": 5,
```

## Usage Example
```python
import requests

response = requests.get(
    'https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=48&includeNsfw=false',
    params={"offset": 0, "limit": 48, "includeNsfw": "false"}
)

data = response.json()
print(data)
```
