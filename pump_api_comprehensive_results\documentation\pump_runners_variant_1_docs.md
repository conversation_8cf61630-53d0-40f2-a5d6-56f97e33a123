# pump_runners_variant_1

## Endpoint Information
- **URL**: https://pump.fun/api/runners
- **Method**: GET
- **Parameters**: {}

## Response Information
- **Response Time**: 359ms
- **Response Size**: 12136 bytes
- **Content Type**: application/json

## Response Schema
```json
{
  "type": "array",
  "length": 10,
  "items": {
    "type": "object",
    "properties": {
      "coin": {
        "type": "object",
        "properties": {
          "mint": {
            "type": "unknown"
          },
          "name": {
            "type": "unknown"
          },
          "symbol": {
            "type": "unknown"
          },
          "description": {
            "type": "unknown"
          },
          "image_uri": {
            "type": "unknown"
          },
          "metadata_uri": {
            "type": "unknown"
          },
          "twitter": {
            "type": "unknown"
          },
          "telegram": {
            "type": "unknown"
          },
          "bonding_curve": {
            "type": "unknown"
          },
          "associated_bonding_curve": {
            "type": "unknown"
          }
        },
        "total_keys": 29
      },
      "description": {
        "type": "string",
        "example": "Livestreamer Attempts to Break 50 State Road Trip "
      },
      "modifiedBy": {
        "type": "string",
        "example": "Unknown"
      }
    },
    "total_keys": 3
  }
}
```

## Sample Response
```json
[
  {
    "coin": {
      "mint": "4Fr2LL7tJ52Y6a8g63nHzNqWDjy9Te1H55m8rrX7pump",
      "name": "Traveling To All 50 States LIVE",
      "symbol": "50STATES",
      "description": "From sea to shining sea, I'm setting out to break the solo record for the fastest time to get to all 50 states, set by Barry Stiefel, at 199 hours, live only on Pump.Fun. All info is on the X. We are starting in Vermont and ending in Hawaii. ",
      "image_uri": "https://ipfs.io/ipfs/bafybeidt7adflt4kmokxydg4vfyero4q3narasx3wgwkxyiwngvy5atdmi",
      "metadata_uri": "https://ipfs.io/ipfs/bafkreida7xiu6ttfnv7ro2dny4hwmpzcqwidqh3o36dqr324wyinveelau",
      "twitter": "https://x.com/50StatesLIVE",
      "telegram": null,
      "bonding_curve": "Vz1sGH91MCxfKd1N3GoemRETkgDTs4nKTZJEYixHrpV",
      "associated_bonding_curve": "4HihtVM9Bm7awqheEpyr2PhCtH6RjiTqZLYsKEXWw1hx",
      "creator": "oPTXFyV1CjCcZtqh4rb6Ffvt124FLGe8cwVaytJ94KH",
      "created_timestamp": 1749658521721,
      "raydium_pool": null,
      "c
```

## Usage Example
```python
import requests

response = requests.get(
    'https://pump.fun/api/runners',
    params={}
)

data = response.json()
print(data)
```
