#!/usr/bin/env python3
"""
Basic functionality test for Cipher-Spy components.

Tests core components without complex dependencies.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def test_imports():
    """Test that all core modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from src.core.state import ScanState, TargetInfo, ScanStatus
        print("✅ Core state imports successful")
    except Exception as e:
        print(f"❌ Core state import failed: {e}")
        return False
    
    try:
        from src.agents.crawler import CrawlerAgent
        print("✅ Crawler agent import successful")
    except Exception as e:
        print(f"❌ Crawler agent import failed: {e}")
        return False
    
    try:
        from src.crawling.playwright_crawler import PlaywrightCrawler
        print("✅ Playwright crawler import successful")
    except Exception as e:
        print(f"❌ Playwright crawler import failed: {e}")
        return False
    
    return True


async def test_state_creation():
    """Test creating scan state."""
    print("\n🧪 Testing state creation...")
    
    try:
        from src.core.state import ScanState, TargetInfo
        
        # Create target
        target = TargetInfo(
            url="https://httpbin.org",
            domain="httpbin.org",
            scope=["httpbin.org"]
        )
        
        # Create scan state
        state = ScanState(target=target)
        
        print(f"✅ Scan state created: {state.scan_id}")
        print(f"   Target: {state.target.url}")
        print(f"   Status: {state.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ State creation failed: {e}")
        return False


async def test_crawler_creation():
    """Test creating crawler agent."""
    print("\n🧪 Testing crawler creation...")
    
    try:
        from src.agents.crawler import CrawlerAgent
        
        # Create crawler with minimal config
        crawler = CrawlerAgent(
            max_crawl_depth=1,
            max_pages_per_domain=5,
            crawl_delay_ms=2000,
            headless=True
        )
        
        print(f"✅ Crawler agent created")
        print(f"   Agent ID: {crawler.agent_id}")
        print(f"   Max depth: {crawler.max_crawl_depth}")
        
        return True
        
    except Exception as e:
        print(f"❌ Crawler creation failed: {e}")
        return False


async def test_simple_crawl():
    """Test a very simple crawl operation."""
    print("\n🧪 Testing simple crawl...")
    
    try:
        from src.core.state import ScanState, TargetInfo
        from src.agents.crawler import CrawlerAgent
        
        # Create target (use a simple, reliable site)
        target = TargetInfo(
            url="https://httpbin.org/html",
            domain="httpbin.org",
            scope=["httpbin.org"]
        )
        
        # Create scan state
        state = ScanState(
            target=target,
            config={
                "max_crawl_depth": 1,
                "max_pages_per_domain": 3,
                "crawl_delay_ms": 3000,
                "safe_mode": True,
                "headless": True
            }
        )
        
        # Create crawler
        crawler = CrawlerAgent(
            max_crawl_depth=1,
            max_pages_per_domain=3,
            crawl_delay_ms=3000,
            headless=True
        )
        
        print(f"🚀 Starting crawl of {target.url}...")
        
        # Execute crawl with timeout
        result = await asyncio.wait_for(
            crawler.execute(state),
            timeout=60.0  # 60 second timeout
        )
        
        print(f"✅ Crawl completed!")
        print(f"   Status: {result.status}")
        print(f"   Pages found: {len(result.pages)}")
        print(f"   Endpoints found: {len(result.endpoints)}")
        
        # Show some results
        if result.pages:
            print(f"   First page: {result.pages[0].url}")
        
        return True
        
    except asyncio.TimeoutError:
        print("❌ Crawl timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Crawl failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("🔬 Cipher-Spy Basic Functionality Test")
    print("="*50)
    
    tests = [
        ("Import Test", test_imports),
        ("State Creation Test", test_state_creation),
        ("Crawler Creation Test", test_crawler_creation),
        ("Simple Crawl Test", test_simple_crawl),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cipher-Spy is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
