"""
Main application entry point for Cipher-Spy.

This module initializes the FastAPI application, sets up middleware,
configures logging, and starts the web server. It also handles
graceful shutdown and cleanup of resources.
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config.settings import get_settings
from .config.database import init_databases, close_databases
from .api.routes import scans, targets, findings, exploits, extension
from .utils.logging import setup_logging
from .core.exceptions import CipherSpyException


# Global settings instance
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.

    Handles startup and shutdown events:
    - Initialize databases and connections
    - Load exploit knowledge base
    - Setup background tasks
    - Cleanup resources on shutdown
    """
    # Startup
    logging.info("Starting Cipher-Spy application...")

    try:
        # Skip database initialization for now - run in standalone mode
        logging.info("Running in standalone mode (no database)")

        # TODO: Initialize database connections
        # await init_databases()
        # logging.info("Database connections initialized")

        # TODO: Load exploit knowledge base
        # await load_exploit_knowledge_base()

        # TODO: Initialize agent workflow
        # await init_agent_workflow()

        logging.info("Cipher-Spy startup complete")

    except Exception as e:
        logging.error(f"Failed to start application: {e}")
        raise

    yield

    # Shutdown
    logging.info("Shutting down Cipher-Spy...")

    try:
        # Skip database cleanup for now
        logging.info("Standalone mode - no database cleanup needed")

        # TODO: Close database connections
        # await close_databases()
        # logging.info("Database connections closed")

        # TODO: Cleanup agent resources
        # await cleanup_agents()

        logging.info("Cipher-Spy shutdown complete")

    except Exception as e:
        logging.error(f"Error during shutdown: {e}")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    app = FastAPI(
        title="Cipher-Spy API",
        description="AI-Driven Red Team Swarm for Penetration Testing",
        version="1.0.0",
        docs_url="/docs" if settings.environment == "development" else None,
        redoc_url="/redoc" if settings.environment == "development" else None,
        lifespan=lifespan
    )

    # Add CORS middleware
    if settings.enable_cors:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # Include API routes
    app.include_router(
        scans.router,
        prefix=f"{settings.api_v1_str}/scans",
        tags=["scans"]
    )
    app.include_router(
        targets.router,
        prefix=f"{settings.api_v1_str}/targets",
        tags=["targets"]
    )
    app.include_router(
        findings.router,
        prefix=f"{settings.api_v1_str}/findings",
        tags=["findings"]
    )
    app.include_router(
        exploits.router,
        prefix=f"{settings.api_v1_str}/exploits",
        tags=["exploits"]
    )
    app.include_router(
        extension.router,
        prefix=f"{settings.api_v1_str}/extension",
        tags=["extension"]
    )

    # Global exception handler
    @app.exception_handler(CipherSpyException)
    async def cipher_spy_exception_handler(request, exc: CipherSpyException):
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.message, "details": exc.details}
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        logging.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": "Internal server error"}
        )

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for monitoring."""
        return {
            "status": "healthy",
            "version": "1.0.0",
            "environment": settings.environment
        }

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with basic information."""
        return {
            "name": "Cipher-Spy",
            "description": "AI-Driven Red Team Swarm",
            "version": "1.0.0",
            "docs": "/docs" if settings.environment == "development" else None
        }

    return app


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logging.info(f"Received signal {signum}, shutting down...")
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(description="Cipher-Spy: AI-Driven Red Team Swarm")
    parser.add_argument("--mode", choices=["server", "crawler", "demo"], default="server",
                       help="Run mode: server (API), crawler (standalone), or demo")
    parser.add_argument("--target", help="Target URL for crawler mode")
    parser.add_argument("--config", help="Configuration file path")

    args = parser.parse_args()

    # Setup logging
    setup_logging(
        level=settings.log_level,
        environment=settings.environment
    )

    if args.mode == "server":
        # Run API server
        setup_signal_handlers()
        app = create_app()
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level=settings.log_level.lower(),
            reload=settings.reload_on_change and settings.environment == "development"
        )
    elif args.mode == "crawler":
        # Run standalone crawler
        if not args.target:
            print("Error: --target required for crawler mode")
            sys.exit(1)
        asyncio.run(run_standalone_crawler(args.target, args.config))
    elif args.mode == "demo":
        # Run demo
        asyncio.run(run_demo())


async def run_standalone_crawler(target_url: str, config_file: Optional[str] = None):
    """Run the crawler in standalone mode."""
    from .core.state import ScanState, TargetInfo
    from .core.workflow import create_workflow
    from urllib.parse import urlparse
    import json

    print(f"🚀 Starting Cipher-Spy Crawler")
    print(f"🎯 Target: {target_url}")

    # Parse target URL
    parsed = urlparse(target_url)
    domain = parsed.netloc

    # Load configuration
    config = {}
    if config_file:
        with open(config_file, 'r') as f:
            config = json.load(f)

    # Create target
    target = TargetInfo(
        url=target_url,
        domain=domain,
        scope=[domain, f"*.{domain}"]
    )

    # Create scan state
    state = ScanState(target=target, config=config)

    # Create and run workflow
    workflow = create_workflow(config)
    final_state = await workflow.execute(state)

    # Print results
    print(f"\n✅ Scan completed!")
    print(f"📄 Pages discovered: {len(final_state.pages)}")
    print(f"🔗 Endpoints discovered: {len(final_state.endpoints)}")
    print(f"📊 Status: {final_state.status}")

    # Cleanup
    await workflow.cleanup()


async def run_demo():
    """Run the interactive demo."""
    from pathlib import Path
    import sys

    # Import demo
    demo_path = Path(__file__).parent.parent / "demo_crawler.py"
    if demo_path.exists():
        # Run the demo
        import subprocess
        result = subprocess.run([sys.executable, str(demo_path)])
        sys.exit(result.returncode)
    else:
        print("Demo script not found. Please run: python demo_crawler.py")
        sys.exit(1)


if __name__ == "__main__":
    main()
