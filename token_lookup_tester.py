#!/usr/bin/env python3
"""
Token Lookup Tester for Pump.fun APIs

Tests various potential endpoints and patterns to find specific token information
using mint address: GtGHchFcr48SRZQHMUN4jTr7PpNZoQSXbpBrexuipump
"""

import requests
import time
import json
from typing import Dict, List, Any, Optional

class TokenLookupTester:
    """Test various endpoints to find token-specific information."""
    
    def __init__(self):
        self.target_mint = "GtGHchFcr48SRZQHMUN4jTr7PpNZoQSXbpBrexuipump"
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://pump.fun',
            'Referer': 'https://pump.fun/advanced/coin?scan=true'
        }
        
        # Potential base URLs to test
        self.base_urls = [
            'https://pump.fun/api',
            'https://frontend-api-v3.pump.fun',
            'https://advanced-api-v2.pump.fun',
            'https://api.pump.fun',
            'https://backend-api.pump.fun'
        ]
        
        # Potential endpoint patterns
        self.endpoint_patterns = [
            '/coin/{mint}',
            '/coins/{mint}',
            '/token/{mint}',
            '/tokens/{mint}',
            '/mint/{mint}',
            '/coin?mint={mint}',
            '/coins?mint={mint}',
            '/token?mint={mint}',
            '/tokens?mint={mint}',
            '/coin/details/{mint}',
            '/coin/info/{mint}',
            '/coin/data/{mint}',
            '/v1/coin/{mint}',
            '/v2/coin/{mint}',
            '/v3/coin/{mint}',
            '/{mint}',
            '/details/{mint}',
            '/info/{mint}'
        ]
    
    def test_all_endpoints(self):
        """Test all potential endpoint combinations."""
        print(f"🔍 Testing token lookup for mint: {self.target_mint}")
        print("=" * 80)
        
        successful_endpoints = []
        
        for base_url in self.base_urls:
            print(f"\n🌐 Testing base URL: {base_url}")
            print("-" * 50)
            
            for pattern in self.endpoint_patterns:
                endpoint = pattern.format(mint=self.target_mint)
                full_url = f"{base_url}{endpoint}"
                
                result = self._test_endpoint(full_url)
                
                if result['success']:
                    successful_endpoints.append(result)
                    print(f"  ✅ {endpoint}: {result['status_code']} - {result['data_type']}")
                    
                    # Save detailed response for successful endpoints
                    self._save_response(full_url, result['data'])
                else:
                    print(f"  ❌ {endpoint}: {result['status_code']}")
                
                time.sleep(0.5)  # Rate limiting
        
        print(f"\n🎉 Found {len(successful_endpoints)} working endpoints!")
        
        if successful_endpoints:
            print("\n📋 Successful Endpoints Summary:")
            for endpoint in successful_endpoints:
                print(f"  🔗 {endpoint['url']}")
                print(f"     Status: {endpoint['status_code']}")
                print(f"     Data Type: {endpoint['data_type']}")
                print(f"     Response Size: {endpoint['response_size']} chars")
                print()
        
        return successful_endpoints
    
    def _test_endpoint(self, url: str) -> Dict[str, Any]:
        """Test a specific endpoint."""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            
            result = {
                'url': url,
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'data': None,
                'data_type': 'unknown',
                'response_size': 0
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result['data'] = data
                    result['data_type'] = self._analyze_data_type(data)
                    result['response_size'] = len(response.text)
                except:
                    result['data'] = response.text
                    result['data_type'] = 'text'
                    result['response_size'] = len(response.text)
            
            return result
            
        except Exception as e:
            return {
                'url': url,
                'success': False,
                'status_code': 0,
                'error': str(e),
                'data': None,
                'data_type': 'error',
                'response_size': 0
            }
    
    def _analyze_data_type(self, data: Any) -> str:
        """Analyze the type and structure of response data."""
        if isinstance(data, dict):
            if 'mint' in data or 'coinMint' in data:
                return 'single_coin'
            elif 'coins' in data:
                return 'coin_list'
            elif 'error' in data:
                return 'error_response'
            else:
                return 'object'
        elif isinstance(data, list):
            if data and isinstance(data[0], dict):
                if 'mint' in data[0] or 'coinMint' in data[0]:
                    return 'coin_array'
                else:
                    return 'array'
            else:
                return 'simple_array'
        else:
            return 'primitive'
    
    def _save_response(self, url: str, data: Any):
        """Save successful response data."""
        filename = f"token_response_{hash(url) % 10000}.json"
        
        response_info = {
            'url': url,
            'mint_address': self.target_mint,
            'timestamp': time.time(),
            'data': data
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(response_info, f, indent=2, default=str)
            print(f"    💾 Response saved to: {filename}")
        except Exception as e:
            print(f"    ⚠️  Failed to save response: {e}")
    
    def test_search_in_listings(self):
        """Test if we can find the token in general listings."""
        print(f"\n🔍 Searching for token in general listings...")
        
        # Test advanced API listings
        search_results = []
        
        endpoints_to_search = [
            ('https://advanced-api-v2.pump.fun/coins/list', {'limit': 200, 'sortBy': 'creationTime'}),
            ('https://advanced-api-v2.pump.fun/coins/list', {'limit': 200, 'sortBy': 'volume'}),
            ('https://advanced-api-v2.pump.fun/coins/list', {'limit': 200, 'sortBy': 'marketCap'}),
            ('https://advanced-api-v2.pump.fun/coins/graduated', {'limit': 200}),
            ('https://frontend-api-v3.pump.fun/coins', {'limit': 50, 'offset': 0}),
            ('https://frontend-api-v3.pump.fun/coins/for-you', {'limit': 48, 'offset': 0})
        ]
        
        for url, params in endpoints_to_search:
            print(f"  🔍 Searching in: {url}")
            
            try:
                response = requests.get(url, params=params, headers=self.headers, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    found = self._search_for_mint_in_data(data, self.target_mint)
                    
                    if found:
                        search_results.append({
                            'endpoint': url,
                            'params': params,
                            'found_data': found
                        })
                        print(f"    ✅ Found token in this endpoint!")
                        self._save_response(f"{url}_search_result", found)
                    else:
                        print(f"    ❌ Token not found in this endpoint")
                else:
                    print(f"    ❌ Request failed: {response.status_code}")
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                print(f"    ❌ Error: {e}")
        
        return search_results
    
    def _search_for_mint_in_data(self, data: Any, target_mint: str) -> Optional[Dict]:
        """Search for specific mint address in response data."""
        if isinstance(data, dict):
            # Check if this object contains the mint
            if data.get('mint') == target_mint or data.get('coinMint') == target_mint:
                return data
            
            # Check nested objects
            for key, value in data.items():
                if key == 'coins' and isinstance(value, list):
                    for coin in value:
                        if isinstance(coin, dict):
                            if coin.get('mint') == target_mint or coin.get('coinMint') == target_mint:
                                return coin
                
                # Recursive search
                result = self._search_for_mint_in_data(value, target_mint)
                if result:
                    return result
        
        elif isinstance(data, list):
            for item in data:
                result = self._search_for_mint_in_data(item, target_mint)
                if result:
                    return result
        
        return None

def main():
    """Main execution function."""
    tester = TokenLookupTester()
    
    print("🚀 Pump.fun Token Lookup Investigation")
    print("=" * 80)
    print(f"Target Mint: {tester.target_mint}")
    print()
    
    # Test direct endpoint patterns
    successful_endpoints = tester.test_all_endpoints()
    
    # Test searching in general listings
    search_results = tester.test_search_in_listings()
    
    print("\n📊 INVESTIGATION SUMMARY")
    print("=" * 80)
    print(f"Direct endpoints tested: {len(tester.base_urls) * len(tester.endpoint_patterns)}")
    print(f"Successful direct endpoints: {len(successful_endpoints)}")
    print(f"Search endpoints tested: 6")
    print(f"Endpoints where token was found: {len(search_results)}")
    
    if successful_endpoints:
        print(f"\n✅ DIRECT ACCESS ENDPOINTS FOUND:")
        for endpoint in successful_endpoints:
            print(f"  🔗 {endpoint['url']}")
    
    if search_results:
        print(f"\n✅ TOKEN FOUND IN LISTINGS:")
        for result in search_results:
            print(f"  🔗 {result['endpoint']}")
            print(f"     Params: {result['params']}")
    
    if not successful_endpoints and not search_results:
        print(f"\n❌ No direct token lookup endpoint found.")
        print(f"💡 Recommendation: Use general listing endpoints and filter by mint address.")
    
    print(f"\n🎯 Next steps:")
    print(f"1. Check saved response files for detailed token data")
    print(f"2. If found in listings, implement client-side filtering")
    print(f"3. Consider web scraping pump.fun/{tester.target_mint} page")

if __name__ == "__main__":
    main()
