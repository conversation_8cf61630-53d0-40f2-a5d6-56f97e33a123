# Cipher-Spy Chrome Extension Installation Guide

## 🚀 Quick Installation

### Step 1: Prepare the Extension
1. **Ensure all files are present:**
   ```
   chrome_extension/
   ├── manifest.json
   ├── background.js
   ├── content.js
   ├── popup/
   │   ├── popup.html
   │   ├── popup.css
   │   └── popup.js
   ├── devtools/
   │   ├── devtools.html
   │   ├── devtools.js
   │   ├── panel.html
   │   ├── panel.css
   │   └── panel.js
   └── icons/
       ├── icon16.png
       ├── icon32.png
       ├── icon48.png
       └── icon128.png
   ```

2. **Create icons if missing:**
   ```bash
   # Run from the project root
   python create_extension_icons.py
   ```

### Step 2: Install in Chrome
1. **Open Chrome Extensions page:**
   - Go to `chrome://extensions/`
   - Or: Menu → More Tools → Extensions

2. **Enable Developer Mode:**
   - Toggle "Developer mode" in the top-right corner

3. **Load the Extension:**
   - Click "Load unpacked"
   - Select the `chrome_extension/` folder
   - Click "Select Folder"

4. **Verify Installation:**
   - You should see "Cipher-Spy Network Monitor" in your extensions list
   - The extension icon should appear in your Chrome toolbar

### Step 3: Start the Backend (Optional)
For full functionality, start the Cipher-Spy backend:

```bash
# From project root
python -m src.main
```

The backend runs on `http://localhost:8000` by default.

## 🔧 Configuration

### Extension Settings
Click the extension icon and go to Settings to configure:

- **Backend URL**: Default is `http://localhost:8000`
- **Capture Headers**: Enable/disable header capture
- **Capture Payloads**: Enable/disable request body capture
- **Filter Patterns**: URLs to monitor (e.g., `/api/`, `.json`)
- **Exclude Patterns**: URLs to ignore (e.g., `.css`, `.js`)

### Permissions Explained
The extension requests these permissions:

- **activeTab**: Access current tab for monitoring
- **storage**: Save settings and captured data
- **webRequest**: Intercept network requests
- **webNavigation**: Track page navigation
- **scripting**: Inject monitoring scripts
- **tabs**: Manage tab information
- **debugger**: Advanced network monitoring

## 🎯 Usage

### Basic Monitoring
1. **Start Monitoring:**
   - Click the Cipher-Spy extension icon
   - Click "Start Monitoring"
   - Browse any website

2. **View Captured Traffic:**
   - Extension popup shows real-time statistics
   - Click on request counts to see details

3. **Export Data:**
   - Click "Export Data" in the popup
   - Choose from JSON, HAR, or OpenAPI formats

### Advanced Analysis (DevTools)
1. **Open DevTools:**
   - Press F12 or right-click → Inspect
   - Look for "Cipher-Spy" tab in DevTools

2. **DevTools Features:**
   - **Requests Tab**: Detailed request/response analysis
   - **Analysis Tab**: Technology detection and patterns
   - **Discovery Tab**: Trigger API discovery
   - **Documentation Tab**: Generate API documentation

### Integration with Backend
1. **Automatic Analysis:**
   - Enable "Auto Analysis" in settings
   - Requests are automatically analyzed by the backend

2. **Manual Triggers:**
   - Click "Analyze Session" in popup
   - Use "Start API Discovery" in DevTools

## 🔍 Troubleshooting

### Extension Won't Load
- **Check file structure**: Ensure all files are present
- **Verify manifest.json**: Check for syntax errors
- **Create icons**: Run `python create_extension_icons.py`
- **Check permissions**: Ensure Chrome allows loading unpacked extensions

### No Network Traffic Captured
- **Check permissions**: Extension needs webRequest permission
- **Verify filters**: Check filter patterns in settings
- **Restart monitoring**: Stop and start monitoring again
- **Reload page**: Some requests only appear on page reload

### Backend Connection Issues
- **Check backend URL**: Verify in extension settings
- **Start backend**: Run `python -m src.main`
- **Check CORS**: Backend should allow extension origins
- **Firewall**: Ensure localhost:8000 is accessible

### DevTools Panel Missing
- **Reload extension**: Disable and re-enable in chrome://extensions/
- **Restart Chrome**: Sometimes required for DevTools panels
- **Check console**: Look for errors in extension console

## 🛡️ Security & Privacy

### Data Handling
- **Local Storage**: All data stored locally in Chrome
- **No External Transmission**: Data only sent to configured backend
- **User Control**: Full control over what gets captured
- **Temporary Storage**: Data cleared when extension is disabled

### Best Practices
- **Use on Authorized Sites**: Only monitor sites you own or have permission to test
- **Sensitive Data**: Be careful with authentication tokens and personal data
- **Rate Limiting**: Extension respects rate limits to avoid overloading servers
- **Regular Cleanup**: Clear captured data regularly

## 🔄 Updates

### Manual Updates
1. Download new extension files
2. Replace files in chrome_extension/ folder
3. Go to chrome://extensions/
4. Click reload button for Cipher-Spy extension

### Version Checking
- Check manifest.json for current version
- Compare with latest release notes
- Backup settings before updating

## 🆘 Support

### Getting Help
- **Console Logs**: Check browser console for errors
- **Extension Console**: Right-click extension → Inspect popup
- **Backend Logs**: Check backend console output
- **GitHub Issues**: Report bugs and feature requests

### Common Issues
1. **"Could not load manifest"**: Check manifest.json syntax
2. **Icons not loading**: Run icon creation script
3. **No requests captured**: Check permissions and filters
4. **Backend connection failed**: Verify backend is running

### Debug Mode
Enable debug logging:
1. Open extension popup
2. Right-click → Inspect
3. Check console for detailed logs

## 📚 Additional Resources

- **Main Documentation**: README_UNIVERSAL.md
- **API Documentation**: Backend API endpoints
- **Development Guide**: Contributing to the extension
- **Security Guide**: Best practices for safe usage

---

**Need Help?** Check the troubleshooting section or open an issue on GitHub.
