#!/usr/bin/env python3
"""
Documentation Generator for Cipher-Spy Universal API Discovery

Generates comprehensive API documentation from discovery results,
including OpenAPI specifications, human-readable guides, and
business intelligence reports.
"""

import json
import yaml
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from .base_analyzer import DiscoveryResult, APISchema


class DocumentationGenerator:
    """
    Generates comprehensive documentation for discovered APIs.
    
    Creates multiple documentation formats including:
    - OpenAPI 3.0 specifications
    - Human-readable API guides
    - Business intelligence reports
    - Technical implementation guides
    """

    def __init__(self, results_dir: Path):
        """
        Initialize the documentation generator.
        
        Args:
            results_dir: Directory to save documentation
        """
        self.results_dir = results_dir
        self.docs_dir = results_dir / "documentation"
        self.docs_dir.mkdir(exist_ok=True)

    async def generate_comprehensive_docs(self, result: DiscoveryResult):
        """
        Generate all documentation formats for discovery results.
        
        Args:
            result: Discovery results to document
        """
        print("📚 Generating comprehensive documentation...")
        
        # Generate OpenAPI specification
        await self._generate_openapi_spec(result)
        print("   ✅ OpenAPI 3.0 specification generated")
        
        # Generate human-readable guide
        await self._generate_api_guide(result)
        print("   ✅ API guide generated")
        
        # Generate business intelligence report
        await self._generate_business_report(result)
        print("   ✅ Business intelligence report generated")
        
        # Generate technical implementation guide
        await self._generate_implementation_guide(result)
        print("   ✅ Implementation guide generated")
        
        # Generate summary dashboard
        await self._generate_summary_dashboard(result)
        print("   ✅ Summary dashboard generated")

    async def _generate_openapi_spec(self, result: DiscoveryResult):
        """Generate OpenAPI 3.0 specification."""
        spec = {
            "openapi": "3.0.3",
            "info": {
                "title": f"{result.target_domain} API",
                "description": f"Auto-discovered API specification for {result.target_domain}",
                "version": "1.0.0",
                "contact": {
                    "name": "Cipher-Spy Discovery",
                    "url": "https://github.com/cipher-spy"
                },
                "x-discovery-metadata": {
                    "discovered_at": result.timestamp,
                    "discovery_duration": result.total_duration,
                    "endpoints_discovered": len(result.discovered_endpoints),
                    "confidence_score": self._calculate_overall_confidence(result)
                }
            },
            "servers": [
                {
                    "url": f"https://{result.target_domain}",
                    "description": "Production server"
                }
            ],
            "paths": {},
            "components": {
                "schemas": {},
                "parameters": {},
                "responses": {}
            },
            "tags": []
        }
        
        # Add paths for each discovered endpoint
        for endpoint in result.discovered_endpoints:
            path_item = self._create_openapi_path_item(endpoint)
            
            # Extract path from URL
            url_path = endpoint.endpoint_url.replace(f"https://{result.target_domain}", "")
            if not url_path.startswith("/"):
                url_path = "/" + url_path
            
            spec["paths"][url_path] = path_item
        
        # Add schemas from discovered endpoints
        for endpoint in result.discovered_endpoints:
            if hasattr(endpoint, 'response_schema') and endpoint.response_schema:
                schema_name = f"{endpoint.name}Response"
                spec["components"]["schemas"][schema_name] = self._convert_to_openapi_schema(
                    endpoint.response_schema
                )
        
        # Save OpenAPI spec in both JSON and YAML formats
        spec_json_file = self.docs_dir / "openapi_spec.json"
        with open(spec_json_file, 'w') as f:
            json.dump(spec, f, indent=2)
        
        spec_yaml_file = self.docs_dir / "openapi_spec.yaml"
        with open(spec_yaml_file, 'w') as f:
            yaml.dump(spec, f, default_flow_style=False, sort_keys=False)

    def _create_openapi_path_item(self, endpoint: APISchema) -> Dict[str, Any]:
        """Create OpenAPI path item for an endpoint."""
        operation = {
            "summary": endpoint.name.replace('_', ' ').title(),
            "description": getattr(endpoint, 'description', f"Auto-discovered endpoint: {endpoint.name}"),
            "operationId": endpoint.name,
            "tags": [self._extract_tag_from_endpoint(endpoint)],
            "parameters": [],
            "responses": {
                "200": {
                    "description": "Successful response",
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": f"#/components/schemas/{endpoint.name}Response"
                            }
                        }
                    }
                }
            },
            "x-discovery-metadata": {
                "confidence_score": getattr(endpoint, 'confidence_score', 0.0),
                "discovered_parameters": list(endpoint.parameters.keys()) if endpoint.parameters else [],
                "business_value": getattr(endpoint, 'business_value', 'Unknown')
            }
        }
        
        # Add parameters
        for param_name, param_info in endpoint.parameters.items():
            parameter = {
                "name": param_name,
                "in": "query",
                "description": f"Auto-discovered parameter: {param_name}",
                "required": False,
                "schema": self._infer_parameter_schema(param_info)
            }
            operation["parameters"].append(parameter)
        
        return {endpoint.method.lower(): operation}

    def _extract_tag_from_endpoint(self, endpoint: APISchema) -> str:
        """Extract a tag name from endpoint information."""
        # Try to extract meaningful tag from endpoint name or URL
        name_parts = endpoint.name.split('_')
        if len(name_parts) > 1:
            return name_parts[0].title()
        
        # Extract from URL path
        url_parts = endpoint.endpoint_url.split('/')
        for part in url_parts:
            if part and not part.startswith('http') and len(part) > 2:
                return part.title()
        
        return "General"

    def _infer_parameter_schema(self, param_info: Dict[str, Any]) -> Dict[str, Any]:
        """Infer OpenAPI schema for a parameter."""
        param_type = param_info.get('type', 'string')
        
        schema = {"type": "string"}  # Default
        
        if param_type in ['int', 'integer']:
            schema = {"type": "integer"}
        elif param_type in ['float', 'number']:
            schema = {"type": "number"}
        elif param_type in ['bool', 'boolean']:
            schema = {"type": "boolean"}
        elif param_type == 'list':
            schema = {"type": "array", "items": {"type": "string"}}
        
        # Add examples if available
        if 'examples' in param_info and param_info['examples']:
            schema['example'] = param_info['examples'][0]
        
        return schema

    def _convert_to_openapi_schema(self, response_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Convert internal schema to OpenAPI schema format."""
        schema = {
            "type": "object",
            "properties": {},
            "x-discovery-metadata": {
                "sample_count": response_schema.get('sample_count', 0),
                "confidence_score": response_schema.get('confidence_score', 0.0)
            }
        }
        
        fields = response_schema.get('fields', {})
        for field_name, field_info in fields.items():
            # Convert nested field names (e.g., "data.items[0].name") to nested schema
            if '.' not in field_name and '[' not in field_name:
                schema["properties"][field_name] = self._convert_field_to_schema(field_info)
        
        return schema

    def _convert_field_to_schema(self, field_info: Dict[str, Any]) -> Dict[str, Any]:
        """Convert field information to OpenAPI schema."""
        field_type = field_info.get('type', 'string')
        
        schema = {"type": "string"}  # Default
        
        if field_type in ['int', 'integer']:
            schema = {"type": "integer"}
        elif field_type in ['float', 'number']:
            schema = {"type": "number"}
        elif field_type in ['bool', 'boolean']:
            schema = {"type": "boolean"}
        elif field_type == 'list':
            schema = {"type": "array", "items": {"type": "string"}}
        elif field_type == 'dict':
            schema = {"type": "object"}
        
        # Add examples and description
        if 'examples' in field_info and field_info['examples']:
            schema['example'] = field_info['examples'][0]
        
        if field_info.get('occurrence_count', 0) > 0:
            schema['description'] = f"Appears in {field_info['occurrence_count']} samples"
        
        return schema

    def _calculate_overall_confidence(self, result: DiscoveryResult) -> float:
        """Calculate overall confidence score for the discovery."""
        if not result.discovered_endpoints:
            return 0.0
        
        total_confidence = sum(
            getattr(endpoint, 'confidence_score', 0.0) 
            for endpoint in result.discovered_endpoints
        )
        
        return total_confidence / len(result.discovered_endpoints)

    async def _generate_api_guide(self, result: DiscoveryResult):
        """Generate human-readable API guide."""
        guide_content = f"""# {result.target_domain} API Guide

*Auto-generated by Cipher-Spy Universal API Discovery*

## Overview

This guide documents the API endpoints discovered for **{result.target_domain}** through automated analysis.

**Discovery Summary:**
- **Target Domain:** {result.target_domain}
- **Discovery Date:** {result.timestamp}
- **Analysis Duration:** {result.total_duration:.2f} seconds
- **Endpoints Discovered:** {len(result.discovered_endpoints)}
- **Parameters Tested:** {len(result.parameter_tests)}
- **Success Rate:** {len([t for t in result.parameter_tests if t.success]) / len(result.parameter_tests) * 100 if result.parameter_tests else 0:.1f}%

## Discovered Endpoints

"""
        
        for i, endpoint in enumerate(result.discovered_endpoints, 1):
            guide_content += f"""### {i}. {endpoint.name.replace('_', ' ').title()}

**URL:** `{endpoint.endpoint_url}`  
**Method:** `{endpoint.method}`  
**Confidence Score:** {getattr(endpoint, 'confidence_score', 0.0):.2f}/1.0

"""
            
            if hasattr(endpoint, 'description') and endpoint.description:
                guide_content += f"**Description:** {endpoint.description}\n\n"
            
            if endpoint.parameters:
                guide_content += "**Parameters:**\n"
                for param_name, param_info in endpoint.parameters.items():
                    param_type = param_info.get('type', 'string')
                    examples = param_info.get('examples', [])
                    example_text = f" (e.g., `{examples[0]}`)" if examples else ""
                    guide_content += f"- `{param_name}` ({param_type}){example_text}\n"
                guide_content += "\n"
            
            # Add example request
            guide_content += f"""**Example Request:**
```bash
curl -X {endpoint.method} "{endpoint.endpoint_url}"
```

"""
            
            if hasattr(endpoint, 'business_value') and endpoint.business_value:
                guide_content += f"**Business Value:** {endpoint.business_value}\n\n"
            
            guide_content += "---\n\n"
        
        # Add business intelligence section
        if result.business_intelligence:
            guide_content += """## Business Intelligence

"""
            bi = result.business_intelligence
            
            if 'domain_insights' in bi:
                guide_content += "### Domain Insights\n\n"
                for endpoint_name, insights in bi['domain_insights'].items():
                    if insights:
                        guide_content += f"**{endpoint_name}:**\n"
                        if 'business_entities' in insights:
                            entities = insights['business_entities']
                            if entities:
                                guide_content += f"- Business Entities: {', '.join(entities)}\n"
                        if 'key_metrics' in insights:
                            metrics = insights['key_metrics']
                            if metrics:
                                guide_content += f"- Key Metrics: {', '.join(metrics[:5])}\n"  # Show first 5
                        guide_content += "\n"
        
        # Add performance metrics
        if result.performance_metrics:
            guide_content += """## Performance Metrics

"""
            perf = result.performance_metrics
            
            if 'response_times' in perf:
                rt = perf['response_times']
                guide_content += f"""**Response Times:**
- Average: {rt.get('average', 0):.3f}s
- Min: {rt.get('min', 0):.3f}s
- Max: {rt.get('max', 0):.3f}s

"""
            
            if 'success_rates' in perf:
                sr = perf['success_rates']
                guide_content += f"""**Success Rates:**
- Overall Success Rate: {sr.get('overall', 0) * 100:.1f}%
- Total Tests: {sr.get('total_tests', 0)}
- Successful Tests: {sr.get('successful_tests', 0)}

"""
        
        guide_content += """## Usage Notes

1. **Rate Limiting:** Be respectful of the target server's resources
2. **Authentication:** Some endpoints may require authentication
3. **Error Handling:** Implement proper error handling for production use
4. **Documentation:** This is auto-generated documentation - verify endpoints before production use

---

*Generated by Cipher-Spy Universal API Discovery Framework*
"""
        
        guide_file = self.docs_dir / "api_guide.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)

    async def _generate_business_report(self, result: DiscoveryResult):
        """Generate business intelligence report."""
        report = {
            "executive_summary": {
                "target_domain": result.target_domain,
                "discovery_date": result.timestamp,
                "key_findings": self._extract_key_findings(result),
                "business_value_assessment": self._assess_business_value(result),
                "competitive_insights": self._extract_competitive_insights(result)
            },
            "technical_analysis": {
                "api_architecture": self._analyze_api_architecture(result),
                "data_structures": self._analyze_data_structures(result),
                "integration_complexity": self._assess_integration_complexity(result)
            },
            "recommendations": {
                "immediate_opportunities": self._identify_opportunities(result),
                "integration_strategy": self._suggest_integration_strategy(result),
                "monitoring_recommendations": self._suggest_monitoring(result)
            },
            "appendix": {
                "detailed_endpoints": [asdict(ep) for ep in result.discovered_endpoints],
                "discovery_metadata": result.discovery_metadata
            }
        }
        
        report_file = self.docs_dir / "business_intelligence_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

    def _extract_key_findings(self, result: DiscoveryResult) -> List[str]:
        """Extract key findings from discovery results."""
        findings = []
        
        findings.append(f"Discovered {len(result.discovered_endpoints)} API endpoints")
        
        if result.parameter_tests:
            success_rate = len([t for t in result.parameter_tests if t.success]) / len(result.parameter_tests) * 100
            findings.append(f"Parameter discovery success rate: {success_rate:.1f}%")
        
        # Analyze endpoint patterns
        endpoint_urls = [ep.endpoint_url for ep in result.discovered_endpoints]
        if any('/api/' in url for url in endpoint_urls):
            findings.append("Standard REST API structure detected")
        
        if any('graphql' in url.lower() for url in endpoint_urls):
            findings.append("GraphQL endpoint discovered")
        
        return findings

    def _assess_business_value(self, result: DiscoveryResult) -> Dict[str, Any]:
        """Assess business value of discovered APIs."""
        assessment = {
            "data_richness": "Medium",  # Default
            "integration_potential": "Medium",
            "competitive_advantage": "Medium"
        }
        
        # Assess based on number of endpoints and parameters
        if len(result.discovered_endpoints) > 10:
            assessment["data_richness"] = "High"
        elif len(result.discovered_endpoints) < 3:
            assessment["data_richness"] = "Low"
        
        return assessment

    def _extract_competitive_insights(self, result: DiscoveryResult) -> List[str]:
        """Extract competitive insights."""
        insights = []
        
        # Analyze business intelligence data
        if result.business_intelligence and 'domain_insights' in result.business_intelligence:
            entities = set()
            for endpoint_insights in result.business_intelligence['domain_insights'].values():
                if 'business_entities' in endpoint_insights:
                    entities.update(endpoint_insights['business_entities'])
            
            if entities:
                insights.append(f"Business entities identified: {', '.join(list(entities)[:5])}")
        
        return insights

    def _analyze_api_architecture(self, result: DiscoveryResult) -> Dict[str, Any]:
        """Analyze API architecture patterns."""
        return {
            "endpoint_count": len(result.discovered_endpoints),
            "methods_used": list(set(ep.method for ep in result.discovered_endpoints)),
            "url_patterns": self._extract_url_patterns(result)
        }

    def _extract_url_patterns(self, result: DiscoveryResult) -> List[str]:
        """Extract common URL patterns."""
        patterns = set()
        
        for endpoint in result.discovered_endpoints:
            url_parts = endpoint.endpoint_url.split('/')
            for part in url_parts:
                if part and not part.startswith('http') and len(part) > 2:
                    patterns.add(part)
        
        return list(patterns)[:10]  # Return top 10 patterns

    def _analyze_data_structures(self, result: DiscoveryResult) -> Dict[str, Any]:
        """Analyze data structures from discovered APIs."""
        return {
            "total_fields_discovered": sum(
                len(ep.response_schema.get('fields', {})) 
                for ep in result.discovered_endpoints 
                if hasattr(ep, 'response_schema') and ep.response_schema
            ),
            "schema_complexity": "Medium"  # Could be enhanced with actual analysis
        }

    def _assess_integration_complexity(self, result: DiscoveryResult) -> str:
        """Assess integration complexity."""
        if len(result.discovered_endpoints) > 20:
            return "High"
        elif len(result.discovered_endpoints) > 5:
            return "Medium"
        else:
            return "Low"

    def _identify_opportunities(self, result: DiscoveryResult) -> List[str]:
        """Identify immediate opportunities."""
        opportunities = []
        
        if len(result.discovered_endpoints) > 0:
            opportunities.append("API integration opportunities identified")
        
        if result.parameter_tests:
            opportunities.append("Parameter optimization potential discovered")
        
        return opportunities

    def _suggest_integration_strategy(self, result: DiscoveryResult) -> List[str]:
        """Suggest integration strategy."""
        return [
            "Start with high-confidence endpoints",
            "Implement proper error handling",
            "Monitor rate limits and performance",
            "Validate data schemas before production use"
        ]

    def _suggest_monitoring(self, result: DiscoveryResult) -> List[str]:
        """Suggest monitoring recommendations."""
        return [
            "Monitor endpoint availability",
            "Track response time performance",
            "Watch for schema changes",
            "Set up alerts for API failures"
        ]

    async def _generate_implementation_guide(self, result: DiscoveryResult):
        """Generate technical implementation guide."""
        guide_content = f"""# Implementation Guide for {result.target_domain} API

## Quick Start

### 1. Authentication
```python
# Add authentication headers if required
headers = {{
    'Authorization': 'Bearer YOUR_API_KEY',
    'User-Agent': 'Your-App/1.0'
}}
```

### 2. Basic Usage
```python
import requests

base_url = "https://{result.target_domain}"

# Example endpoint calls
"""
        
        for endpoint in result.discovered_endpoints[:3]:  # Show first 3 endpoints
            guide_content += f"""
# {endpoint.name}
response = requests.{endpoint.method.lower()}(
    "{endpoint.endpoint_url}",
    headers=headers
)
data = response.json()
"""
        
        guide_content += """

### 3. Error Handling
```python
try:
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    data = response.json()
except requests.exceptions.RequestException as e:
    print(f"API request failed: {e}")
```

### 4. Rate Limiting
```python
import time

# Implement rate limiting
time.sleep(1)  # Wait 1 second between requests
```

## Production Considerations

1. **Error Handling:** Implement comprehensive error handling
2. **Rate Limiting:** Respect API rate limits
3. **Caching:** Cache responses when appropriate
4. **Monitoring:** Monitor API health and performance
5. **Security:** Secure API keys and sensitive data

## Testing

Use the generated test suite to validate API functionality:

```bash
python test_api_client.py
```
"""
        
        impl_file = self.docs_dir / "implementation_guide.md"
        with open(impl_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)

    async def _generate_summary_dashboard(self, result: DiscoveryResult):
        """Generate HTML summary dashboard."""
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>{result.target_domain} API Discovery Dashboard</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .metric {{ display: inline-block; margin: 10px; padding: 15px; background: #e8f4f8; border-radius: 5px; }}
        .endpoint {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .confidence-high {{ border-left: 5px solid #4CAF50; }}
        .confidence-medium {{ border-left: 5px solid #FF9800; }}
        .confidence-low {{ border-left: 5px solid #f44336; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{result.target_domain} API Discovery Results</h1>
        <p>Generated by Cipher-Spy on {result.timestamp}</p>
    </div>
    
    <div class="metrics">
        <div class="metric">
            <h3>{len(result.discovered_endpoints)}</h3>
            <p>Endpoints Discovered</p>
        </div>
        <div class="metric">
            <h3>{len(result.parameter_tests)}</h3>
            <p>Parameters Tested</p>
        </div>
        <div class="metric">
            <h3>{result.total_duration:.1f}s</h3>
            <p>Discovery Duration</p>
        </div>
    </div>
    
    <h2>Discovered Endpoints</h2>
"""
        
        for endpoint in result.discovered_endpoints:
            confidence = getattr(endpoint, 'confidence_score', 0.0)
            confidence_class = 'confidence-high' if confidence > 0.7 else 'confidence-medium' if confidence > 0.3 else 'confidence-low'
            
            html_content += f"""
    <div class="endpoint {confidence_class}">
        <h3>{endpoint.name}</h3>
        <p><strong>URL:</strong> <code>{endpoint.endpoint_url}</code></p>
        <p><strong>Method:</strong> {endpoint.method}</p>
        <p><strong>Confidence:</strong> {confidence:.2f}</p>
    </div>
"""
        
        html_content += """
</body>
</html>"""
        
        dashboard_file = self.docs_dir / "dashboard.html"
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
