#!/usr/bin/env python3
"""
Minimal test to verify <PERSON><PERSON> is working.
"""

import asyncio
from playwright.async_api import async_playwright

async def test_playwright():
    """Test basic Playwright functionality."""
    print("🧪 Testing Playwright...")
    
    try:
        # Start Playwright
        playwright = await async_playwright().start()
        print("✅ Playwright started")
        
        # Launch browser
        browser = await playwright.chromium.launch(
            headless=True,
            args=['--no-sandbox']
        )
        print("✅ Browser launched")
        
        # Create page
        page = await browser.new_page()
        print("✅ Page created")
        
        # Navigate to test site
        print("🌐 Navigating to httpbin.org...")
        response = await page.goto("https://httpbin.org/html", timeout=30000)
        print(f"✅ Page loaded with status: {response.status}")
        
        # Get page title
        title = await page.title()
        print(f"📄 Page title: {title}")
        
        # Get page content length
        content = await page.content()
        print(f"📊 Content length: {len(content)} characters")
        
        # Cleanup
        await page.close()
        await browser.close()
        await playwright.stop()
        print("✅ Cleanup complete")
        
        print("\n🎉 Playwright test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Playwright test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    print("🔬 Minimal Playwright Test")
    print("="*30)
    
    success = await test_playwright()
    
    if success:
        print("\n✅ All tests passed! Playwright is working correctly.")
        print("You can now run the full Cipher-Spy crawler.")
        return 0
    else:
        print("\n❌ Tests failed! Please check Playwright installation.")
        print("Try running: python setup_playwright.py")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
