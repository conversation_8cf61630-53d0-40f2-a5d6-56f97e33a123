#!/usr/bin/env python3
"""
Pump.fun API Tester

Direct testing of known pump.fun APIs based on our reverse engineering.
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional


class PumpFunAPITester:
    """Test pump.fun APIs directly."""
    
    def __init__(self):
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'Referer': 'https://pump.fun/'
        }
        
        # Known API endpoints from our discovery
        self.api_endpoints = {
            'trending_coins': {
                'url': 'https://frontend-api-v3.pump.fun/coins',
                'method': 'GET',
                'params': {
                    'offset': 0,
                    'limit': 50,
                    'sort': 'market_cap',
                    'order': 'DESC',
                    'includeNsfw': 'false'
                },
                'description': 'Get trending coins by market cap'
            },
            'for_you_coins': {
                'url': 'https://frontend-api-v3.pump.fun/coins/for-you',
                'method': 'GET',
                'params': {
                    'offset': 0,
                    'limit': 48,
                    'includeNsfw': 'false'
                },
                'description': 'Get personalized coin recommendations'
            },
            'search_coins': {
                'url': 'https://frontend-api-v3.pump.fun/search',
                'method': 'GET',
                'params': {
                    'q': 'bitcoin',
                    'limit': 20
                },
                'description': 'Search for coins/tokens'
            },
            'coin_details': {
                'url': 'https://frontend-api-v3.pump.fun/coins/{coin_address}',
                'method': 'GET',
                'params': {},
                'description': 'Get details for a specific coin'
            },
            'pump_fun_flags': {
                'url': 'https://pump.fun/api/flags',
                'method': 'GET',
                'params': {},
                'description': 'Get pump.fun feature flags'
            },
            'pump_fun_runners': {
                'url': 'https://pump.fun/api/runners',
                'method': 'GET',
                'params': {},
                'description': 'Get pump.fun runners data'
            }
        }
    
    def test_all_apis(self) -> Dict[str, Any]:
        """Test all known APIs."""
        print("🧪 Pump.fun API Testing Suite")
        print("="*50)
        print("Testing known APIs discovered through reverse engineering...")
        print()
        
        results = {}
        
        for api_name, api_config in self.api_endpoints.items():
            print(f"🔍 Testing {api_name}...")
            print(f"   📄 {api_config['description']}")
            
            try:
                result = self._test_api(api_name, api_config)
                results[api_name] = result
                
                if result['success']:
                    print(f"   ✅ Success! Status: {result['status_code']}")
                    print(f"   📊 Response size: {result['response_size']} bytes")
                    
                    # Show preview of JSON data
                    if result.get('json_data'):
                        self._show_json_preview(result['json_data'])
                else:
                    print(f"   ❌ Failed! Status: {result['status_code']}")
                    print(f"   💬 Error: {result.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"   💥 Exception: {e}")
                results[api_name] = {
                    'success': False,
                    'error': str(e),
                    'exception': True
                }
            
            print()
            time.sleep(1)  # Rate limiting
        
        # Generate summary
        self._print_summary(results)
        
        return results
    
    def _test_api(self, api_name: str, api_config: Dict[str, Any]) -> Dict[str, Any]:
        """Test a single API endpoint."""
        url = api_config['url']
        method = api_config['method']
        params = api_config.get('params', {})
        
        # Handle parameterized URLs (like coin details)
        if '{coin_address}' in url:
            # Use a known coin address for testing
            url = url.replace('{coin_address}', 'So11111111111111111111111111111111111111112')  # Wrapped SOL
        
        try:
            if method == 'GET':
                response = requests.get(
                    url,
                    params=params,
                    headers=self.base_headers,
                    timeout=15
                )
            elif method == 'POST':
                response = requests.post(
                    url,
                    json=params,
                    headers=self.base_headers,
                    timeout=15
                )
            else:
                return {
                    'success': False,
                    'error': f'Unsupported method: {method}'
                }
            
            # Parse response
            result = {
                'success': 200 <= response.status_code < 300,
                'status_code': response.status_code,
                'response_size': len(response.content),
                'content_type': response.headers.get('content-type', ''),
                'headers': dict(response.headers),
                'url_called': response.url
            }
            
            # Try to parse JSON
            try:
                if 'application/json' in result['content_type']:
                    result['json_data'] = response.json()
                else:
                    result['text_data'] = response.text[:500]
            except:
                result['text_data'] = response.text[:500]
            
            if not result['success']:
                result['error'] = f"HTTP {response.status_code}"
                result['response_text'] = response.text[:200]
            
            return result
            
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Request timeout'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'Connection error'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _show_json_preview(self, data: Any, max_items: int = 3):
        """Show a preview of JSON data."""
        if isinstance(data, dict):
            print(f"   📋 JSON keys: {list(data.keys())}")
            
            # Show some sample values
            for i, (key, value) in enumerate(data.items()):
                if i >= max_items:
                    print(f"   ... and {len(data) - max_items} more keys")
                    break
                
                if isinstance(value, (str, int, float, bool)):
                    print(f"   • {key}: {value}")
                elif isinstance(value, list):
                    print(f"   • {key}: Array with {len(value)} items")
                elif isinstance(value, dict):
                    print(f"   • {key}: Object with {len(value)} keys")
        
        elif isinstance(data, list):
            print(f"   📋 Array with {len(data)} items")
            
            if len(data) > 0 and isinstance(data[0], dict):
                print(f"   📋 First item keys: {list(data[0].keys())}")
    
    def _print_summary(self, results: Dict[str, Any]):
        """Print test summary."""
        print("📊 TEST SUMMARY")
        print("="*30)
        
        successful = sum(1 for r in results.values() if r.get('success', False))
        total = len(results)
        
        print(f"✅ Successful: {successful}/{total} ({successful/total*100:.1f}%)")
        print(f"❌ Failed: {total-successful}/{total}")
        print()
        
        print("🎯 WORKING APIs:")
        for api_name, result in results.items():
            if result.get('success', False):
                status = result['status_code']
                size = result['response_size']
                print(f"  ✅ {api_name}: HTTP {status}, {size} bytes")
        
        print()
        print("❌ FAILED APIs:")
        for api_name, result in results.items():
            if not result.get('success', False):
                error = result.get('error', 'Unknown error')
                print(f"  ❌ {api_name}: {error}")
        
        print()
        print("🔧 USAGE EXAMPLES:")
        print("For working APIs, you can use them like this:")
        print()
        
        for api_name, result in results.items():
            if result.get('success', False):
                api_config = self.api_endpoints[api_name]
                print(f"# {api_name}")
                print(f"import requests")
                print(f"response = requests.get('{api_config['url']}', params={api_config['params']})")
                print(f"data = response.json()")
                print()
                break  # Just show one example
    
    def test_specific_api(self, api_name: str) -> Optional[Dict[str, Any]]:
        """Test a specific API by name."""
        if api_name not in self.api_endpoints:
            print(f"❌ Unknown API: {api_name}")
            print(f"Available APIs: {list(self.api_endpoints.keys())}")
            return None
        
        api_config = self.api_endpoints[api_name]
        print(f"🔍 Testing {api_name}...")
        print(f"📄 {api_config['description']}")
        
        result = self._test_api(api_name, api_config)
        
        if result['success']:
            print(f"✅ Success! Status: {result['status_code']}")
            if result.get('json_data'):
                print("📊 Response data:")
                print(json.dumps(result['json_data'], indent=2)[:1000])
        else:
            print(f"❌ Failed! Error: {result.get('error')}")
        
        return result
    
    def generate_curl_commands(self):
        """Generate cURL commands for all APIs."""
        print("🔧 cURL Commands for Pump.fun APIs")
        print("="*50)
        
        for api_name, api_config in self.api_endpoints.items():
            url = api_config['url']
            params = api_config.get('params', {})
            
            print(f"# {api_name}: {api_config['description']}")
            
            if params:
                param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                full_url = f"{url}?{param_string}"
            else:
                full_url = url
            
            curl_cmd = f"curl -X {api_config['method']} \\\n"
            curl_cmd += f"  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' \\\n"
            curl_cmd += f"  -H 'Accept: application/json' \\\n"
            curl_cmd += f"  '{full_url}'"
            
            print(curl_cmd)
            print()


def main():
    """Main entry point."""
    tester = PumpFunAPITester()
    
    print("🚀 Pump.fun API Reverse Engineering Test Suite")
    print("Testing APIs discovered through autonomous navigation...")
    print()
    
    # Test all APIs
    results = tester.test_all_apis()
    
    # Generate cURL commands
    print("\n" + "="*70)
    tester.generate_curl_commands()
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f"pump_fun_api_test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"💾 Test results saved to: {results_file}")
    
    return 0


if __name__ == "__main__":
    exit(main())
