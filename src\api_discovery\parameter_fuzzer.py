#!/usr/bin/env python3
"""
Parameter Fuzzer for Cipher-Spy Universal API Discovery

Advanced parameter discovery and fuzzing system that intelligently
tests API endpoints to discover hidden parameters, validate inputs,
and understand parameter behavior patterns.
"""

import asyncio
import itertools
import random
import string
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime, timedelta

from .base_analyzer import ParameterTest
from .target_config import TargetConfig


@dataclass
class FuzzingResult:
    """Result of parameter fuzzing operation."""
    endpoint_url: str
    total_tests: int
    successful_tests: int
    discovered_parameters: List[str]
    parameter_behaviors: Dict[str, Dict[str, Any]]
    error_patterns: Dict[str, List[str]]
    rate_limit_info: Dict[str, Any]
    fuzzing_duration: float
    timestamp: str


class ParameterFuzzer:
    """
    Advanced parameter fuzzing system for API discovery.
    
    This class implements intelligent parameter discovery strategies
    including systematic testing, behavioral analysis, and error pattern
    recognition to comprehensively understand API parameter spaces.
    """

    def __init__(self, target_config: TargetConfig, session):
        """
        Initialize the parameter fuzzer.
        
        Args:
            target_config: Target configuration
            session: HTTP session for making requests
        """
        self.target_config = target_config
        self.session = session
        
        # Fuzzing configuration
        self.max_tests_per_endpoint = 1000
        self.timeout_seconds = 10.0
        self.rate_limit_delay = target_config.rate_limit_delay
        
        # Parameter discovery strategies
        self.discovery_strategies = [
            'systematic_enumeration',
            'common_patterns',
            'behavioral_analysis',
            'error_based_discovery',
            'combination_testing'
        ]
        
        # Advanced parameter sets for fuzzing
        self.advanced_parameter_sets = {
            'authentication': [
                'api_key', 'apikey', 'key', 'token', 'auth', 'authorization',
                'access_token', 'bearer', 'jwt', 'session', 'sid'
            ],
            'pagination_advanced': [
                'page', 'p', 'pagenum', 'pageNumber', 'currentPage',
                'limit', 'l', 'size', 'pageSize', 'perPage', 'count',
                'offset', 'skip', 'start', 'begin', 'from', 'index',
                'cursor', 'next_cursor', 'prev_cursor', 'continuation',
                'bookmark', 'marker', 'after', 'before', 'since'
            ],
            'filtering_advanced': [
                'filter', 'filters', 'where', 'q', 'query', 'search',
                'find', 'match', 'contains', 'like', 'regex',
                'eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'nin',
                'between', 'range', 'min', 'max', 'minValue', 'maxValue'
            ],
            'sorting_advanced': [
                'sort', 'sortBy', 'sortField', 'orderBy', 'order',
                'direction', 'dir', 'asc', 'desc', 'ascending', 'descending',
                'reverse', 'sortOrder', 'sortDirection'
            ],
            'format_control': [
                'format', 'fmt', 'output', 'type', 'contentType',
                'accept', 'response_type', 'encoding', 'charset',
                'pretty', 'indent', 'minify', 'compress'
            ],
            'versioning': [
                'version', 'v', 'ver', 'api_version', 'apiVersion',
                'revision', 'rev', 'build', 'release'
            ],
            'localization': [
                'lang', 'language', 'locale', 'region', 'country',
                'timezone', 'tz', 'culture', 'i18n', 'l10n'
            ],
            'debugging': [
                'debug', 'verbose', 'trace', 'log', 'profile',
                'benchmark', 'timing', 'explain', 'analyze'
            ],
            'caching': [
                'cache', 'no_cache', 'nocache', 'fresh', 'stale',
                'ttl', 'expires', 'max_age', 'etag', 'if_modified_since'
            ],
            'security': [
                'csrf', 'csrf_token', 'nonce', 'signature', 'hash',
                'checksum', 'hmac', 'timestamp', 'client_id'
            ]
        }
        
        # Advanced test values
        self.advanced_test_values = {
            'numeric_edge_cases': [
                0, 1, -1, 2147483647, -2147483648,  # 32-bit int limits
                9223372036854775807, -9223372036854775808,  # 64-bit int limits
                0.1, -0.1, 3.14159, 2.718281828
            ],
            'string_edge_cases': [
                '', ' ', '  ', '\t', '\n', '\r\n',
                'null', 'NULL', 'nil', 'undefined',
                'true', 'false', 'True', 'False',
                '0', '1', '-1', 'NaN', 'Infinity'
            ],
            'injection_tests': [
                "'; DROP TABLE users; --",
                '<script>alert("xss")</script>',
                '{{7*7}}', '${7*7}', '#{7*7}',
                '../../../etc/passwd',
                'file:///etc/passwd',
                'http://evil.com/malware'
            ],
            'unicode_tests': [
                'café', '北京', '🚀', '💻', '🔒',
                '\u0000', '\u001f', '\u007f', '\u0080',
                'Ω', 'π', '∞', '≠', '≤', '≥'
            ],
            'length_tests': [
                'a' * 1, 'a' * 10, 'a' * 100, 'a' * 1000, 'a' * 10000,
                'x' * 255, 'x' * 256, 'x' * 65535, 'x' * 65536
            ],
            'format_tests': [
                '2024-01-01', '01/01/2024', '2024-01-01T00:00:00Z',
                '1704067200', '1704067200000',  # Unix timestamps
                '<EMAIL>', 'http://example.com',
                '***********', '::1', 'localhost'
            ]
        }

    async def fuzz_endpoint(self, endpoint_url: str, endpoint_name: str) -> FuzzingResult:
        """
        Perform comprehensive parameter fuzzing on an endpoint.
        
        Args:
            endpoint_url: URL of the endpoint to fuzz
            endpoint_name: Name of the endpoint
            
        Returns:
            FuzzingResult: Comprehensive fuzzing results
        """
        start_time = datetime.now()
        print(f"🔬 Fuzzing endpoint: {endpoint_name}")
        
        all_tests = []
        discovered_parameters = set()
        parameter_behaviors = {}
        error_patterns = {}
        rate_limit_info = {}
        
        # Strategy 1: Systematic parameter enumeration
        print("   🎯 Systematic parameter enumeration...")
        systematic_tests = await self._systematic_parameter_enumeration(endpoint_url, endpoint_name)
        all_tests.extend(systematic_tests)
        
        # Strategy 2: Common pattern testing
        print("   📋 Common pattern testing...")
        pattern_tests = await self._common_pattern_testing(endpoint_url, endpoint_name)
        all_tests.extend(pattern_tests)
        
        # Strategy 3: Behavioral analysis
        print("   🧠 Behavioral analysis...")
        behavioral_results = await self._behavioral_analysis(endpoint_url, endpoint_name)
        all_tests.extend(behavioral_results['tests'])
        parameter_behaviors.update(behavioral_results['behaviors'])
        
        # Strategy 4: Error-based discovery
        print("   💥 Error-based discovery...")
        error_tests = await self._error_based_discovery(endpoint_url, endpoint_name)
        all_tests.extend(error_tests['tests'])
        error_patterns.update(error_tests['patterns'])
        
        # Strategy 5: Combination testing
        print("   🔗 Parameter combination testing...")
        combo_tests = await self._combination_testing(endpoint_url, endpoint_name, list(discovered_parameters))
        all_tests.extend(combo_tests)
        
        # Analyze results
        successful_tests = [test for test in all_tests if test.success]
        discovered_parameters = set(test.parameter_name for test in successful_tests)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        result = FuzzingResult(
            endpoint_url=endpoint_url,
            total_tests=len(all_tests),
            successful_tests=len(successful_tests),
            discovered_parameters=list(discovered_parameters),
            parameter_behaviors=parameter_behaviors,
            error_patterns=error_patterns,
            rate_limit_info=rate_limit_info,
            fuzzing_duration=duration,
            timestamp=start_time.isoformat()
        )
        
        print(f"   ✅ Fuzzing completed: {len(discovered_parameters)} parameters discovered")
        return result

    async def _systematic_parameter_enumeration(self, endpoint_url: str, endpoint_name: str) -> List[ParameterTest]:
        """Systematically test all parameter categories."""
        tests = []
        
        for category, parameters in self.advanced_parameter_sets.items():
            for param in parameters:
                for value_type, values in self.advanced_test_values.items():
                    if self._should_test_parameter_value_combination(param, value_type):
                        # Test with first few values from each type
                        for value in values[:2]:
                            test = await self._test_single_parameter(endpoint_url, param, value, endpoint_name)
                            tests.append(test)
                            
                            # Rate limiting
                            await asyncio.sleep(self.rate_limit_delay)
                            
                            # Break if we hit limits
                            if len(tests) >= self.max_tests_per_endpoint // 5:
                                break
                        
                        if len(tests) >= self.max_tests_per_endpoint // 5:
                            break
                    
                    if len(tests) >= self.max_tests_per_endpoint // 5:
                        break
                
                if len(tests) >= self.max_tests_per_endpoint // 5:
                    break
        
        return tests

    async def _common_pattern_testing(self, endpoint_url: str, endpoint_name: str) -> List[ParameterTest]:
        """Test common parameter patterns and conventions."""
        tests = []
        
        # Generate parameter names based on endpoint name
        endpoint_based_params = self._generate_endpoint_based_parameters(endpoint_name)
        
        # Test endpoint-specific parameters
        for param in endpoint_based_params:
            for value in ['test', 1, True, '2024-01-01']:
                test = await self._test_single_parameter(endpoint_url, param, value, endpoint_name)
                tests.append(test)
                await asyncio.sleep(self.rate_limit_delay)
        
        # Test REST convention parameters
        rest_params = ['id', 'ids', 'fields', 'include', 'exclude', 'expand']
        for param in rest_params:
            for value in ['1', 'name,id', 'all']:
                test = await self._test_single_parameter(endpoint_url, param, value, endpoint_name)
                tests.append(test)
                await asyncio.sleep(self.rate_limit_delay)
        
        return tests

    async def _behavioral_analysis(self, endpoint_url: str, endpoint_name: str) -> Dict[str, Any]:
        """Analyze parameter behavior patterns."""
        tests = []
        behaviors = {}
        
        # Test parameter sensitivity to different values
        test_params = ['limit', 'page', 'sort', 'filter']
        
        for param in test_params:
            param_behavior = {
                'accepts_numeric': False,
                'accepts_string': False,
                'accepts_boolean': False,
                'value_range': {},
                'error_responses': []
            }
            
            # Test different value types
            test_values = [
                (1, 'numeric'), (100, 'numeric'), (0, 'numeric'),
                ('test', 'string'), ('', 'string'),
                (True, 'boolean'), (False, 'boolean')
            ]
            
            for value, value_type in test_values:
                test = await self._test_single_parameter(endpoint_url, param, value, endpoint_name)
                tests.append(test)
                
                if test.success:
                    param_behavior[f'accepts_{value_type}'] = True
                else:
                    param_behavior['error_responses'].append({
                        'value': value,
                        'error_code': test.response_code,
                        'error_message': test.error_message
                    })
                
                await asyncio.sleep(self.rate_limit_delay)
            
            behaviors[param] = param_behavior
        
        return {'tests': tests, 'behaviors': behaviors}

    async def _error_based_discovery(self, endpoint_url: str, endpoint_name: str) -> Dict[str, Any]:
        """Discover parameters through error message analysis."""
        tests = []
        error_patterns = {}
        
        # Test with invalid values to trigger error messages
        invalid_test_cases = [
            ('invalid_param', 'invalid_value'),
            ('required_field', ''),
            ('numeric_field', 'not_a_number'),
            ('date_field', 'invalid_date'),
            ('enum_field', 'invalid_option')
        ]
        
        for param, value in invalid_test_cases:
            test = await self._test_single_parameter(endpoint_url, param, value, endpoint_name)
            tests.append(test)
            
            if not test.success and test.response_data:
                # Analyze error message for parameter hints
                error_message = str(test.response_data).lower()
                
                # Look for parameter names in error messages
                potential_params = self._extract_parameters_from_error(error_message)
                if potential_params:
                    error_patterns[f'error_{test.response_code}'] = potential_params
            
            await asyncio.sleep(self.rate_limit_delay)
        
        return {'tests': tests, 'patterns': error_patterns}

    async def _combination_testing(self, endpoint_url: str, endpoint_name: str, discovered_params: List[str]) -> List[ParameterTest]:
        """Test parameter combinations."""
        tests = []
        
        if len(discovered_params) < 2:
            return tests
        
        # Test pairs of discovered parameters
        for param1, param2 in itertools.combinations(discovered_params[:5], 2):
            test_combinations = [
                {param1: 'test1', param2: 'test2'},
                {param1: 1, param2: 10},
                {param1: True, param2: False}
            ]
            
            for params in test_combinations:
                test = await self._test_parameter_combination(endpoint_url, params, endpoint_name)
                tests.append(test)
                await asyncio.sleep(self.rate_limit_delay)
        
        return tests

    def _should_test_parameter_value_combination(self, param: str, value_type: str) -> bool:
        """Determine if a parameter/value type combination should be tested."""
        param_lower = param.lower()
        
        # Smart filtering based on parameter name patterns
        if value_type == 'injection_tests':
            # Only test injection on string-like parameters
            return any(keyword in param_lower for keyword in ['search', 'query', 'filter', 'name'])
        
        if value_type == 'numeric_edge_cases':
            # Only test numeric edge cases on numeric parameters
            return any(keyword in param_lower for keyword in ['limit', 'page', 'count', 'size', 'id'])
        
        if value_type == 'unicode_tests':
            # Test unicode on text parameters
            return any(keyword in param_lower for keyword in ['name', 'title', 'description', 'text'])
        
        return True

    def _generate_endpoint_based_parameters(self, endpoint_name: str) -> List[str]:
        """Generate parameter names based on endpoint name."""
        params = []
        
        # Extract words from endpoint name
        words = endpoint_name.replace('_', ' ').replace('-', ' ').split()
        
        for word in words:
            word_lower = word.lower()
            
            # Generate variations
            params.extend([
                word_lower,
                f"{word_lower}_id",
                f"{word_lower}_name",
                f"{word_lower}_type",
                f"min_{word_lower}",
                f"max_{word_lower}",
                f"{word_lower}_filter",
                f"sort_by_{word_lower}"
            ])
        
        return params

    def _extract_parameters_from_error(self, error_message: str) -> List[str]:
        """Extract potential parameter names from error messages."""
        import re
        
        # Common patterns in error messages that reveal parameter names
        patterns = [
            r"parameter '(\w+)' is required",
            r"unknown parameter '(\w+)'",
            r"invalid value for '(\w+)'",
            r"missing required field '(\w+)'",
            r"'(\w+)' must be",
            r"expected '(\w+)' to be"
        ]
        
        found_params = []
        for pattern in patterns:
            matches = re.findall(pattern, error_message, re.IGNORECASE)
            found_params.extend(matches)
        
        return list(set(found_params))

    async def _test_single_parameter(self, endpoint_url: str, param: str, value: Any, endpoint_name: str) -> ParameterTest:
        """Test a single parameter with a value."""
        start_time = datetime.now()
        
        try:
            params = {param: value}
            response = self.session.get(endpoint_url, params=params, timeout=self.timeout_seconds)
            
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Try to parse response
            response_data = None
            try:
                response_data = response.json()
            except:
                response_data = response.text[:200] if response.text else None
            
            return ParameterTest(
                parameter_name=param,
                parameter_value=value,
                test_type='single_parameter',
                success=response.status_code == 200,
                response_code=response.status_code,
                response_data=response_data,
                response_time=response_time,
                error_message=None,
                timestamp=start_time.isoformat(),
                endpoint_url=endpoint_url
            )
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            return ParameterTest(
                parameter_name=param,
                parameter_value=value,
                test_type='single_parameter',
                success=False,
                response_code=0,
                response_data=None,
                response_time=response_time,
                error_message=str(e),
                timestamp=start_time.isoformat(),
                endpoint_url=endpoint_url
            )

    async def _test_parameter_combination(self, endpoint_url: str, params: Dict[str, Any], endpoint_name: str) -> ParameterTest:
        """Test a combination of parameters."""
        start_time = datetime.now()
        
        try:
            response = self.session.get(endpoint_url, params=params, timeout=self.timeout_seconds)
            response_time = (datetime.now() - start_time).total_seconds()
            
            response_data = None
            try:
                response_data = response.json()
            except:
                response_data = response.text[:200] if response.text else None
            
            return ParameterTest(
                parameter_name=str(params),
                parameter_value=params,
                test_type='parameter_combination',
                success=response.status_code == 200,
                response_code=response.status_code,
                response_data=response_data,
                response_time=response_time,
                error_message=None,
                timestamp=start_time.isoformat(),
                endpoint_url=endpoint_url
            )
            
        except Exception as e:
            response_time = (datetime.now() - start_time).total_seconds()
            return ParameterTest(
                parameter_name=str(params),
                parameter_value=params,
                test_type='parameter_combination',
                success=False,
                response_code=0,
                response_data=None,
                response_time=response_time,
                error_message=str(e),
                timestamp=start_time.isoformat(),
                endpoint_url=endpoint_url
            )
