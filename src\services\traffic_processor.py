#!/usr/bin/env python3
"""
Traffic Processor Service for Chrome Extension Integration

Processes network traffic data from the Chrome extension, performs
analysis, and integrates with the Cipher-Spy discovery framework.
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
from collections import defaultdict, Counter

from ..models.extension_models import (
    ExtensionRequest, 
    DomainAnalysis, 
    APIEndpointInfo,
    SessionAnalysisResult,
    TrafficPattern,
    SecurityFinding,
    PerformanceMetric
)


class TrafficProcessor:
    """
    Processes and analyzes network traffic from Chrome extension.
    
    This service handles the analysis of captured network requests,
    identifies API patterns, extracts business intelligence, and
    provides insights for further discovery.
    """

    def __init__(self):
        """Initialize the traffic processor."""
        
        # API detection patterns
        self.api_patterns = [
            r'/api/',
            r'/v\d+/',
            r'\.json$',
            r'/graphql',
            r'/rest/',
            r'/endpoints?/',
            r'/services?/',
            r'/data/',
            r'/feed/'
        ]
        
        # Technology detection patterns
        self.tech_patterns = {
            'React': [r'react', r'_react', r'ReactDOM'],
            'Vue': [r'vue\.js', r'vuejs', r'_vue'],
            'Angular': [r'angular', r'ng-', r'@angular'],
            'jQuery': [r'jquery', r'\$\.'],
            'GraphQL': [r'graphql', r'/graphql'],
            'REST': [r'/api/', r'/rest/'],
            'WebSocket': [r'ws://', r'wss://'],
            'Socket.IO': [r'socket\.io'],
            'Express': [r'express', r'X-Powered-By.*Express'],
            'Django': [r'django', r'csrftoken'],
            'Rails': [r'rails', r'X-Request-Id'],
            'Laravel': [r'laravel', r'laravel_session'],
            'Spring': [r'spring', r'JSESSIONID'],
            'ASP.NET': [r'asp\.net', r'__VIEWSTATE']
        }
        
        # Security header patterns
        self.security_headers = [
            'Content-Security-Policy',
            'X-Frame-Options',
            'X-Content-Type-Options',
            'X-XSS-Protection',
            'Strict-Transport-Security',
            'Referrer-Policy',
            'Permissions-Policy',
            'Cross-Origin-Embedder-Policy',
            'Cross-Origin-Opener-Policy',
            'Cross-Origin-Resource-Policy'
        ]

    async def analyze_session(self, requests: List[ExtensionRequest]) -> SessionAnalysisResult:
        """
        Perform comprehensive analysis of a browsing session.
        
        Args:
            requests: List of captured requests
            
        Returns:
            SessionAnalysisResult: Comprehensive analysis results
        """
        print(f"🔍 Analyzing session with {len(requests)} requests...")
        
        # Group requests by domain
        domain_requests = self._group_requests_by_domain(requests)
        
        # Analyze each domain
        domain_analyses = []
        for domain, domain_reqs in domain_requests.items():
            analysis = await self._analyze_domain(domain, domain_reqs)
            domain_analyses.append(analysis)
        
        # Detect technologies across all requests
        technologies = self._detect_technologies(requests)
        
        # Perform security analysis
        security_analysis = self._analyze_security(requests)
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics(requests)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(domain_analyses, security_analysis)
        
        # Count API requests
        api_requests = sum(1 for req in requests if self._is_api_request(req))
        
        result = SessionAnalysisResult(
            session_id=requests[0].sessionId if requests else "unknown",
            analysis_time=datetime.now(),
            total_requests=len(requests),
            api_requests=api_requests,
            unique_domains=len(domain_requests),
            domains=domain_analyses,
            technologies=technologies,
            security_analysis=security_analysis,
            performance_metrics=performance_metrics,
            recommendations=recommendations
        )
        
        print(f"✅ Session analysis completed: {api_requests} API requests across {len(domain_requests)} domains")
        return result

    def _group_requests_by_domain(self, requests: List[ExtensionRequest]) -> Dict[str, List[ExtensionRequest]]:
        """Group requests by domain."""
        domain_requests = defaultdict(list)
        
        for request in requests:
            try:
                parsed = urlparse(request.url)
                domain = parsed.netloc
                domain_requests[domain].append(request)
            except Exception:
                domain_requests['unknown'].append(request)
        
        return dict(domain_requests)

    async def _analyze_domain(self, domain: str, requests: List[ExtensionRequest]) -> DomainAnalysis:
        """Analyze requests for a specific domain."""
        
        # Identify API endpoints
        api_endpoints = self._identify_api_endpoints(requests)
        
        # Detect technologies for this domain
        domain_technologies = self._detect_domain_technologies(requests)
        
        # Identify patterns
        patterns = self._identify_domain_patterns(requests)
        
        # Extract security headers
        security_headers = self._extract_security_headers(requests)
        
        return DomainAnalysis(
            domain=domain,
            request_count=len(requests),
            api_endpoints=api_endpoints,
            technologies=domain_technologies,
            patterns=patterns,
            security_headers=security_headers
        )

    def _identify_api_endpoints(self, requests: List[ExtensionRequest]) -> List[APIEndpointInfo]:
        """Identify API endpoints from requests."""
        endpoint_data = defaultdict(lambda: {
            'methods': set(),
            'response_codes': set(),
            'parameters': set(),
            'headers': {},
            'content_types': set(),
            'count': 0
        })
        
        for request in requests:
            if self._is_api_request(request):
                # Normalize URL (remove query parameters for grouping)
                parsed = urlparse(request.url)
                base_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
                
                endpoint_data[base_url]['methods'].add(request.method.value)
                endpoint_data[base_url]['count'] += 1
                
                # Extract parameters from query string
                if parsed.query:
                    params = parse_qs(parsed.query)
                    endpoint_data[base_url]['parameters'].update(params.keys())
                
                # Extract response information
                if request.response:
                    endpoint_data[base_url]['response_codes'].add(request.response.statusCode)
                    
                    if request.response.responseHeaders:
                        for header in request.response.responseHeaders:
                            if header.name.lower() == 'content-type':
                                endpoint_data[base_url]['content_types'].add(header.value)
        
        # Convert to APIEndpointInfo objects
        api_endpoints = []
        for url, data in endpoint_data.items():
            # Use the most common method
            primary_method = max(data['methods']) if data['methods'] else 'GET'
            
            endpoint = APIEndpointInfo(
                url=url,
                method=primary_method,
                request_count=data['count'],
                response_codes=list(data['response_codes']),
                parameters=list(data['parameters']),
                headers=data['headers'],
                content_type=list(data['content_types'])[0] if data['content_types'] else None,
                is_api=True
            )
            api_endpoints.append(endpoint)
        
        return api_endpoints

    def _is_api_request(self, request: ExtensionRequest) -> bool:
        """Determine if a request is likely an API call."""
        url = request.url.lower()
        
        # Check URL patterns
        for pattern in self.api_patterns:
            if re.search(pattern, url):
                return True
        
        # Check content type
        if request.response and request.response.responseHeaders:
            for header in request.response.responseHeaders:
                if header.name.lower() == 'content-type':
                    if 'json' in header.value.lower() or 'xml' in header.value.lower():
                        return True
        
        # Check for AJAX requests
        if request.type.value == 'xmlhttprequest':
            return True
        
        return False

    def _detect_technologies(self, requests: List[ExtensionRequest]) -> Dict[str, int]:
        """Detect technologies used across all requests."""
        tech_counts = defaultdict(int)
        
        for request in requests:
            # Check URL patterns
            url = request.url.lower()
            for tech, patterns in self.tech_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        tech_counts[tech] += 1
                        break
            
            # Check headers
            if request.response and request.response.responseHeaders:
                for header in request.response.responseHeaders:
                    header_value = f"{header.name}: {header.value}".lower()
                    for tech, patterns in self.tech_patterns.items():
                        for pattern in patterns:
                            if re.search(pattern, header_value, re.IGNORECASE):
                                tech_counts[tech] += 1
                                break
        
        return dict(tech_counts)

    def _detect_domain_technologies(self, requests: List[ExtensionRequest]) -> List[str]:
        """Detect technologies for a specific domain."""
        technologies = set()
        
        for request in requests:
            url = request.url.lower()
            for tech, patterns in self.tech_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        technologies.add(tech)
                        break
        
        return list(technologies)

    def _identify_domain_patterns(self, requests: List[ExtensionRequest]) -> List[str]:
        """Identify patterns in domain requests."""
        patterns = []
        
        # URL path patterns
        paths = [urlparse(req.url).path for req in requests]
        path_segments = []
        for path in paths:
            path_segments.extend(path.split('/'))
        
        # Find common path segments
        segment_counts = Counter(path_segments)
        common_segments = [seg for seg, count in segment_counts.most_common(5) if seg and count > 1]
        
        if common_segments:
            patterns.append(f"Common path segments: {', '.join(common_segments)}")
        
        # API versioning patterns
        versioned_paths = [path for path in paths if re.search(r'/v\d+/', path)]
        if versioned_paths:
            patterns.append("API versioning detected")
        
        # RESTful patterns
        rest_methods = set(req.method.value for req in requests)
        if len(rest_methods) > 2:
            patterns.append("RESTful API patterns detected")
        
        return patterns

    def _extract_security_headers(self, requests: List[ExtensionRequest]) -> Dict[str, str]:
        """Extract security headers from responses."""
        security_headers = {}
        
        for request in requests:
            if request.response and request.response.responseHeaders:
                for header in request.response.responseHeaders:
                    if header.name in self.security_headers:
                        security_headers[header.name] = header.value
        
        return security_headers

    def _analyze_security(self, requests: List[ExtensionRequest]) -> Dict[str, Any]:
        """Perform security analysis on requests."""
        security_analysis = {
            'findings': [],
            'security_score': 100,
            'missing_headers': [],
            'insecure_requests': 0,
            'mixed_content': False
        }
        
        https_requests = 0
        http_requests = 0
        
        for request in requests:
            parsed = urlparse(request.url)
            
            # Count HTTP vs HTTPS
            if parsed.scheme == 'https':
                https_requests += 1
            elif parsed.scheme == 'http':
                http_requests += 1
                security_analysis['insecure_requests'] += 1
        
        # Check for mixed content
        if https_requests > 0 and http_requests > 0:
            security_analysis['mixed_content'] = True
            security_analysis['security_score'] -= 20
        
        # Check for missing security headers
        all_headers = set()
        for request in requests:
            if request.response and request.response.responseHeaders:
                for header in request.response.responseHeaders:
                    all_headers.add(header.name)
        
        missing_headers = [header for header in self.security_headers if header not in all_headers]
        security_analysis['missing_headers'] = missing_headers
        security_analysis['security_score'] -= len(missing_headers) * 5
        
        # Ensure score doesn't go below 0
        security_analysis['security_score'] = max(0, security_analysis['security_score'])
        
        return security_analysis

    def _calculate_performance_metrics(self, requests: List[ExtensionRequest]) -> Dict[str, Any]:
        """Calculate performance metrics."""
        metrics = {
            'total_requests': len(requests),
            'avg_response_time': 0,
            'slow_requests': 0,
            'failed_requests': 0,
            'largest_response': 0,
            'request_distribution': {}
        }
        
        response_times = []
        failed_count = 0
        
        for request in requests:
            if request.response:
                if request.response.statusCode >= 400:
                    failed_count += 1
                
                # Calculate response time if we have timestamps
                # Note: This would need actual timing data from the extension
                
        metrics['failed_requests'] = failed_count
        
        # Request distribution by domain
        domain_counts = defaultdict(int)
        for request in requests:
            domain = urlparse(request.url).netloc
            domain_counts[domain] += 1
        
        metrics['request_distribution'] = dict(domain_counts)
        
        return metrics

    def _generate_recommendations(self, domain_analyses: List[DomainAnalysis], security_analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis."""
        recommendations = []
        
        # Security recommendations
        if security_analysis['missing_headers']:
            recommendations.append(f"Implement missing security headers: {', '.join(security_analysis['missing_headers'][:3])}")
        
        if security_analysis['insecure_requests'] > 0:
            recommendations.append("Migrate HTTP requests to HTTPS for better security")
        
        if security_analysis['mixed_content']:
            recommendations.append("Fix mixed content issues (HTTP resources on HTTPS pages)")
        
        # API recommendations
        api_domains = [domain for domain in domain_analyses if domain.api_endpoints]
        if api_domains:
            recommendations.append(f"Consider implementing API discovery for {len(api_domains)} domains with API endpoints")
        
        # Performance recommendations
        total_requests = sum(domain.request_count for domain in domain_analyses)
        if total_requests > 100:
            recommendations.append("High request volume detected - consider request optimization")
        
        return recommendations

    def extract_target_domains(self, requests: List[ExtensionRequest]) -> List[str]:
        """Extract unique domains that could be targets for API discovery."""
        domains = set()
        
        for request in requests:
            if self._is_api_request(request):
                try:
                    parsed = urlparse(request.url)
                    domain = parsed.netloc
                    if domain and not self._is_common_cdn(domain):
                        domains.add(domain)
                except Exception:
                    continue
        
        return list(domains)

    def _is_common_cdn(self, domain: str) -> bool:
        """Check if domain is a common CDN that shouldn't be analyzed."""
        cdn_patterns = [
            'googleapis.com',
            'cloudflare.com',
            'amazonaws.com',
            'azure.com',
            'jsdelivr.net',
            'unpkg.com',
            'cdnjs.cloudflare.com',
            'fonts.gstatic.com',
            'google-analytics.com',
            'googletagmanager.com'
        ]
        
        return any(cdn in domain.lower() for cdn in cdn_patterns)

    def is_api_request(self, request: Any) -> bool:
        """Public method to check if a request is an API request."""
        if isinstance(request, ExtensionRequest):
            return self._is_api_request(request)
        elif isinstance(request, dict):
            # Handle dict format
            url = request.get('url', '').lower()
            return any(re.search(pattern, url) for pattern in self.api_patterns)
        else:
            return False

    def extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            return 'unknown'
