#!/usr/bin/env python3
"""
Ultra-simple test to get Cipher-Spy working.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def main():
    print("Starting simple test...")
    
    try:
        # Test 1: Basic imports
        print("Testing imports...")
        from src.crawling.playwright_crawler import PlaywrightCrawler
        print("✅ PlaywrightCrawler imported")
        
        # Test 2: Create crawler
        print("Creating crawler...")
        crawler = PlaywrightCrawler()
        print("✅ Crawler created")
        
        # Test 3: Start browser
        print("Starting browser...")
        await crawler.start()
        print("✅ Browser started")
        
        # Test 4: Simple page visit
        print("Visiting test page...")
        result = await crawler.crawl_page("https://httpbin.org/html")
        print(f"✅ Page crawled: {result.url}")
        print(f"   Title: {result.title}")
        print(f"   Status: {result.status_code}")
        
        # Test 5: Cleanup
        print("Cleaning up...")
        await crawler.stop()
        print("✅ Cleanup complete")
        
        print("\n🎉 All tests passed! Basic crawler is working.")
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
