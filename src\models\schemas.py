"""
Pydantic schemas for API request/response models.

Defines the data structures used for API serialization, validation,
and documentation. These schemas ensure type safety and automatic
API documentation generation.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from pydantic import BaseModel, Field, HttpUrl, validator

from ..core.state import ScanStatus, AgentStatus, ExploitStatus


class ScanCreate(BaseModel):
    """Schema for creating a new scan."""
    
    target_url: HttpUrl = Field(
        ...,
        description="Target URL to scan",
        example="https://example.com"
    )
    scope: Optional[List[str]] = Field(
        default=None,
        description="Additional URLs or domains in scope",
        example=["https://api.example.com", "*.example.com"]
    )
    max_depth: Optional[int] = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum crawling depth"
    )
    max_pages: Optional[int] = Field(
        default=1000,
        ge=1,
        le=10000,
        description="Maximum pages to crawl"
    )
    credentials: Optional[Dict[str, str]] = Field(
        default=None,
        description="Authentication credentials if needed"
    )
    headers: Optional[Dict[str, str]] = Field(
        default=None,
        description="Custom HTTP headers to include"
    )
    safe_mode: Optional[bool] = Field(
        default=True,
        description="Enable safe mode (no destructive actions)"
    )
    
    @validator("target_url")
    def validate_target_url(cls, v):
        """Validate target URL format."""
        url_str = str(v)
        if not url_str.startswith(("http://", "https://")):
            raise ValueError("Target URL must start with http:// or https://")
        return v


class ScanUpdate(BaseModel):
    """Schema for updating scan configuration."""
    
    status: Optional[ScanStatus] = Field(
        default=None,
        description="New scan status"
    )
    max_depth: Optional[int] = Field(
        default=None,
        ge=1,
        le=20,
        description="Updated maximum crawling depth"
    )
    max_pages: Optional[int] = Field(
        default=None,
        ge=1,
        le=10000,
        description="Updated maximum pages to crawl"
    )
    safe_mode: Optional[bool] = Field(
        default=None,
        description="Updated safe mode setting"
    )


class ScanResponse(BaseModel):
    """Schema for scan response data."""
    
    scan_id: str = Field(..., description="Unique scan identifier")
    target_url: str = Field(..., description="Target URL being scanned")
    status: ScanStatus = Field(..., description="Current scan status")
    created_at: datetime = Field(..., description="Scan creation timestamp")
    started_at: Optional[datetime] = Field(
        default=None,
        description="Scan start timestamp"
    )
    completed_at: Optional[datetime] = Field(
        default=None,
        description="Scan completion timestamp"
    )
    progress: Optional[float] = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Overall scan progress (0.0 to 1.0)"
    )
    current_agent: Optional[str] = Field(
        default=None,
        description="Currently active agent"
    )
    pages_found: Optional[int] = Field(
        default=0,
        description="Number of pages discovered"
    )
    endpoints_found: Optional[int] = Field(
        default=0,
        description="Number of API endpoints discovered"
    )
    vulnerabilities_found: Optional[int] = Field(
        default=0,
        description="Number of vulnerabilities identified"
    )
    message: Optional[str] = Field(
        default=None,
        description="Status message or error description"
    )
    
    class Config:
        use_enum_values = True


class ScanSummary(BaseModel):
    """Schema for scan summary in list views."""
    
    scan_id: str = Field(..., description="Unique scan identifier")
    target_url: str = Field(..., description="Target URL")
    status: ScanStatus = Field(..., description="Current scan status")
    created_at: datetime = Field(..., description="Creation timestamp")
    progress: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Overall progress"
    )
    findings_count: int = Field(
        default=0,
        description="Total number of findings"
    )
    
    class Config:
        use_enum_values = True


class AgentProgress(BaseModel):
    """Schema for individual agent progress."""
    
    agent_id: str = Field(..., description="Agent identifier")
    agent_type: str = Field(..., description="Type of agent")
    status: AgentStatus = Field(..., description="Current agent status")
    progress: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Agent progress (0.0 to 1.0)"
    )
    current_task: Optional[str] = Field(
        default=None,
        description="Current task description"
    )
    started_at: Optional[datetime] = Field(
        default=None,
        description="Agent start timestamp"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Error message if failed"
    )
    
    class Config:
        use_enum_values = True


class ScanProgress(BaseModel):
    """Schema for detailed scan progress information."""
    
    scan_id: str = Field(..., description="Unique scan identifier")
    status: ScanStatus = Field(..., description="Current scan status")
    overall_progress: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Overall scan progress"
    )
    current_agent: Optional[str] = Field(
        default=None,
        description="Currently active agent"
    )
    agent_progress: Dict[str, AgentProgress] = Field(
        default_factory=dict,
        description="Progress for each agent"
    )
    human_intervention_required: bool = Field(
        default=False,
        description="Whether human intervention is required"
    )
    intervention_message: Optional[str] = Field(
        default=None,
        description="Message describing required intervention"
    )
    pending_approvals: List[str] = Field(
        default_factory=list,
        description="List of exploit plan IDs awaiting approval"
    )
    message: Optional[str] = Field(
        default=None,
        description="Current status message"
    )
    
    class Config:
        use_enum_values = True


class TargetCreate(BaseModel):
    """Schema for creating a target."""
    
    url: HttpUrl = Field(..., description="Target URL")
    name: Optional[str] = Field(
        default=None,
        description="Human-readable target name"
    )
    description: Optional[str] = Field(
        default=None,
        description="Target description"
    )


class TargetResponse(BaseModel):
    """Schema for target response data."""
    
    target_id: str = Field(..., description="Unique target identifier")
    url: str = Field(..., description="Target URL")
    name: Optional[str] = Field(default=None, description="Target name")
    description: Optional[str] = Field(default=None, description="Description")
    created_at: datetime = Field(..., description="Creation timestamp")
    last_scanned: Optional[datetime] = Field(
        default=None,
        description="Last scan timestamp"
    )
    scan_count: int = Field(default=0, description="Number of scans performed")


class TechnologyInfo(BaseModel):
    """Schema for detected technology information."""
    
    name: str = Field(..., description="Technology name")
    version: Optional[str] = Field(default=None, description="Version if detected")
    category: str = Field(..., description="Technology category")
    confidence: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Detection confidence"
    )
    detection_method: Optional[str] = Field(
        default=None,
        description="Method used for detection"
    )


class VulnerabilityInfo(BaseModel):
    """Schema for vulnerability information."""
    
    vulnerability_id: str = Field(..., description="Unique vulnerability ID")
    name: str = Field(..., description="Vulnerability name")
    description: str = Field(..., description="Vulnerability description")
    severity: str = Field(..., description="Severity level")
    cve_id: Optional[str] = Field(default=None, description="CVE identifier")
    affected_technology: Optional[str] = Field(
        default=None,
        description="Affected technology"
    )
    confidence: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Confidence in vulnerability"
    )
    evidence: List[str] = Field(
        default_factory=list,
        description="Evidence supporting the vulnerability"
    )
    discovered_at: datetime = Field(..., description="Discovery timestamp")


class ExploitPlanResponse(BaseModel):
    """Schema for exploit plan information."""
    
    plan_id: str = Field(..., description="Unique exploit plan ID")
    vulnerability_id: str = Field(..., description="Associated vulnerability ID")
    name: str = Field(..., description="Exploit plan name")
    description: str = Field(..., description="Exploit description")
    steps: List[str] = Field(..., description="Execution steps")
    payloads: List[str] = Field(
        default_factory=list,
        description="Exploit payloads"
    )
    expected_outcome: str = Field(..., description="Expected result")
    risk_level: str = Field(..., description="Risk level")
    status: ExploitStatus = Field(..., description="Current status")
    created_at: datetime = Field(..., description="Creation timestamp")
    approved_at: Optional[datetime] = Field(
        default=None,
        description="Approval timestamp"
    )
    executed_at: Optional[datetime] = Field(
        default=None,
        description="Execution timestamp"
    )
    results: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Execution results"
    )
    
    class Config:
        use_enum_values = True


class FindingResponse(BaseModel):
    """Schema for general finding information."""
    
    finding_id: str = Field(..., description="Unique finding ID")
    finding_type: str = Field(..., description="Type of finding")
    title: str = Field(..., description="Finding title")
    description: str = Field(..., description="Finding description")
    severity: str = Field(..., description="Severity level")
    confidence: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Confidence level"
    )
    evidence: Dict[str, Any] = Field(
        default_factory=dict,
        description="Supporting evidence"
    )
    recommendations: List[str] = Field(
        default_factory=list,
        description="Remediation recommendations"
    )
    discovered_at: datetime = Field(..., description="Discovery timestamp")
    scan_id: str = Field(..., description="Associated scan ID")


class ExploitApproval(BaseModel):
    """Schema for exploit approval/rejection."""
    
    plan_id: str = Field(..., description="Exploit plan ID")
    approved: bool = Field(..., description="Whether to approve the exploit")
    reason: Optional[str] = Field(
        default=None,
        description="Reason for approval/rejection"
    )
    modifications: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Requested modifications to the plan"
    )
