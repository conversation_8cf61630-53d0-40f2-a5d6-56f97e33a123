"""
LangGraph workflow definition for Cipher-Spy.

Defines the multi-agent workflow orchestration using LangGraph,
managing state transitions and agent coordination for the red team swarm.
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

# Optional LangGraph import
try:
    from langgraph import StateGraph, END
    LANGGRAPH_AVAILABLE = True
except ImportError:
    StateGraph = None
    END = None
    LANGGRAPH_AVAILABLE = False

# Optional LangChain import
try:
    from langchain.schema import BaseMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    BaseMessage = None
    LANGCHAIN_AVAILABLE = False

from .state import ScanState, ScanStatus, AgentStatus
from .exceptions import CipherSpyException
from ..agents.orchestrator import OrchestratorAgent
from ..agents.crawler import CrawlerAgent
# from ..agents.fingerprinter import FingerprintingAgent
# from ..agents.graph_rag import GraphRAGAgent
# from ..agents.exploit_planner import ExploitPlannerAgent
# from ..agents.executor import ExecutorAgent
from ..utils.logging import get_logger


class CipherSpyWorkflow:
    """
    LangGraph-based workflow orchestrator for Cipher-Spy.

    Manages the multi-agent red team workflow with the following phases:
    1. Orchestration and planning
    2. Web crawling and reconnaissance
    3. Technology fingerprinting
    4. Knowledge graph analysis
    5. Exploit planning
    6. Human approval
    7. Controlled execution
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the workflow.

        Args:
            config: Optional workflow configuration
        """
        self.config = config or {}
        self.logger = get_logger(__name__)

        # Initialize agents
        self.orchestrator = OrchestratorAgent(**self.config.get("orchestrator", {}))
        self.crawler = CrawlerAgent(**self.config.get("crawler", {}))
        # TODO: Initialize other agents when implemented
        # self.fingerprinter = FingerprintingAgent(**self.config.get("fingerprinter", {}))
        # self.graph_rag = GraphRAGAgent(**self.config.get("graph_rag", {}))
        # self.exploit_planner = ExploitPlannerAgent(**self.config.get("exploit_planner", {}))
        # self.executor = ExecutorAgent(**self.config.get("executor", {}))

        # Build the workflow graph
        self.workflow = self._build_workflow()

        self.logger.info("Cipher-Spy workflow initialized")

    def _build_workflow(self):
        """Build the workflow (LangGraph if available, simple otherwise)."""
        try:
            if LANGGRAPH_AVAILABLE:
                return self._build_langgraph_workflow()
            else:
                return self._build_simple_workflow()
        except Exception as e:
            raise CipherSpyException(f"Failed to build workflow: {e}")

    def _build_langgraph_workflow(self):
        """Build LangGraph workflow."""
        # Create the state graph
        workflow = StateGraph(ScanState)

        # Add nodes for each agent
        workflow.add_node("orchestrator", self._orchestrator_node)
        workflow.add_node("crawler", self._crawler_node)

        # Add conditional edges based on orchestrator decisions
        workflow.add_conditional_edges(
            "orchestrator",
            self._route_next_agent,
            {
                "crawler": "crawler",
                "complete": END,
                "wait": "orchestrator"
            }
        )

        # Add edges back to orchestrator after each agent
        workflow.add_edge("crawler", "orchestrator")

        # Set entry point
        workflow.set_entry_point("orchestrator")

        return workflow.compile()

    def _build_simple_workflow(self):
        """Build simple workflow without LangGraph."""
        return SimpleWorkflow(self)

    async def _orchestrator_node(self, state: ScanState) -> ScanState:
        """Execute the orchestrator agent."""
        try:
            self.logger.info("Executing orchestrator node")
            return await self.orchestrator.execute(state)
        except Exception as e:
            self.logger.error(f"Orchestrator node failed: {e}")
            state.status = ScanStatus.FAILED
            state.error_message = str(e)
            return state

    async def _crawler_node(self, state: ScanState) -> ScanState:
        """Execute the crawler agent."""
        try:
            self.logger.info("Executing crawler node")
            return await self.crawler.execute(state)
        except Exception as e:
            self.logger.error(f"Crawler node failed: {e}")
            # Mark crawler as failed but continue workflow
            state.update_agent_status(
                agent_id="crawler",
                status=AgentStatus.FAILED,
                error=str(e)
            )
            return state

    # TODO: Implement other agent nodes when agents are ready
    # async def _fingerprinter_node(self, state: ScanState) -> ScanState:
    #     """Execute the fingerprinter agent."""
    #     try:
    #         self.logger.info("Executing fingerprinter node")
    #         return await self.fingerprinter.execute(state)
    #     except Exception as e:
    #         self.logger.error(f"Fingerprinter node failed: {e}")
    #         state.update_agent_status(
    #             agent_id="fingerprinter",
    #             status=AgentStatus.FAILED,
    #             error=str(e)
    #         )
    #         return state

    def _route_next_agent(self, state: ScanState) -> str:
        """
        Determine the next agent to execute based on current state.

        Args:
            state: Current scan state

        Returns:
            str: Next agent to execute or action to take
        """
        try:
            # Check if human intervention is required
            if state.human_intervention_required:
                return "wait"

            # Check if workflow should complete
            if state.status == ScanStatus.COMPLETED:
                return "complete"

            # Route based on current agent and completion status
            current_agent = state.current_agent

            if current_agent == "crawler":
                crawler_status = state.agents.get("crawler", {}).status
                if crawler_status == AgentStatus.COMPLETED:
                    # TODO: Route to fingerprinter when implemented
                    # return "fingerprinter"
                    return "complete"  # For now, complete after crawling
                elif crawler_status == AgentStatus.FAILED:
                    return "complete"

            # TODO: Add routing logic for other agents
            # elif current_agent == "fingerprinter":
            #     fingerprinter_status = state.agents.get("fingerprinter", {}).status
            #     if fingerprinter_status == AgentStatus.COMPLETED:
            #         return "graph_rag"
            #     elif fingerprinter_status == AgentStatus.FAILED:
            #         return "complete"

            # Default routing
            if not current_agent:
                return "crawler"  # Start with crawler

            return "complete"

        except Exception as e:
            self.logger.error(f"Error routing next agent: {e}")
            return "complete"

    async def execute(self, state: ScanState) -> ScanState:
        """
        Execute the complete workflow.

        Args:
            state: Initial scan state

        Returns:
            ScanState: Final scan state
        """
        try:
            self.logger.info(f"Starting workflow execution for scan: {state.scan_id}")

            # Execute the workflow
            final_state = await self.workflow.ainvoke(state)

            self.logger.info(f"Workflow execution completed for scan: {state.scan_id}")
            return final_state

        except Exception as e:
            self.logger.error(f"Workflow execution failed: {e}")
            state.status = ScanStatus.FAILED
            state.error_message = str(e)
            state.completed_at = datetime.utcnow()
            return state

    async def execute_step(self, state: ScanState, agent_name: str) -> ScanState:
        """
        Execute a single workflow step.

        Args:
            state: Current scan state
            agent_name: Name of agent to execute

        Returns:
            ScanState: Updated scan state
        """
        try:
            self.logger.info(f"Executing workflow step: {agent_name}")

            if agent_name == "orchestrator":
                return await self._orchestrator_node(state)
            elif agent_name == "crawler":
                return await self._crawler_node(state)
            # TODO: Add other agents when implemented
            else:
                raise CipherSpyException(f"Unknown agent: {agent_name}")

        except Exception as e:
            self.logger.error(f"Workflow step {agent_name} failed: {e}")
            state.status = ScanStatus.FAILED
            state.error_message = str(e)
            return state

    def get_workflow_status(self, state: ScanState) -> Dict[str, Any]:
        """
        Get current workflow status.

        Args:
            state: Current scan state

        Returns:
            Dict containing workflow status information
        """
        return {
            "scan_id": state.scan_id,
            "status": state.status,
            "current_agent": state.current_agent,
            "human_intervention_required": state.human_intervention_required,
            "intervention_message": state.intervention_message,
            "agents": {
                agent_id: {
                    "status": agent.status,
                    "progress": agent.progress,
                    "current_task": agent.current_task,
                    "error_message": agent.error_message
                }
                for agent_id, agent in state.agents.items()
            },
            "summary": state.get_summary()
        }

    async def cleanup(self) -> None:
        """Cleanup workflow resources."""
        try:
            await self.orchestrator.cleanup()
            await self.crawler.cleanup()
            # TODO: Cleanup other agents when implemented

            self.logger.info("Workflow cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during workflow cleanup: {e}")


class SimpleWorkflow:
    """Simple workflow implementation without LangGraph dependency."""

    def __init__(self, cipher_spy_workflow):
        self.workflow = cipher_spy_workflow
        self.logger = cipher_spy_workflow.logger

    async def ainvoke(self, state: ScanState) -> ScanState:
        """Execute simple workflow."""
        try:
            self.logger.info("Starting simple workflow execution")

            # Simple sequential execution
            # 1. Run orchestrator to initialize
            state = await self.workflow._orchestrator_node(state)

            # 2. Run crawler if needed
            if state.current_agent == "crawler":
                state = await self.workflow._crawler_node(state)

                # 3. Run orchestrator again to finalize
                state = await self.workflow._orchestrator_node(state)

            self.logger.info("Simple workflow execution completed")
            return state

        except Exception as e:
            self.logger.error(f"Simple workflow failed: {e}")
            state.status = ScanStatus.FAILED
            state.error_message = str(e)
            return state


# Workflow factory function
def create_workflow(config: Optional[Dict[str, Any]] = None) -> CipherSpyWorkflow:
    """
    Create a new Cipher-Spy workflow instance.

    Args:
        config: Optional workflow configuration

    Returns:
        CipherSpyWorkflow: Configured workflow instance
    """
    return CipherSpyWorkflow(config)
