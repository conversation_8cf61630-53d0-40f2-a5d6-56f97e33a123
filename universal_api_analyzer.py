#!/usr/bin/env python3
"""
Cipher-Spy Universal API Analyzer

Enhanced API discovery and reverse engineering tool powered by the Universal
API Discovery Framework. Works with any website while maintaining specialized
capabilities for pump.fun and other cryptocurrency platforms.

Features:
- Universal API discovery for any website
- Specialized pump.fun analysis
- Chrome extension integration
- Autonomous endpoint discovery
- Systematic parameter fuzzing
- Response schema analysis
- Business intelligence extraction
- OpenAPI documentation generation
- Client code generation
- Competitive analysis

Author: Cipher-Spy Development Team
License: MIT
Version: 2.0.0 - Universal Framework
"""

import asyncio
import argparse
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import the new universal framework
from src.api_discovery.discovery_engine import DiscoveryEngine
from src.api_discovery.target_config import TargetConfig
from src.api_discovery.universal_analyzer import UniversalAPIAnalyzer
from src.api_discovery.analyzers.pump_fun_analyzer import PumpFunAPIAnalyzer


class UniversalCipherSpy:
    """
    Universal Cipher-Spy API Discovery System.
    
    This class provides a unified interface for API discovery and analysis
    that can work with any website while providing specialized capabilities
    for known platforms like pump.fun.
    """

    def __init__(self, results_dir: Optional[Path] = None):
        """
        Initialize the Universal Cipher-Spy system.
        
        Args:
            results_dir: Directory to save results
        """
        self.results_dir = results_dir or Path("universal_cipher_spy_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Initialize the discovery engine
        self.discovery_engine = DiscoveryEngine(results_dir=self.results_dir)
        
        # Register specialized analyzers
        self.discovery_engine.register_analyzer('pump_fun', PumpFunAPIAnalyzer)

    async def analyze_target(
        self, 
        target: str,
        analyzer_type: str = 'auto',
        generate_docs: bool = True,
        generate_clients: bool = True,
        deep_analysis: bool = False
    ) -> Dict[str, Any]:
        """
        Analyze a target website for API discovery.
        
        Args:
            target: Target domain or URL
            analyzer_type: Type of analyzer ('auto', 'universal', 'pump_fun')
            generate_docs: Whether to generate documentation
            generate_clients: Whether to generate client code
            deep_analysis: Whether to perform deep analysis
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        print(f"🚀 Starting Universal API Discovery for: {target}")
        print("="*80)
        
        # Create target configuration
        target_config = await self._create_target_config(target, analyzer_type)
        
        # Auto-detect analyzer type if needed
        if analyzer_type == 'auto':
            analyzer_type = self._detect_analyzer_type(target)
        
        print(f"🔬 Using {analyzer_type} analyzer for {target_config.domain}")
        
        # Perform discovery
        result = await self.discovery_engine.discover_target(
            target_config=target_config,
            analyzer_type=analyzer_type,
            generate_docs=generate_docs,
            generate_clients=generate_clients
        )
        
        # Perform deep analysis if requested
        if deep_analysis and analyzer_type == 'pump_fun':
            print("\n🔬 Performing specialized deep analysis...")
            deep_result = await self._perform_pump_fun_deep_analysis(target_config)
            result.discovery_metadata['deep_analysis'] = deep_result
        
        return {
            'target': target,
            'analyzer_type': analyzer_type,
            'discovery_result': result,
            'timestamp': datetime.now().isoformat()
        }

    async def analyze_multiple_targets(
        self,
        targets: List[str],
        analyzer_type: str = 'auto',
        concurrent_limit: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Analyze multiple targets concurrently.
        
        Args:
            targets: List of target domains/URLs
            analyzer_type: Type of analyzer to use
            concurrent_limit: Maximum concurrent analyses
            
        Returns:
            List[Dict[str, Any]]: Results for all targets
        """
        print(f"🎯 Starting analysis of {len(targets)} targets")
        
        # Create target configurations
        target_configs = []
        for target in targets:
            config = await self._create_target_config(target, analyzer_type)
            target_configs.append(config)
        
        # Perform discoveries
        results = await self.discovery_engine.discover_multiple_targets(
            target_configs=target_configs,
            analyzer_type=analyzer_type if analyzer_type != 'auto' else 'universal',
            concurrent_limit=concurrent_limit
        )
        
        return [
            {
                'target': targets[i],
                'discovery_result': result,
                'timestamp': datetime.now().isoformat()
            }
            for i, result in enumerate(results)
        ]

    async def compare_targets(
        self,
        targets: List[str],
        analyzer_type: str = 'universal'
    ) -> Dict[str, Any]:
        """
        Compare API structures across multiple targets.
        
        Args:
            targets: List of target domains/URLs
            analyzer_type: Type of analyzer to use
            
        Returns:
            Dict[str, Any]: Comparative analysis results
        """
        print(f"🔍 Performing comparative analysis of {len(targets)} targets")
        
        # Create target configurations
        target_configs = []
        for target in targets:
            config = await self._create_target_config(target, analyzer_type)
            target_configs.append(config)
        
        # Perform comparison
        comparison = await self.discovery_engine.compare_targets(
            target_configs=target_configs,
            analyzer_type=analyzer_type
        )
        
        return comparison

    async def _create_target_config(self, target: str, analyzer_type: str) -> TargetConfig:
        """
        Create a target configuration for the given target.
        
        Args:
            target: Target domain or URL
            analyzer_type: Type of analyzer
            
        Returns:
            TargetConfig: Configuration for the target
        """
        # Extract domain from URL if needed
        if target.startswith(('http://', 'https://')):
            from urllib.parse import urlparse
            parsed = urlparse(target)
            domain = parsed.netloc
        else:
            domain = target
        
        # Create specialized configurations
        if analyzer_type == 'pump_fun' or 'pump.fun' in domain:
            return TargetConfig.create_pump_fun_config()
        else:
            return TargetConfig.create_generic_config(domain)

    def _detect_analyzer_type(self, target: str) -> str:
        """
        Auto-detect the best analyzer type for a target.
        
        Args:
            target: Target domain or URL
            
        Returns:
            str: Recommended analyzer type
        """
        target_lower = target.lower()
        
        # Check for known specialized targets
        if 'pump.fun' in target_lower:
            return 'pump_fun'
        
        # Default to universal analyzer
        return 'universal'

    async def _perform_pump_fun_deep_analysis(self, target_config: TargetConfig) -> Dict[str, Any]:
        """
        Perform specialized deep analysis for pump.fun.
        
        Args:
            target_config: Target configuration
            
        Returns:
            Dict[str, Any]: Deep analysis results
        """
        analyzer = PumpFunAPIAnalyzer(target_config)
        
        # Perform graduation pattern analysis
        graduation_analysis = await analyzer.analyze_graduation_patterns()
        
        return {
            'graduation_patterns': graduation_analysis,
            'analysis_type': 'pump_fun_deep',
            'timestamp': datetime.now().isoformat()
        }

    def get_discovery_history(self) -> List[Dict[str, Any]]:
        """Get history of all discovery sessions."""
        return self.discovery_engine.get_discovery_history()

    def get_active_sessions(self) -> Dict[str, str]:
        """Get information about active discovery sessions."""
        return self.discovery_engine.get_active_sessions()


async def main():
    """Main CLI interface for Universal Cipher-Spy."""
    parser = argparse.ArgumentParser(
        description="Cipher-Spy Universal API Discovery System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze pump.fun with specialized analyzer
  python universal_api_analyzer.py --target pump.fun --analyzer pump_fun --deep

  # Analyze any website with universal analyzer
  python universal_api_analyzer.py --target example.com --analyzer universal

  # Compare multiple targets
  python universal_api_analyzer.py --compare target1.com target2.com target3.com

  # Analyze multiple targets concurrently
  python universal_api_analyzer.py --targets target1.com target2.com --concurrent 2
        """
    )
    
    # Target specification
    parser.add_argument('--target', '-t', type=str, help='Single target domain or URL')
    parser.add_argument('--targets', nargs='+', help='Multiple targets for concurrent analysis')
    parser.add_argument('--compare', nargs='+', help='Multiple targets for comparative analysis')
    
    # Analysis options
    parser.add_argument('--analyzer', '-a', choices=['auto', 'universal', 'pump_fun'], 
                       default='auto', help='Analyzer type to use')
    parser.add_argument('--deep', action='store_true', help='Perform deep analysis (pump.fun only)')
    parser.add_argument('--no-docs', action='store_true', help='Skip documentation generation')
    parser.add_argument('--no-clients', action='store_true', help='Skip client code generation')
    
    # Concurrency options
    parser.add_argument('--concurrent', '-c', type=int, default=3, 
                       help='Maximum concurrent analyses')
    
    # Output options
    parser.add_argument('--output', '-o', type=str, help='Output directory')
    parser.add_argument('--format', choices=['json', 'yaml'], default='json', 
                       help='Output format')
    
    args = parser.parse_args()
    
    # Validate arguments
    if not any([args.target, args.targets, args.compare]):
        parser.error("Must specify --target, --targets, or --compare")
    
    # Initialize Universal Cipher-Spy
    output_dir = Path(args.output) if args.output else None
    cipher_spy = UniversalCipherSpy(results_dir=output_dir)
    
    try:
        if args.target:
            # Single target analysis
            result = await cipher_spy.analyze_target(
                target=args.target,
                analyzer_type=args.analyzer,
                generate_docs=not args.no_docs,
                generate_clients=not args.no_clients,
                deep_analysis=args.deep
            )
            
            print(f"\n✅ Analysis completed for {args.target}")
            print(f"📊 Endpoints discovered: {len(result['discovery_result'].discovered_endpoints)}")
            print(f"🧪 Parameters tested: {len(result['discovery_result'].parameter_tests)}")
            
        elif args.targets:
            # Multiple target analysis
            results = await cipher_spy.analyze_multiple_targets(
                targets=args.targets,
                analyzer_type=args.analyzer,
                concurrent_limit=args.concurrent
            )
            
            print(f"\n✅ Analysis completed for {len(results)} targets")
            for result in results:
                target = result['target']
                endpoints = len(result['discovery_result'].discovered_endpoints)
                print(f"   📊 {target}: {endpoints} endpoints discovered")
            
        elif args.compare:
            # Comparative analysis
            comparison = await cipher_spy.compare_targets(
                targets=args.compare,
                analyzer_type=args.analyzer
            )
            
            print(f"\n✅ Comparative analysis completed for {len(args.compare)} targets")
            print(f"📊 Common endpoints: {len(comparison.get('endpoint_comparison', {}).get('common_patterns', []))}")
            print(f"🔍 Common parameters: {len(comparison.get('parameter_comparison', {}).get('common_parameters', []))}")
        
        print(f"\n📁 Results saved to: {cipher_spy.results_dir}")
        
    except Exception as e:
        print(f"\n💥 Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
