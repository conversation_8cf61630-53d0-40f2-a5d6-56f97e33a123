# Database Configuration
DATABASE_URL=postgresql://cipher_user:cipher_pass@localhost:5432/cipher_spy
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=cipher_neo4j

# LLM Configuration
OPENROUTER_API_KEY=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
DEFAULT_LLM_MODEL=anthropic/claude-3.7-sonnet:thinking
FALLBACK_LLM_MODEL=openai/gpt-3.5-turbo
LOCAL_LLM_ENABLED=false
LOCAL_LLM_MODEL_PATH=/app/models/llama-2-7b-chat.gguf

# Application Settings
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=false
SECRET_KEY=your-secret-key-here
API_V1_STR=/api/v1

# Crawling Configuration
MAX_CRAWL_DEPTH=5
MAX_PAGES_PER_DOMAIN=1000
CRAWL_DELAY_MS=1000
RESPECT_ROBOTS_TXT=true
USER_AGENT=CipherSpy/1.0 (Security Research Tool)

# Security Settings
SAFE_MODE=true
REQUIRE_APPROVAL_FOR_EXPLOITS=true
MAX_EXPLOIT_ATTEMPTS=3
EXPLOIT_TIMEOUT_SECONDS=300

# Fingerprinting Configuration
WAPPALYZER_TIMEOUT=30
WAFW00F_TIMEOUT=60
ENABLE_AGGRESSIVE_FINGERPRINTING=false

# Knowledge Base Settings
EXPLOITDB_UPDATE_INTERVAL_HOURS=24
VECTOR_SIMILARITY_THRESHOLD=0.7
MAX_EXPLOIT_RECOMMENDATIONS=10

# Storage Settings
DATA_DIRECTORY=/app/data
LOGS_DIRECTORY=/app/logs
SCREENSHOTS_DIRECTORY=/app/data/screenshots
REPORTS_DIRECTORY=/app/data/reports

# Performance Settings
MAX_CONCURRENT_CRAWLERS=5
DATABASE_POOL_SIZE=20
NEO4J_POOL_SIZE=10
VECTOR_BATCH_SIZE=100

# Monitoring & Health
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Development Settings
RELOAD_ON_CHANGE=false
ENABLE_CORS=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
