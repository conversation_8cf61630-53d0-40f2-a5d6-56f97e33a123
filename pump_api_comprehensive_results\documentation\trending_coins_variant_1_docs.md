# trending_coins_variant_1

## Endpoint Information
- **URL**: https://frontend-api-v3.pump.fun/coins?offset=0&limit=50&sort=market_cap&order=DESC&includeNsfw=false
- **Method**: GET
- **Parameters**: {
  "offset": 0,
  "limit": 50,
  "sort": "market_cap",
  "order": "DESC",
  "includeNsfw": "false"
}

## Response Information
- **Response Time**: 254ms
- **Response Size**: 63654 bytes
- **Content Type**: application/json; charset=utf-8

## Response Schema
```json
{
  "type": "array",
  "length": 50,
  "items": {
    "type": "object",
    "properties": {
      "mint": {
        "type": "string",
        "example": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
      },
      "name": {
        "type": "string",
        "example": "Fartcoin "
      },
      "symbol": {
        "type": "string",
        "example": "Fartcoin "
      },
      "description": {
        "type": "null"
      },
      "image_uri": {
        "type": "string",
        "example": "https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y"
      },
      "metadata_uri": {
        "type": "string",
        "example": "https://ipfs.io/ipfs/QmYfe8zVGHA1heej47AkBX3Nnetg2"
      },
      "twitter": {
        "type": "string",
        "example": "https://www.infinitebackrooms.com/dreams/conversat"
      },
      "telegram": {
        "type": "null"
      },
      "bonding_curve": {
        "type": "string",
        "example": "TBHe5tJnuT4CQbHorJ1uVdfUoaYGPKgfCpiv2jgesVN"
      },
      "associated_bonding_curve": {
        "type": "string",
        "example": "4oPaRNdUyHNPn7oba9RnY66mkCUeUMZNVLmJ2X6J5a4Z"
      }
    },
    "total_keys": 32
  }
}
```

## Sample Response
```json
[
  {
    "mint": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "name": "Fartcoin ",
    "symbol": "Fartcoin ",
    "description": null,
    "image_uri": "https://ipfs.io/ipfs/QmQr3Fz4h1etNsF7oLGMRHiCzhB5y9a7GjyodnF7zLHK1g",
    "metadata_uri": "https://ipfs.io/ipfs/QmYfe8zVGHA1heej47AkBX3Nnetg2h2kqj5yymz1xyKeHb",
    "twitter": "https://www.infinitebackrooms.com/dreams/conversation-1721540624-scenario-terminal-of-truths-txt",
    "telegram": null,
    "bonding_curve": "TBHe5tJnuT4CQbHorJ1uVdfUoaYGPKgfCpiv2jgesVN",
    "associated_bonding_curve": "4oPaRNdUyHNPn7oba9RnY66mkCUeUMZNVLmJ2X6J5a4Z",
    "creator": "HyYNVYmnFmi87NsQqWzLJhUTPBKQUfgfhdbBa554nMFF",
    "created_timestamp": 1729231506539,
    "raydium_pool": "Bzc9NZfMqkXR6fz1DBph7BDf9BroyEf6pnzESP7v5iiw",
    "complete": true,
    "virtual_sol_reserves": 115005359177,
    "virtual_token_reserves": 279900000000000,
    "hidden": null,
    "total_supply": 1000000000000000,
    "website": null,
    "show_name": true,
    "last
```

## Usage Example
```python
import requests

response = requests.get(
    'https://frontend-api-v3.pump.fun/coins?offset=0&limit=50&sort=market_cap&order=DESC&includeNsfw=false',
    params={"offset": 0, "limit": 50, "sort": "market_cap", "order": "DESC", "includeNsfw": "false"}
)

data = response.json()
print(data)
```
