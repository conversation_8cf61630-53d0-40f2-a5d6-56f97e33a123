# Cipher-Spy: AI-Driven Red Team Swarm

A locally deployed, AI-driven red team swarm designed for professional penetration testers. Cipher-Spy empowers human red teamers with an autonomous suite of AI agents that collaboratively perform web reconnaissance, vulnerability analysis, and exploit planning in a controlled environment.

## 🎯 Vision

Cipher-Spy replicates a full red-team workflow – from initial reconnaissance to exploit execution – using orchestrated LLM-driven agents, while keeping the human "pilot" in ultimate control. The system dramatically speeds up penetration testing by automating tedious discovery tasks and suggesting exploit strategies, all within a local, containerized system for security and privacy.

## ✨ Key Features

- **Multi-Agent Orchestration**: Utilizes LangGraph to coordinate specialized AI agents in a directed graph workflow
- **Autonomous Web Crawling**: Automatically navigates target applications using <PERSON><PERSON> with network interception
- **Technology Fingerprinting**: Identifies frameworks, libraries, and security tools using Wappalyzer and WafW00f
- **Graph-RAG Exploit Knowledge**: Integrates Exploit-DB data with knowledge graphs for intelligent vulnerability matching
- **Human-in-the-Loop Control**: Requires human approval for all destructive actions while automating reconnaissance
- **Local-First Deployment**: Runs entirely locally with <PERSON><PERSON> for privacy and security

## 🏗️ Architecture

### Agent Workflow
```
Orchestrator → Crawler → Fingerprinter → Graph-RAG → Exploit Planner → (Human Approval) → Executor
```

### Core Agents
- **Orchestrator Agent**: Central coordinator managing workflow state and agent transitions
- **Crawler Agent**: Playwright-based web reconnaissance and API discovery
- **Fingerprinting Agent**: Technology stack identification and analysis
- **Graph-RAG Agent**: Knowledge base queries for vulnerability matching
- **Exploit Planner Agent**: LLM-driven exploit strategy generation
- **Executor Agent**: Controlled exploit execution with safety measures

## 🛠️ Technology Stack

- **Backend**: Python 3.11+ with FastAPI
- **Agent Orchestration**: LangGraph + LangChain
- **Web Automation**: Playwright
- **Databases**: PostgreSQL (operational data) + Neo4j (knowledge graph)
- **Knowledge Graph**: Graphiti with Exploit-DB integration
- **Fingerprinting**: Wappalyzer, WafW00f, custom detectors
- **LLM Integration**: OpenRouter (optional) + local model support
- **Deployment**: Docker Compose

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- 8GB+ RAM recommended
- OpenRouter API key (optional, for premium LLMs)

### Installation

1. **Clone the repository**
   ```bash
   git clone <cipher-spy-repo>
   cd cipher-spy
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the system**
   ```bash
   docker-compose up --build
   ```

4. **Access the API**
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Entry Points

Cipher-Spy provides multiple ways to run the system:

#### 1. Interactive Menu (Easiest)
```bash
python run.py
```
This provides a user-friendly menu with options for demo, crawling, server, and tests.

#### 2. Command Line Interface
```bash
# Run demo on pump.fun
python cipher-spy.py demo

# Crawl a specific target
python cipher-spy.py crawl https://pump.fun

# Start API server
python cipher-spy.py server

# Get help
python cipher-spy.py --help
```

#### 3. Direct Scripts
```bash
# Interactive demo
python demo_crawler.py

# Component tests
python test_crawler.py

# Setup browsers
python setup_playwright.py
```

### Basic Usage Examples

#### Autonomous Crawling
```bash
# Quick demo on pump.fun
python cipher-spy.py demo

# Crawl with custom settings
python cipher-spy.py crawl https://example.com --depth 3 --pages 50 --delay 2000

# Show browser window for debugging
python cipher-spy.py crawl https://example.com --show-browser --verbose
```

#### API Server Mode
```bash
# Start server
python cipher-spy.py server

# Then use API endpoints:
curl -X POST "http://localhost:8000/api/v1/scans" \
  -H "Content-Type: application/json" \
  -d '{
    "target_url": "https://example.com",
    "max_depth": 3,
    "safe_mode": true
  }'
```

## 📁 Project Structure

```
cipher-spy/
├── src/                    # Main application code
│   ├── agents/            # AI agent implementations
│   ├── api/               # REST API endpoints
│   ├── config/            # Configuration management
│   ├── core/              # Core workflow and state
│   ├── crawling/          # Web crawling components
│   ├── fingerprinting/    # Technology detection
│   ├── knowledge/         # Knowledge graph and RAG
│   ├── exploitation/      # Exploit planning and execution
│   └── utils/             # Utilities and helpers
├── tests/                 # Test suite
├── scripts/               # Setup and maintenance scripts
├── data/                  # Data storage (exploitdb, etc.)
├── docs/                  # Documentation
└── docker/                # Docker configurations
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=**************************************************/cipher_spy
NEO4J_URI=bolt://neo4j:7687

# LLM Configuration
OPENROUTER_API_KEY=your_key_here
DEFAULT_LLM_MODEL=openai/gpt-4-turbo-preview

# Security
SAFE_MODE=true
REQUIRE_APPROVAL_FOR_EXPLOITS=true

# Crawling
MAX_CRAWL_DEPTH=5
MAX_PAGES_PER_DOMAIN=1000
RESPECT_ROBOTS_TXT=true
```

### Scan Configuration

Customize scans via API parameters:

```json
{
  "target_url": "https://target.com",
  "scope": ["https://api.target.com", "*.target.com"],
  "max_depth": 5,
  "max_pages": 1000,
  "credentials": {
    "username": "testuser",
    "password": "testpass"
  },
  "safe_mode": true
}
```

## 🔒 Security Considerations

### Safe Mode
- **Default**: All destructive actions disabled
- **Reconnaissance Only**: Crawling and analysis without exploitation
- **Human Approval**: Required for all exploit execution

### Data Privacy
- **Local Processing**: All data stays on your infrastructure
- **No Cloud Dependencies**: Optional OpenRouter for LLM access only
- **Encrypted Storage**: Sensitive data encrypted at rest

### Scope Management
- **Domain Boundaries**: Automatic scope enforcement
- **Robots.txt Respect**: Configurable compliance
- **Rate Limiting**: Prevents target overload

## 📊 Monitoring and Reporting

### Real-time Progress
- Agent status and progress tracking
- Live finding updates
- Human intervention alerts

### Comprehensive Reports
- Executive summaries
- Technical findings
- Exploit documentation
- Remediation recommendations

## 🧪 Development

### Running Tests
```bash
# Unit tests
pytest tests/unit/

# Integration tests
pytest tests/integration/

# All tests
pytest
```

### Development Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Setup pre-commit hooks
pre-commit install

# Run development server
python -m src.main
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🆘 Support

- **Documentation**: See `docs/` directory
- **Issues**: GitHub Issues
- **Security**: Report security issues privately

## ⚠️ Disclaimer

Cipher-Spy is designed for authorized security testing only. Users are responsible for ensuring they have proper authorization before testing any systems. The developers are not responsible for any misuse of this tool.

---

**Cipher-Spy** - Empowering ethical hackers with AI-driven reconnaissance and analysis.
