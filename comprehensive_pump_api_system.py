#!/usr/bin/env python3
"""
Comprehensive Pump.fun API Testing and Documentation System

Performs enhanced API discovery, testing, cataloging, and automated documentation
generation using OpenRouter API with <PERSON> for professional documentation.
"""

import asyncio
import json
import requests
import time
import os
import sys
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse, parse_qs
import statistics
import hashlib
from dataclasses import dataclass, asdict
import traceback

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor
from src.utils.logging import setup_logging


@dataclass
class APITestResult:
    """Structured API test result."""
    endpoint_name: str
    url: str
    method: str
    parameters: Dict[str, Any]
    success: bool
    status_code: Optional[int]
    response_time_ms: float
    response_size_bytes: int
    content_type: str
    error_message: Optional[str]
    response_data: Optional[Any]
    response_schema: Optional[Dict[str, Any]]
    rate_limit_info: Optional[Dict[str, Any]]
    timestamp: str


@dataclass
class EndpointDocumentation:
    """Generated documentation for an endpoint."""
    endpoint_name: str
    description: str
    use_cases: List[str]
    parameters: Dict[str, Dict[str, Any]]
    response_fields: Dict[str, Dict[str, Any]]
    code_samples: Dict[str, str]
    error_handling: str
    rate_limits: Optional[str]
    authentication: Optional[str]


class ComprehensivePumpAPISystem:
    """
    Comprehensive system for pump.fun API discovery, testing, and documentation.
    """

    def __init__(self, openrouter_api_key: Optional[str] = None):
        self.openrouter_api_key = openrouter_api_key or os.getenv('OPENROUTER_API_KEY')
        self.results_dir = Path("pump_api_comprehensive_results")
        self.results_dir.mkdir(exist_ok=True)

        # Create subdirectories
        (self.results_dir / "responses").mkdir(exist_ok=True)
        (self.results_dir / "schemas").mkdir(exist_ok=True)
        (self.results_dir / "documentation").mkdir(exist_ok=True)
        (self.results_dir / "reports").mkdir(exist_ok=True)

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'Referer': 'https://pump.fun/'
        })

        # Test results storage
        self.test_results: List[APITestResult] = []
        self.discovered_endpoints: List[Dict[str, Any]] = []
        self.response_catalog: Dict[str, Any] = {}
        self.generated_docs: Dict[str, EndpointDocumentation] = {}

        # Known endpoints for comprehensive testing
        self.known_endpoints = {
            'trending_coins': {
                'url': 'https://frontend-api-v3.pump.fun/coins',
                'method': 'GET',
                'category': 'trending',
                'params_variants': [
                    {'offset': 0, 'limit': 50, 'sort': 'market_cap', 'order': 'DESC', 'includeNsfw': 'false'},
                    {'offset': 0, 'limit': 20, 'sort': 'created_timestamp', 'order': 'DESC'},
                    {'offset': 0, 'limit': 100, 'sort': 'last_trade_timestamp', 'order': 'DESC'},
                ],
                'description': 'Get trending coins with various sorting options'
            },
            'for_you_coins': {
                'url': 'https://frontend-api-v3.pump.fun/coins/for-you',
                'method': 'GET',
                'category': 'recommendations',
                'params_variants': [
                    {'offset': 0, 'limit': 48, 'includeNsfw': 'false'},
                    {'offset': 0, 'limit': 20, 'includeNsfw': 'true'},
                ],
                'description': 'Get personalized coin recommendations'
            },
            'search_coins': {
                'url': 'https://frontend-api-v3.pump.fun/search',
                'method': 'GET',
                'category': 'search',
                'params_variants': [
                    {'q': 'bitcoin', 'limit': 20},
                    {'q': 'meme', 'limit': 10},
                    {'q': 'sol', 'limit': 50},
                ],
                'description': 'Search for coins and tokens'
            },
            'coin_details': {
                'url': 'https://frontend-api-v3.pump.fun/coins/{coin_address}',
                'method': 'GET',
                'category': 'coin_details',
                'params_variants': [
                    {},  # Will be populated with real coin addresses
                ],
                'description': 'Get detailed information for specific coins'
            },
            'pump_flags': {
                'url': 'https://pump.fun/api/flags',
                'method': 'GET',
                'category': 'system',
                'params_variants': [{}],
                'description': 'Get pump.fun feature flags and configuration'
            },
            'pump_runners': {
                'url': 'https://pump.fun/api/runners',
                'method': 'GET',
                'category': 'featured',
                'params_variants': [{}],
                'description': 'Get featured/runner coins data'
            }
        }

    async def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """Run the complete comprehensive analysis pipeline."""
        print("🚀 Comprehensive Pump.fun API Analysis System")
        print("="*70)
        print("Performing enhanced discovery, testing, cataloging, and documentation...")
        print()

        try:
            # Phase 1: Enhanced API Discovery
            print("📡 Phase 1: Enhanced API Discovery")
            print("-" * 40)
            discovery_results = await self._enhanced_api_discovery()

            # Phase 2: Comprehensive Testing
            print("\n🧪 Phase 2: Comprehensive API Testing")
            print("-" * 40)
            testing_results = await self._comprehensive_testing()

            # Phase 3: Response Cataloging & Analysis
            print("\n📊 Phase 3: Response Cataloging & Analysis")
            print("-" * 40)
            cataloging_results = await self._response_cataloging()

            # Phase 4: Automated Documentation Generation
            print("\n📚 Phase 4: Automated Documentation Generation")
            print("-" * 40)
            documentation_results = await self._automated_documentation()

            # Phase 5: Master Report Generation
            print("\n📋 Phase 5: Master Report Generation")
            print("-" * 40)
            master_report = await self._generate_master_report(
                discovery_results, testing_results, cataloging_results, documentation_results
            )

            print(f"\n✅ Comprehensive analysis completed!")
            print(f"📁 Results saved to: {self.results_dir}")

            return master_report

        except Exception as e:
            print(f"\n💥 Comprehensive analysis failed: {e}")
            traceback.print_exc()
            return {'error': str(e), 'traceback': traceback.format_exc()}

    async def _enhanced_api_discovery(self) -> Dict[str, Any]:
        """Enhanced API discovery through autonomous navigation."""
        print("🕷️  Discovering APIs through autonomous navigation...")

        interceptor = NetworkInterceptor()
        crawler = PlaywrightCrawler(headless=True, delay_ms=1000)

        discovered_apis = []

        try:
            await crawler.start()
            page = crawler.page
            await interceptor.setup_page(page)

            # Navigate to multiple pump.fun pages
            pages_to_explore = [
                "https://pump.fun",
                "https://pump.fun/board",
                "https://pump.fun/create",
                # Add more as discovered
            ]

            for url in pages_to_explore:
                try:
                    print(f"   📄 Exploring {url}...")
                    await page.goto(url, wait_until="domcontentloaded", timeout=20000)
                    await asyncio.sleep(4)  # Wait for API calls

                    # Try some interactions to trigger more API calls
                    try:
                        # Look for buttons and links to click
                        buttons = await page.locator('button, a[href]').all()
                        for i, button in enumerate(buttons[:5]):  # Click first 5 safe elements
                            try:
                                text = await button.text_content()
                                if text and not any(dangerous in text.lower() for dangerous in ['delete', 'logout', 'sign out']):
                                    await button.click()
                                    await asyncio.sleep(1)
                            except:
                                continue
                    except:
                        pass

                except Exception as e:
                    print(f"   ⚠️  {url} exploration failed: {e}")
                    continue

            # Get discovered endpoints
            endpoints = interceptor.get_discovered_endpoints()
            discovered_apis.extend(endpoints)

            print(f"   ✅ Discovered {len(discovered_apis)} API endpoints through navigation")

        except Exception as e:
            print(f"   💥 Discovery error: {e}")

        finally:
            try:
                await crawler.stop()
            except:
                pass

        # Combine with known endpoints
        self.discovered_endpoints = discovered_apis

        # Save discovery results
        discovery_file = self.results_dir / "discovery_results.json"
        with open(discovery_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'discovered_endpoints': discovered_apis,
                'total_discovered': len(discovered_apis)
            }, f, indent=2, default=str)

        return {
            'discovered_endpoints': len(discovered_apis),
            'discovery_successful': True,
            'discovery_file': str(discovery_file)
        }

    async def _comprehensive_testing(self) -> Dict[str, Any]:
        """Comprehensive testing of all discovered and known endpoints."""
        print("🧪 Testing all discovered and known endpoints...")

        total_tests = 0
        successful_tests = 0

        # Test known endpoints with multiple parameter variants
        for endpoint_name, endpoint_config in self.known_endpoints.items():
            print(f"   🔍 Testing {endpoint_name}...")

            for i, params in enumerate(endpoint_config['params_variants']):
                test_name = f"{endpoint_name}_variant_{i+1}"

                # Handle parameterized URLs
                url = endpoint_config['url']
                if '{coin_address}' in url:
                    # Get a real coin address from trending coins first
                    coin_address = await self._get_sample_coin_address()
                    if coin_address:
                        url = url.replace('{coin_address}', coin_address)
                    else:
                        continue

                result = await self._test_endpoint_with_retries(
                    test_name, url, endpoint_config['method'], params, endpoint_config['category']
                )

                self.test_results.append(result)
                total_tests += 1

                if result.success:
                    successful_tests += 1
                    print(f"      ✅ {test_name}: {result.status_code} ({result.response_time_ms:.0f}ms)")
                else:
                    print(f"      ❌ {test_name}: {result.error_message}")

                # Rate limiting
                await asyncio.sleep(1)

        # Test discovered endpoints
        for endpoint in self.discovered_endpoints[:10]:  # Test first 10 discovered
            if self._is_testable_endpoint(endpoint):
                test_name = f"discovered_{hashlib.md5(endpoint['url'].encode()).hexdigest()[:8]}"

                result = await self._test_endpoint_with_retries(
                    test_name, endpoint['url'], endpoint.get('method', 'GET'), {}, 'discovered'
                )

                self.test_results.append(result)
                total_tests += 1

                if result.success:
                    successful_tests += 1

                await asyncio.sleep(1)

        # Save test results
        test_results_file = self.results_dir / "test_results.json"
        with open(test_results_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': f"{(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                'test_results': [asdict(result) for result in self.test_results]
            }, f, indent=2, default=str)

        print(f"   📊 Testing complete: {successful_tests}/{total_tests} successful ({successful_tests/total_tests*100:.1f}%)")

        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests/total_tests if total_tests > 0 else 0,
            'test_results_file': str(test_results_file)
        }

    async def _test_endpoint_with_retries(self, name: str, url: str, method: str,
                                        params: Dict[str, Any], category: str,
                                        max_retries: int = 3) -> APITestResult:
        """Test an endpoint with retry logic and comprehensive metrics."""

        for attempt in range(max_retries):
            start_time = time.time()

            try:
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=15)
                elif method.upper() == 'POST':
                    response = self.session.post(url, json=params, timeout=15)
                else:
                    return APITestResult(
                        endpoint_name=name,
                        url=url,
                        method=method,
                        parameters=params,
                        success=False,
                        status_code=None,
                        response_time_ms=0,
                        response_size_bytes=0,
                        content_type='',
                        error_message=f'Unsupported method: {method}',
                        response_data=None,
                        response_schema=None,
                        rate_limit_info=None,
                        timestamp=datetime.now().isoformat()
                    )

                response_time_ms = (time.time() - start_time) * 1000

                # Extract rate limit info
                rate_limit_info = self._extract_rate_limit_info(response.headers)

                # Parse response
                response_data = None
                response_schema = None

                try:
                    if 'application/json' in response.headers.get('content-type', ''):
                        response_data = response.json()
                        response_schema = self._infer_schema(response_data)
                except:
                    response_data = response.text[:1000] if response.text else None

                return APITestResult(
                    endpoint_name=name,
                    url=response.url,
                    method=method,
                    parameters=params,
                    success=200 <= response.status_code < 300,
                    status_code=response.status_code,
                    response_time_ms=response_time_ms,
                    response_size_bytes=len(response.content),
                    content_type=response.headers.get('content-type', ''),
                    error_message=None if 200 <= response.status_code < 300 else f"HTTP {response.status_code}",
                    response_data=response_data,
                    response_schema=response_schema,
                    rate_limit_info=rate_limit_info,
                    timestamp=datetime.now().isoformat()
                )

            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    return APITestResult(
                        endpoint_name=name,
                        url=url,
                        method=method,
                        parameters=params,
                        success=False,
                        status_code=None,
                        response_time_ms=(time.time() - start_time) * 1000,
                        response_size_bytes=0,
                        content_type='',
                        error_message='Request timeout',
                        response_data=None,
                        response_schema=None,
                        rate_limit_info=None,
                        timestamp=datetime.now().isoformat()
                    )
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

            except Exception as e:
                if attempt == max_retries - 1:
                    return APITestResult(
                        endpoint_name=name,
                        url=url,
                        method=method,
                        parameters=params,
                        success=False,
                        status_code=None,
                        response_time_ms=(time.time() - start_time) * 1000,
                        response_size_bytes=0,
                        content_type='',
                        error_message=str(e),
                        response_data=None,
                        response_schema=None,
                        rate_limit_info=None,
                        timestamp=datetime.now().isoformat()
                    )
                await asyncio.sleep(2 ** attempt)

    async def _get_sample_coin_address(self) -> Optional[str]:
        """Get a sample coin address from trending coins."""
        try:
            response = self.session.get(
                'https://frontend-api-v3.pump.fun/coins',
                params={'offset': 0, 'limit': 1, 'sort': 'market_cap', 'order': 'DESC'},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    return data[0].get('mint')
        except:
            pass

        return None

    def _is_testable_endpoint(self, endpoint: Dict[str, Any]) -> bool:
        """Check if an endpoint is safe to test."""
        url = endpoint.get('url', '').lower()
        method = endpoint.get('method', 'GET').upper()

        # Only test GET requests for safety
        if method != 'GET':
            return False

        # Skip static assets
        static_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.ttf']
        if any(url.endswith(ext) for ext in static_extensions):
            return False

        # Skip analytics and tracking
        if any(term in url for term in ['analytics', 'tracking', 'gtm', 'datadog']):
            return False

        return True

    def _extract_rate_limit_info(self, headers: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Extract rate limiting information from response headers."""
        rate_limit_info = {}

        # Common rate limit headers
        rate_limit_headers = {
            'x-ratelimit-limit': 'limit',
            'x-ratelimit-remaining': 'remaining',
            'x-ratelimit-reset': 'reset',
            'retry-after': 'retry_after'
        }

        for header, key in rate_limit_headers.items():
            if header in headers:
                rate_limit_info[key] = headers[header]

        return rate_limit_info if rate_limit_info else None

    def _infer_schema(self, data: Any, max_depth: int = 3) -> Dict[str, Any]:
        """Infer JSON schema from response data."""
        if max_depth <= 0:
            return {'type': 'unknown', 'reason': 'max_depth_reached'}

        if data is None:
            return {'type': 'null'}
        elif isinstance(data, bool):
            return {'type': 'boolean'}
        elif isinstance(data, int):
            return {'type': 'integer'}
        elif isinstance(data, float):
            return {'type': 'number'}
        elif isinstance(data, str):
            return {'type': 'string', 'example': data[:50]}
        elif isinstance(data, list):
            if len(data) == 0:
                return {'type': 'array', 'items': {'type': 'unknown'}}
            else:
                return {
                    'type': 'array',
                    'length': len(data),
                    'items': self._infer_schema(data[0], max_depth - 1)
                }
        elif isinstance(data, dict):
            properties = {}
            for key, value in list(data.items())[:10]:  # Limit to first 10 keys
                properties[key] = self._infer_schema(value, max_depth - 1)

            return {
                'type': 'object',
                'properties': properties,
                'total_keys': len(data)
            }
        else:
            return {'type': 'unknown', 'python_type': str(type(data))}

    async def _response_cataloging(self) -> Dict[str, Any]:
        """Catalog and analyze all successful responses."""
        print("📊 Cataloging and analyzing successful responses...")

        successful_results = [r for r in self.test_results if r.success and r.response_data]

        # Group by category
        categorized_responses = {}

        for result in successful_results:
            # Determine category from endpoint name or URL
            category = self._determine_category(result)

            if category not in categorized_responses:
                categorized_responses[category] = []

            categorized_responses[category].append(result)

            # Save individual response
            response_file = self.results_dir / "responses" / f"{result.endpoint_name}_response.json"
            with open(response_file, 'w') as f:
                json.dump({
                    'endpoint_info': {
                        'name': result.endpoint_name,
                        'url': result.url,
                        'method': result.method,
                        'parameters': result.parameters,
                        'timestamp': result.timestamp
                    },
                    'response_data': result.response_data,
                    'response_schema': result.response_schema,
                    'metrics': {
                        'response_time_ms': result.response_time_ms,
                        'response_size_bytes': result.response_size_bytes,
                        'content_type': result.content_type
                    }
                }, f, indent=2, default=str)

            # Save schema
            if result.response_schema:
                schema_file = self.results_dir / "schemas" / f"{result.endpoint_name}_schema.json"
                with open(schema_file, 'w') as f:
                    json.dump(result.response_schema, f, indent=2)

        # Generate category summaries
        category_summaries = {}
        for category, results in categorized_responses.items():
            category_summaries[category] = {
                'endpoint_count': len(results),
                'avg_response_time_ms': statistics.mean([r.response_time_ms for r in results]),
                'avg_response_size_bytes': statistics.mean([r.response_size_bytes for r in results]),
                'endpoints': [r.endpoint_name for r in results]
            }

        # Save cataloging results
        catalog_file = self.results_dir / "response_catalog.json"
        with open(catalog_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'total_successful_endpoints': len(successful_results),
                'categories': category_summaries,
                'detailed_results': [asdict(r) for r in successful_results]
            }, f, indent=2, default=str)

        self.response_catalog = categorized_responses

        print(f"   📁 Cataloged {len(successful_results)} successful responses across {len(categorized_responses)} categories")

        return {
            'successful_endpoints': len(successful_results),
            'categories': len(categorized_responses),
            'category_summaries': category_summaries,
            'catalog_file': str(catalog_file)
        }

    def _determine_category(self, result: APITestResult) -> str:
        """Determine the category of an API endpoint."""
        url = result.url.lower()
        name = result.endpoint_name.lower()

        if 'trending' in name or 'coins' in url and 'for-you' not in url:
            return 'trending'
        elif 'for-you' in url or 'recommendation' in name:
            return 'recommendations'
        elif 'search' in url or 'search' in name:
            return 'search'
        elif 'coin' in url and ('/' in url.split('coins/')[-1] if 'coins/' in url else False):
            return 'coin_details'
        elif 'flags' in url or 'flag' in name:
            return 'system'
        elif 'runners' in url or 'runner' in name:
            return 'featured'
        else:
            return 'other'

    async def _automated_documentation(self) -> Dict[str, Any]:
        """Generate automated documentation using OpenRouter API with Claude."""
        print("📚 Generating automated documentation using Claude...")

        if not self.openrouter_api_key:
            print("   ⚠️  No OpenRouter API key provided, skipping automated documentation")
            return {'documentation_generated': False, 'reason': 'no_api_key'}

        successful_results = [r for r in self.test_results if r.success and r.response_data]
        documentation_count = 0

        # Group by category for better documentation
        for category, results in self.response_catalog.items():
            print(f"   📖 Generating documentation for {category} category...")

            for result in results[:3]:  # Limit to 3 per category to manage API costs
                try:
                    doc = await self._generate_endpoint_documentation(result, category)
                    if doc:
                        self.generated_docs[result.endpoint_name] = doc
                        documentation_count += 1

                        # Save individual documentation
                        doc_file = self.results_dir / "documentation" / f"{result.endpoint_name}_docs.md"
                        with open(doc_file, 'w') as f:
                            f.write(self._format_documentation_markdown(doc))

                        print(f"      ✅ Generated docs for {result.endpoint_name}")

                    # Rate limit for API calls
                    await asyncio.sleep(2)

                except Exception as e:
                    print(f"      ❌ Failed to generate docs for {result.endpoint_name}: {e}")

        # Generate master documentation file
        master_doc_file = self.results_dir / "documentation" / "pump_fun_api_documentation.md"
        with open(master_doc_file, 'w') as f:
            f.write(self._generate_master_documentation())

        print(f"   📚 Generated documentation for {documentation_count} endpoints")

        return {
            'documentation_generated': True,
            'endpoints_documented': documentation_count,
            'master_doc_file': str(master_doc_file)
        }
