/* Cipher-Spy DevTools Panel Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  color: #333;
  background: #f8f9fa;
  height: 100vh;
  overflow: hidden;
}

.panel-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Header */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon {
  font-size: 16px;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: #1a73e8;
}

.controls {
  display: flex;
  gap: 8px;
}

/* Buttons */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover {
  background: #1557b0;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

/* Tabs */
.tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #e1e5e9;
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: none;
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #495057;
  background: #f8f9fa;
}

.tab-button.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow: hidden;
}

.tab-pane {
  display: none;
  height: 100%;
  overflow-y: auto;
}

.tab-pane.active {
  display: block;
}

/* Filter Bar */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
}

.filter-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 11px;
}

.filter-select {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 11px;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  cursor: pointer;
}

/* Requests Table */
.requests-table {
  height: calc(100% - 60px);
  overflow: hidden;
}

.table-header {
  display: flex;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  font-weight: 600;
  font-size: 11px;
  color: #495057;
}

.table-body {
  height: calc(100% - 32px);
  overflow-y: auto;
}

.col-method { width: 60px; }
.col-url { flex: 1; }
.col-status { width: 60px; }
.col-type { width: 80px; }
.col-time { width: 80px; }

.request-row {
  display: flex;
  padding: 6px 16px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 11px;
  cursor: pointer;
  transition: background 0.2s;
}

.request-row:hover {
  background: #f8f9fa;
}

.request-row.selected {
  background: #e3f2fd;
}

.method-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  font-size: 9px;
  text-align: center;
}

.method-GET { background: #d4edda; color: #155724; }
.method-POST { background: #d1ecf1; color: #0c5460; }
.method-PUT { background: #fff3cd; color: #856404; }
.method-DELETE { background: #f8d7da; color: #721c24; }

.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 600;
  font-size: 9px;
  text-align: center;
}

.status-2xx { background: #d4edda; color: #155724; }
.status-3xx { background: #fff3cd; color: #856404; }
.status-4xx { background: #f8d7da; color: #721c24; }
.status-5xx { background: #f5c6cb; color: #721c24; }

/* Analysis Section */
.analysis-section {
  padding: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1a73e8;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #6c757d;
}

.analysis-details h4 {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
}

.tech-list, .patterns-list {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
  min-height: 60px;
}

.tech-item, .pattern-item {
  display: inline-block;
  padding: 4px 8px;
  margin: 2px;
  background: #e3f2fd;
  color: #1565c0;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

/* Discovery Section */
.discovery-section {
  padding: 16px;
}

.discovery-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.discovery-status {
  margin-bottom: 24px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: #1a73e8;
  width: 0%;
  transition: width 0.3s;
}

.status-text {
  font-size: 11px;
  color: #6c757d;
}

.endpoints-list {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  padding: 12px;
  min-height: 200px;
}

.endpoint-item {
  padding: 8px;
  border-bottom: 1px solid #f1f3f4;
  font-size: 11px;
}

.endpoint-item:last-child {
  border-bottom: none;
}

.endpoint-url {
  font-weight: 500;
  color: #1a73e8;
  margin-bottom: 2px;
}

.endpoint-details {
  color: #6c757d;
  font-size: 10px;
}

/* Documentation Section */
.docs-section {
  padding: 16px;
}

.docs-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.format-select {
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 11px;
}

.docs-content {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  padding: 16px;
  min-height: 300px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  overflow-y: auto;
}

.docs-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Empty State */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #6c757d;
  font-size: 11px;
  text-align: center;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
