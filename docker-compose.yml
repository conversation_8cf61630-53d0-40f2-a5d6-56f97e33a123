version: '3.8'

services:
  cipher-spy:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************************/cipher_spy
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=cipher_neo4j
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=${ENVIRONMENT:-development}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - playwright_cache:/app/.cache
    depends_on:
      - postgres
      - neo4j
    networks:
      - cipher-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=cipher_spy
      - POSTGRES_USER=cipher_user
      - POSTGRES_PASSWORD=cipher_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    ports:
      - "5432:5432"
    networks:
      - cipher-network
    restart: unless-stopped

  neo4j:
    image: neo4j:5.13-community
    environment:
      - NEO4J_AUTH=neo4j/cipher_neo4j
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    networks:
      - cipher-network
    restart: unless-stopped

  # Optional: Vector database for embeddings (if not using Neo4j vectors)
  chroma:
    image: chromadb/chroma:latest
    ports:
      - "8001:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - cipher-network
    restart: unless-stopped

volumes:
  postgres_data:
  neo4j_data:
  neo4j_logs:
  chroma_data:
  playwright_cache:

networks:
  cipher-network:
    driver: bridge
