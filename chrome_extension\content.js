/**
 * Cipher-Spy Network Monitor - Content Script
 *
 * Injected into web pages to provide additional monitoring capabilities
 * and real-time feedback about network activity.
 */

// Global state
let isMonitoring = false;
let sessionId = null;
let pageAnalysis = {
  apiCalls: [],
  patterns: [],
  insights: []
};

// Initialize content script
(function() {
  console.log('Cipher-Spy content script loaded');

  // Request current monitoring status
  chrome.runtime.sendMessage({ action: 'getMonitoringStatus' });

  // Initialize page analysis
  initializePageAnalysis();

  // Set up DOM observers
  setupDOMObservers();

  // Inject page-level monitoring
  injectPageMonitoring();
})();

// Message handling from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  handleBackgroundMessage(message, sender, sendResponse);
  return true;
});

function handleBackgroundMessage(message, sender, sendResponse) {
  switch (message.action) {
    case 'monitoringStarted':
      isMonitoring = true;
      sessionId = message.data.sessionId;
      showMonitoringIndicator();
      sendResponse({ success: true });
      break;

    case 'monitoringStopped':
      isMonitoring = false;
      sessionId = null;
      hideMonitoringIndicator();
      sendResponse({ success: true });
      break;

    case 'requestCaptured':
      handleRequestCaptured(message.data);
      sendResponse({ success: true });
      break;

    case 'monitoringStatus':
      isMonitoring = message.data.isMonitoring;
      sessionId = message.data.sessionId;
      if (isMonitoring) {
        showMonitoringIndicator();
      }
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ error: 'Unknown action' });
  }
}

// Page analysis initialization
function initializePageAnalysis() {
  // Analyze current page for API patterns
  analyzePageForAPIs();

  // Set up periodic analysis
  setInterval(analyzePageForAPIs, 5000); // Every 5 seconds
}

function analyzePageForAPIs() {
  const analysis = {
    timestamp: Date.now(),
    url: window.location.href,
    title: document.title,
    apiIndicators: findAPIIndicators(),
    formElements: analyzeFormElements(),
    jsFrameworks: detectJSFrameworks(),
    ajaxPatterns: detectAJAXPatterns()
  };

  pageAnalysis.insights.push(analysis);

  // Keep only last 10 analyses
  if (pageAnalysis.insights.length > 10) {
    pageAnalysis.insights.shift();
  }

  // Send to background for processing
  if (isMonitoring) {
    chrome.runtime.sendMessage({
      action: 'pageAnalysis',
      data: analysis
    });
  }
}

// API indicator detection
function findAPIIndicators() {
  const indicators = [];

  // Look for API-related text in page
  const apiKeywords = ['api', 'endpoint', 'rest', 'graphql', 'json', 'xml'];
  const pageText = document.body.textContent.toLowerCase();

  apiKeywords.forEach(keyword => {
    if (pageText.includes(keyword)) {
      indicators.push(`text_contains_${keyword}`);
    }
  });

  // Look for API-related links
  const links = document.querySelectorAll('a[href]');
  links.forEach(link => {
    const href = link.href.toLowerCase();
    if (href.includes('/api/') || href.includes('.json') || href.includes('/rest/')) {
      indicators.push(`api_link: ${link.href}`);
    }
  });

  // Look for developer tools or documentation
  const devIndicators = ['swagger', 'openapi', 'postman', 'curl', 'documentation'];
  devIndicators.forEach(indicator => {
    if (pageText.includes(indicator)) {
      indicators.push(`dev_tool_${indicator}`);
    }
  });

  return indicators;
}

// Form analysis
function analyzeFormElements() {
  const forms = document.querySelectorAll('form');
  const formAnalysis = [];

  forms.forEach((form, index) => {
    const analysis = {
      index: index,
      action: form.action,
      method: form.method,
      inputs: [],
      hasFileUpload: false,
      hasHiddenFields: false
    };

    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      analysis.inputs.push({
        type: input.type,
        name: input.name,
        id: input.id,
        required: input.required
      });

      if (input.type === 'file') {
        analysis.hasFileUpload = true;
      }
      if (input.type === 'hidden') {
        analysis.hasHiddenFields = true;
      }
    });

    formAnalysis.push(analysis);
  });

  return formAnalysis;
}

// JavaScript framework detection
function detectJSFrameworks() {
  const frameworks = [];

  // Check for common frameworks
  if (window.React) frameworks.push('React');
  if (window.Vue) frameworks.push('Vue');
  if (window.angular) frameworks.push('Angular');
  if (window.jQuery || window.$) frameworks.push('jQuery');
  if (window.Ember) frameworks.push('Ember');
  if (window.Backbone) frameworks.push('Backbone');

  // Check for framework-specific attributes
  if (document.querySelector('[ng-app], [data-ng-app]')) frameworks.push('Angular');
  if (document.querySelector('[v-app], [data-v-app]')) frameworks.push('Vue');

  return frameworks;
}

// AJAX pattern detection
function detectAJAXPatterns() {
  const patterns = [];

  // Override XMLHttpRequest to detect AJAX calls
  if (!window._cipherSpyXHROverridden) {
    const originalXHR = window.XMLHttpRequest;
    const originalFetch = window.fetch;

    // XMLHttpRequest override
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;

      xhr.open = function(method, url, ...args) {
        this._cipherSpyMethod = method;
        this._cipherSpyUrl = url;
        return originalOpen.apply(this, [method, url, ...args]);
      };

      xhr.send = function(data) {
        patterns.push({
          type: 'XMLHttpRequest',
          method: this._cipherSpyMethod,
          url: this._cipherSpyUrl,
          timestamp: Date.now()
        });
        return originalSend.apply(this, arguments);
      };

      return xhr;
    };

    // Fetch override
    window.fetch = function(url, options = {}) {
      patterns.push({
        type: 'fetch',
        method: options.method || 'GET',
        url: url,
        timestamp: Date.now()
      });
      return originalFetch.apply(this, arguments);
    };

    window._cipherSpyXHROverridden = true;
  }

  return patterns;
}

// DOM observers
function setupDOMObservers() {
  // Observe DOM changes for dynamic content
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // Check for new API-related elements
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            checkElementForAPIPatterns(node);
          }
        });
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

function checkElementForAPIPatterns(element) {
  // Check for API-related attributes or content
  const apiPatterns = [
    'data-api',
    'data-endpoint',
    'api-url',
    'rest-endpoint'
  ];

  apiPatterns.forEach(pattern => {
    if (element.hasAttribute && element.hasAttribute(pattern)) {
      pageAnalysis.patterns.push({
        type: 'dom_attribute',
        pattern: pattern,
        value: element.getAttribute(pattern),
        timestamp: Date.now()
      });
    }
  });

  // Check for API-related text content
  if (element.textContent) {
    const text = element.textContent.toLowerCase();
    if (text.includes('api') || text.includes('endpoint')) {
      pageAnalysis.patterns.push({
        type: 'dom_text',
        content: element.textContent.substring(0, 100),
        timestamp: Date.now()
      });
    }
  }
}

// Page-level monitoring injection
function injectPageMonitoring() {
  // Inject a script to monitor page-level network activity
  const script = document.createElement('script');
  script.textContent = `
    (function() {
      // Monitor console for API-related logs
      const originalLog = console.log;
      const originalError = console.error;

      console.log = function(...args) {
        const message = args.join(' ');
        if (message.toLowerCase().includes('api') || message.toLowerCase().includes('fetch')) {
          window.postMessage({
            type: 'CIPHER_SPY_CONSOLE',
            level: 'log',
            message: message,
            timestamp: Date.now()
          }, '*');
        }
        return originalLog.apply(this, args);
      };

      console.error = function(...args) {
        const message = args.join(' ');
        if (message.toLowerCase().includes('api') || message.toLowerCase().includes('fetch')) {
          window.postMessage({
            type: 'CIPHER_SPY_CONSOLE',
            level: 'error',
            message: message,
            timestamp: Date.now()
          }, '*');
        }
        return originalError.apply(this, args);
      };
    })();
  `;

  document.documentElement.appendChild(script);
  script.remove();

  // Listen for messages from injected script
  window.addEventListener('message', (event) => {
    if (event.data.type === 'CIPHER_SPY_CONSOLE') {
      pageAnalysis.patterns.push({
        type: 'console_log',
        level: event.data.level,
        message: event.data.message,
        timestamp: event.data.timestamp
      });
    }
  });
}

// Request handling
function handleRequestCaptured(requestData) {
  pageAnalysis.apiCalls.push({
    id: requestData.id,
    url: requestData.url,
    method: requestData.method,
    timestamp: requestData.timestamp
  });

  // Keep only last 50 API calls
  if (pageAnalysis.apiCalls.length > 50) {
    pageAnalysis.apiCalls.shift();
  }

  // Update monitoring indicator
  updateMonitoringIndicator();
}

// Visual indicators
function showMonitoringIndicator() {
  if (document.getElementById('cipher-spy-indicator')) return;

  const indicator = document.createElement('div');
  indicator.id = 'cipher-spy-indicator';
  indicator.innerHTML = `
    <div style="
      position: fixed;
      top: 10px;
      right: 10px;
      background: #4CAF50;
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-family: Arial, sans-serif;
      font-size: 12px;
      z-index: 10000;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      cursor: pointer;
    ">
      CS Monitoring
      <span id="cipher-spy-count">0</span>
    </div>
  `;

  indicator.addEventListener('click', () => {
    showMonitoringDetails();
  });

  document.body.appendChild(indicator);
}

function hideMonitoringIndicator() {
  const indicator = document.getElementById('cipher-spy-indicator');
  if (indicator) {
    indicator.remove();
  }
}

function updateMonitoringIndicator() {
  const countElement = document.getElementById('cipher-spy-count');
  if (countElement) {
    countElement.textContent = pageAnalysis.apiCalls.length;
  }
}

function showMonitoringDetails() {
  // Create a modal with monitoring details
  const modal = document.createElement('div');
  modal.id = 'cipher-spy-modal';
  modal.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    ">
      <div style="
        background: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 600px;
        max-height: 80%;
        overflow-y: auto;
        font-family: Arial, sans-serif;
      ">
        <h3>Cipher-Spy Monitoring Details</h3>
        <p><strong>Session ID:</strong> ${sessionId}</p>
        <p><strong>API Calls Captured:</strong> ${pageAnalysis.apiCalls.length}</p>
        <p><strong>Patterns Detected:</strong> ${pageAnalysis.patterns.length}</p>

        <h4>Recent API Calls:</h4>
        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
          ${pageAnalysis.apiCalls.slice(-10).map(call => `
            <div style="margin-bottom: 5px; font-size: 12px;">
              <strong>${call.method}</strong> ${call.url}
            </div>
          `).join('')}
        </div>

        <div style="margin-top: 15px; text-align: right;">
          <button onclick="document.getElementById('cipher-spy-modal').remove()" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
          ">Close</button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (isMonitoring) {
    chrome.runtime.sendMessage({
      action: 'pageUnload',
      data: {
        url: window.location.href,
        analysis: pageAnalysis
      }
    });
  }
});
