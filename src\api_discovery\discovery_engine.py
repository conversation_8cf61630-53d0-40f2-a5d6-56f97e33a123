#!/usr/bin/env python3
"""
Discovery Engine for Cipher-Spy Universal API Discovery

Orchestrates the entire API discovery process, coordinating different
analyzers, strategies, and data sources to provide comprehensive
API intelligence for any target website.
"""

import asyncio
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Type
from dataclasses import asdict

from .base_analyzer import BaseAPIAnalyzer, DiscoveryResult
from .universal_analyzer import UniversalAPIAnalyzer
from .target_config import TargetConfig
from .documentation_generator import DocumentationGenerator


class DiscoveryEngine:
    """
    Central orchestrator for API discovery operations.
    
    This engine coordinates multiple analyzers, manages discovery workflows,
    and provides a unified interface for comprehensive API intelligence.
    """

    def __init__(self, results_dir: Optional[Path] = None):
        """
        Initialize the discovery engine.
        
        Args:
            results_dir: Base directory for saving results
        """
        self.results_dir = results_dir or Path("universal_api_discovery")
        self.results_dir.mkdir(exist_ok=True)
        
        # Registry of available analyzers
        self.analyzer_registry: Dict[str, Type[BaseAPIAnalyzer]] = {
            'universal': UniversalAPIAnalyzer,
            # Additional analyzers can be registered here
        }
        
        # Active discovery sessions
        self.active_sessions: Dict[str, BaseAPIAnalyzer] = {}
        
        # Discovery history
        self.discovery_history: List[DiscoveryResult] = []

    def register_analyzer(self, name: str, analyzer_class: Type[BaseAPIAnalyzer]):
        """
        Register a new analyzer type.
        
        Args:
            name: Name for the analyzer
            analyzer_class: Analyzer class to register
        """
        self.analyzer_registry[name] = analyzer_class
        print(f"📝 Registered analyzer: {name}")

    async def discover_target(
        self, 
        target_config: TargetConfig,
        analyzer_type: str = 'universal',
        generate_docs: bool = True,
        generate_clients: bool = True
    ) -> DiscoveryResult:
        """
        Perform comprehensive API discovery for a target.
        
        Args:
            target_config: Configuration for the target website
            analyzer_type: Type of analyzer to use
            generate_docs: Whether to generate documentation
            generate_clients: Whether to generate client code
            
        Returns:
            DiscoveryResult: Complete discovery results
        """
        print(f"🚀 Starting API Discovery for {target_config.domain}")
        print("="*80)
        
        # Create target-specific results directory
        target_results_dir = self.results_dir / f"{target_config.domain}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        target_results_dir.mkdir(exist_ok=True)
        
        try:
            # Initialize analyzer
            if analyzer_type not in self.analyzer_registry:
                raise ValueError(f"Unknown analyzer type: {analyzer_type}")
            
            analyzer_class = self.analyzer_registry[analyzer_type]
            analyzer = analyzer_class(target_config, results_dir=target_results_dir)
            
            # Store active session
            session_id = f"{target_config.domain}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.active_sessions[session_id] = analyzer
            
            # Perform discovery
            print(f"🔬 Using {analyzer_type} analyzer")
            result = await analyzer.perform_comprehensive_analysis()
            
            # Post-processing
            if generate_docs:
                print("\n📚 Generating Documentation")
                print("-" * 50)
                await self._generate_documentation(result, target_results_dir)
            
            if generate_clients:
                print("\n🛠️ Generating Client Code")
                print("-" * 50)
                await self._generate_client_code(result, target_results_dir)
            
            # Save discovery metadata
            await self._save_discovery_metadata(result, target_results_dir, session_id)
            
            # Add to history
            self.discovery_history.append(result)
            
            # Cleanup session
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            print(f"\n✅ Discovery completed successfully!")
            print(f"📁 Results saved to: {target_results_dir}")
            
            return result
            
        except Exception as e:
            print(f"\n💥 Discovery failed: {e}")
            import traceback
            traceback.print_exc()
            raise

    async def discover_multiple_targets(
        self, 
        target_configs: List[TargetConfig],
        analyzer_type: str = 'universal',
        concurrent_limit: int = 3
    ) -> List[DiscoveryResult]:
        """
        Discover multiple targets concurrently.
        
        Args:
            target_configs: List of target configurations
            analyzer_type: Type of analyzer to use
            concurrent_limit: Maximum concurrent discoveries
            
        Returns:
            List[DiscoveryResult]: Results for all targets
        """
        print(f"🎯 Starting discovery for {len(target_configs)} targets")
        
        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def discover_with_semaphore(config: TargetConfig) -> DiscoveryResult:
            async with semaphore:
                return await self.discover_target(config, analyzer_type)
        
        # Run discoveries concurrently
        tasks = [discover_with_semaphore(config) for config in target_configs]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log them
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"❌ Discovery failed for {target_configs[i].domain}: {result}")
            else:
                successful_results.append(result)
        
        print(f"✅ Completed {len(successful_results)}/{len(target_configs)} discoveries")
        return successful_results

    async def compare_targets(
        self, 
        target_configs: List[TargetConfig],
        analyzer_type: str = 'universal'
    ) -> Dict[str, Any]:
        """
        Compare API structures across multiple targets.
        
        Args:
            target_configs: List of target configurations
            analyzer_type: Type of analyzer to use
            
        Returns:
            Dict[str, Any]: Comparative analysis results
        """
        print(f"🔍 Performing comparative analysis of {len(target_configs)} targets")
        
        # Discover all targets
        results = await self.discover_multiple_targets(target_configs, analyzer_type)
        
        # Perform comparative analysis
        comparison = {
            'targets': [result.target_domain for result in results],
            'endpoint_comparison': self._compare_endpoints(results),
            'parameter_comparison': self._compare_parameters(results),
            'schema_comparison': self._compare_schemas(results),
            'business_comparison': self._compare_business_logic(results),
            'timestamp': datetime.now().isoformat()
        }
        
        # Save comparison results
        comparison_file = self.results_dir / f"comparative_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(comparison_file, 'w') as f:
            json.dump(comparison, f, indent=2, default=str)
        
        print(f"📊 Comparative analysis saved to: {comparison_file}")
        return comparison

    def _compare_endpoints(self, results: List[DiscoveryResult]) -> Dict[str, Any]:
        """Compare endpoints across targets."""
        comparison = {
            'total_endpoints': {},
            'common_patterns': [],
            'unique_endpoints': {},
            'endpoint_overlap': {}
        }
        
        for result in results:
            domain = result.target_domain
            endpoints = [ep.endpoint_url for ep in result.discovered_endpoints]
            comparison['total_endpoints'][domain] = len(endpoints)
            comparison['unique_endpoints'][domain] = endpoints
        
        # Find common patterns
        all_endpoints = []
        for result in results:
            all_endpoints.extend([ep.endpoint_url for ep in result.discovered_endpoints])
        
        # Simple pattern detection (can be enhanced)
        patterns = {}
        for endpoint in all_endpoints:
            # Extract path patterns
            path_parts = endpoint.split('/')
            for part in path_parts:
                if part and not part.startswith('http'):
                    patterns[part] = patterns.get(part, 0) + 1
        
        # Common patterns are those that appear in multiple targets
        comparison['common_patterns'] = [
            pattern for pattern, count in patterns.items() 
            if count >= len(results) * 0.5  # Appears in at least 50% of targets
        ]
        
        return comparison

    def _compare_parameters(self, results: List[DiscoveryResult]) -> Dict[str, Any]:
        """Compare parameters across targets."""
        comparison = {
            'parameter_frequency': {},
            'common_parameters': [],
            'target_specific_parameters': {}
        }
        
        all_parameters = {}
        target_parameters = {}
        
        for result in results:
            domain = result.target_domain
            target_params = set()
            
            for test in result.parameter_tests:
                if test.success:
                    param = test.parameter_name
                    target_params.add(param)
                    all_parameters[param] = all_parameters.get(param, 0) + 1
            
            target_parameters[domain] = list(target_params)
        
        comparison['parameter_frequency'] = all_parameters
        comparison['target_specific_parameters'] = target_parameters
        
        # Common parameters appear in multiple targets
        comparison['common_parameters'] = [
            param for param, count in all_parameters.items()
            if count >= len(results) * 0.5
        ]
        
        return comparison

    def _compare_schemas(self, results: List[DiscoveryResult]) -> Dict[str, Any]:
        """Compare response schemas across targets."""
        comparison = {
            'schema_complexity': {},
            'common_fields': [],
            'field_patterns': {}
        }
        
        all_fields = {}
        
        for result in results:
            domain = result.target_domain
            total_fields = 0
            
            for endpoint in result.discovered_endpoints:
                if hasattr(endpoint, 'response_schema') and endpoint.response_schema:
                    schema_fields = endpoint.response_schema.get('fields', {})
                    total_fields += len(schema_fields)
                    
                    for field_name in schema_fields.keys():
                        all_fields[field_name] = all_fields.get(field_name, 0) + 1
            
            comparison['schema_complexity'][domain] = total_fields
        
        # Common fields appear across multiple targets
        comparison['common_fields'] = [
            field for field, count in all_fields.items()
            if count >= len(results) * 0.3  # Appears in at least 30% of targets
        ]
        
        comparison['field_patterns'] = all_fields
        
        return comparison

    def _compare_business_logic(self, results: List[DiscoveryResult]) -> Dict[str, Any]:
        """Compare business logic across targets."""
        comparison = {
            'business_domains': {},
            'common_entities': [],
            'industry_patterns': {}
        }
        
        all_entities = {}
        
        for result in results:
            domain = result.target_domain
            business_data = result.business_intelligence
            
            # Extract business entities
            entities = []
            if 'domain_insights' in business_data:
                for endpoint_insights in business_data['domain_insights'].values():
                    if 'business_entities' in endpoint_insights:
                        entities.extend(endpoint_insights['business_entities'])
            
            comparison['business_domains'][domain] = entities
            
            for entity in entities:
                all_entities[entity] = all_entities.get(entity, 0) + 1
        
        # Common entities appear across multiple targets
        comparison['common_entities'] = [
            entity for entity, count in all_entities.items()
            if count >= len(results) * 0.3
        ]
        
        return comparison

    async def _generate_documentation(self, result: DiscoveryResult, results_dir: Path):
        """Generate comprehensive documentation for discovery results."""
        doc_generator = DocumentationGenerator(results_dir)
        await doc_generator.generate_comprehensive_docs(result)

    async def _generate_client_code(self, result: DiscoveryResult, results_dir: Path):
        """Generate client code for discovered APIs."""
        # This would generate client libraries in various languages
        # For now, we'll create a simple Python client template
        
        client_dir = results_dir / "clients"
        client_dir.mkdir(exist_ok=True)
        
        # Generate Python client
        python_client = self._generate_python_client(result)
        with open(client_dir / "python_client.py", 'w') as f:
            f.write(python_client)
        
        print(f"   🐍 Generated Python client")

    def _generate_python_client(self, result: DiscoveryResult) -> str:
        """Generate a Python client for the discovered API."""
        client_code = f'''#!/usr/bin/env python3
"""
Auto-generated Python client for {result.target_domain} API
Generated by Cipher-Spy Universal API Discovery
Generated at: {datetime.now().isoformat()}
"""

import requests
from typing import Dict, List, Any, Optional


class {result.target_domain.replace('.', '_').replace('-', '_').title()}APIClient:
    """Auto-generated API client for {result.target_domain}"""
    
    def __init__(self, base_url: str = None, api_key: str = None):
        self.base_url = base_url or "https://{result.target_domain}"
        self.api_key = api_key
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({{"Authorization": f"Bearer {{api_key}}"}})
    
'''
        
        # Add methods for each discovered endpoint
        for endpoint in result.discovered_endpoints:
            method_name = endpoint.name.replace('-', '_').replace(' ', '_').lower()
            client_code += f'''
    def {method_name}(self, **params) -> Dict[str, Any]:
        """
        {endpoint.description or f"Call {endpoint.name} endpoint"}
        
        URL: {endpoint.endpoint_url}
        Method: {endpoint.method}
        """
        response = self.session.{endpoint.method.lower()}(
            "{endpoint.endpoint_url}",
            params=params
        )
        response.raise_for_status()
        return response.json()
'''
        
        return client_code

    async def _save_discovery_metadata(self, result: DiscoveryResult, results_dir: Path, session_id: str):
        """Save discovery metadata and summary."""
        metadata = {
            'session_id': session_id,
            'target_domain': result.target_domain,
            'discovery_timestamp': result.timestamp,
            'total_duration': result.total_duration,
            'endpoints_discovered': len(result.discovered_endpoints),
            'parameters_tested': len(result.parameter_tests),
            'successful_tests': len([t for t in result.parameter_tests if t.success]),
            'analyzer_metadata': result.discovery_metadata,
            'results_directory': str(results_dir)
        }
        
        metadata_file = results_dir / "discovery_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)

    def get_discovery_history(self) -> List[Dict[str, Any]]:
        """Get summary of all discovery sessions."""
        return [
            {
                'target_domain': result.target_domain,
                'timestamp': result.timestamp,
                'duration': result.total_duration,
                'endpoints_found': len(result.discovered_endpoints),
                'success_rate': len([t for t in result.parameter_tests if t.success]) / len(result.parameter_tests) * 100 if result.parameter_tests else 0
            }
            for result in self.discovery_history
        ]

    def get_active_sessions(self) -> Dict[str, str]:
        """Get information about active discovery sessions."""
        return {
            session_id: analyzer.target_config.domain
            for session_id, analyzer in self.active_sessions.items()
        }
