#!/usr/bin/env python3
"""
Test environment variable loading
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🔍 Testing environment variable loading...")
print(f"Current working directory: {os.getcwd()}")

# Check if .env file exists
env_file = ".env"
if os.path.exists(env_file):
    print(f"✅ .env file found: {env_file}")
    
    # Read .env file content
    with open(env_file, 'r') as f:
        content = f.read()
    print(f"📄 .env file content:\n{content}")
else:
    print(f"❌ .env file not found: {env_file}")

# Check environment variables
openrouter_key = os.getenv('OPENROUTER_API_KEY')
if openrouter_key:
    print(f"✅ OPENROUTER_API_KEY loaded: {openrouter_key[:20]}...")
else:
    print("❌ OPENROUTER_API_KEY not found")

default_model = os.getenv('DEFAULT_LLM_MODEL')
if default_model:
    print(f"✅ DEFAULT_LLM_MODEL loaded: {default_model}")
else:
    print("❌ DEFAULT_LLM_MODEL not found")

# Test a simple OpenRouter API call
if openrouter_key:
    print("\n🧪 Testing OpenRouter API connection...")
    import requests
    
    try:
        response = requests.post(
            'https://openrouter.ai/api/v1/chat/completions',
            headers={
                'Authorization': f'Bearer {openrouter_key}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://cipher-spy.com',
                'X-Title': 'Cipher-Spy Test'
            },
            json={
                'model': 'anthropic/claude-3.5-sonnet',
                'messages': [
                    {'role': 'user', 'content': 'Say "Hello from Cipher-Spy!" in exactly those words.'}
                ],
                'max_tokens': 50
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result['choices'][0]['message']['content']
            print(f"✅ OpenRouter API working! Response: {message}")
        else:
            print(f"❌ OpenRouter API error: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ OpenRouter API test failed: {e}")
else:
    print("⚠️  Skipping OpenRouter API test - no API key")

print("\n🎯 Environment test complete!")
