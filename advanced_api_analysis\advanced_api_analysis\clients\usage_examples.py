#!/usr/bin/env python3
"""
Usage Examples for Enhanced Pump.fun API Client

Demonstrates various trading strategies and analysis techniques.
"""

import time
from enhanced_pump_client import EnhancedPumpClient

def example_early_discovery():
    """Example: Early coin discovery strategy."""
    print("🔍 Early Discovery Strategy")
    print("-" * 30)

    client = EnhancedPumpClient()

    # Get newest coins
    latest_coins = client.get_advanced_coins(sort_by='creationTime', limit=100)

    print(f"Found {len(latest_coins.get('coins', []))} latest coins")

    # Analyze recent coins
    recent_opportunities = []
    for coin in latest_coins.get('coins', [])[:20]:
        score = client._calculate_opportunity_score(coin)
        if score > 60:  # High potential threshold
            recent_opportunities.append({
                'name': coin.get('name', 'Unknown'),
                'score': score,
                'volume': coin.get('volume', 0),
                'market_cap': coin.get('marketCap', 0),
                'holders': coin.get('numHolders', 0)
            })

    print(f"\nHigh potential recent coins ({len(recent_opportunities)}):")
    for opp in recent_opportunities[:5]:
        print(f"  {opp['name']}: Score {opp['score']:.1f}")
        print(f"    Volume: ${opp['volume']:,.0f}")
        print(f"    Market Cap: ${opp['market_cap']:,.0f}")
        print(f"    Holders: {opp['holders']}")
        print()

def example_graduation_analysis():
    """Example: Graduation analysis strategy."""
    print("🎓 Graduation Analysis Strategy")
    print("-" * 35)

    client = EnhancedPumpClient()

    # Get graduated coins to understand patterns
    graduated_coins = client.get_graduated_coins(limit=100)
    print(f"Analyzing {len(graduated_coins.get('coins', []))} graduated coins")

    # Calculate graduation thresholds
    market_caps = []
    volumes = []
    holder_counts = []

    for coin in graduated_coins.get('coins', []):
        if coin.get('marketCap'):
            market_caps.append(coin['marketCap'])
        if coin.get('volume'):
            volumes.append(coin['volume'])
        if coin.get('numHolders'):
            holder_counts.append(coin['numHolders'])

    if market_caps:
        avg_graduation_mcap = sum(market_caps) / len(market_caps)
        min_graduation_mcap = min(market_caps)
        print(f"\nGraduation Market Cap Analysis:")
        print(f"  Average: ${avg_graduation_mcap:,.0f}")
        print(f"  Minimum: ${min_graduation_mcap:,.0f}")

    if volumes:
        avg_graduation_volume = sum(volumes) / len(volumes)
        print(f"\nGraduation Volume Analysis:")
        print(f"  Average: ${avg_graduation_volume:,.0f}")

    if holder_counts:
        avg_graduation_holders = sum(holder_counts) / len(holder_counts)
        print(f"\nGraduation Holders Analysis:")
        print(f"  Average: {avg_graduation_holders:.0f}")

def example_volume_momentum():
    """Example: Volume momentum strategy."""
    print("📈 Volume Momentum Strategy")
    print("-" * 30)

    client = EnhancedPumpClient()

    # Get high volume coins
    volume_coins = client.get_advanced_coins(sort_by='volume', limit=100)

    momentum_candidates = []
    for coin in volume_coins.get('coins', []):
        volume = coin.get('volume', 0)
        market_cap = coin.get('marketCap', 0)

        if volume > 50000 and market_cap > 0:
            volume_ratio = volume / market_cap
            if volume_ratio > 0.2:  # High volume relative to market cap
                momentum_candidates.append({
                    'name': coin.get('name', 'Unknown'),
                    'volume': volume,
                    'market_cap': market_cap,
                    'volume_ratio': volume_ratio,
                    'holders': coin.get('numHolders', 0)
                })

    # Sort by volume ratio
    momentum_candidates.sort(key=lambda x: x['volume_ratio'], reverse=True)

    print(f"\nTop momentum candidates ({len(momentum_candidates)}):")
    for candidate in momentum_candidates[:5]:
        print(f"  {candidate['name']}")
        print(f"    Volume: ${candidate['volume']:,.0f}")
        print(f"    Market Cap: ${candidate['market_cap']:,.0f}")
        print(f"    Volume Ratio: {candidate['volume_ratio']:.2f}")
        print(f"    Holders: {candidate['holders']}")
        print()

def example_comprehensive_analysis():
    """Example: Comprehensive market analysis."""
    print("🔍 Comprehensive Market Analysis")
    print("-" * 40)

    client = EnhancedPumpClient()

    # Get data from multiple endpoints
    latest_coins = client.get_advanced_coins(sort_by='creationTime', limit=200)
    volume_coins = client.get_advanced_coins(sort_by='volume', limit=200)
    graduated_coins = client.get_graduated_coins(limit=100)

    print(f"Data collected:")
    print(f"  Latest coins: {len(latest_coins.get('coins', []))}")
    print(f"  Volume coins: {len(volume_coins.get('coins', []))}")
    print(f"  Graduated coins: {len(graduated_coins.get('coins', []))}")

    # Find high potential opportunities
    opportunities = client.find_high_potential_coins()

    print(f"\nHigh potential opportunities: {len(opportunities)}")
    for opp in opportunities[:3]:
        print(f"  {opp.get('name', 'Unknown')}: Score {opp['opportunity_score']:.1f}")

    # Show client performance
    stats = client.get_stats()
    print(f"\nClient Performance:")
    print(f"  Total requests: {stats['total_requests']}")
    print(f"  Cache hit rate: {stats['cache_hit_rate']:.1%}")
    print(f"  Error rate: {stats['error_rate']:.1%}")

def main():
    """Run all examples."""
    print("🚀 Pump.fun API Client Examples")
    print("=" * 50)

    example_early_discovery()
    print("\n" + "="*50 + "\n")

    example_graduation_analysis()
    print("\n" + "="*50 + "\n")

    example_volume_momentum()
    print("\n" + "="*50 + "\n")

    example_comprehensive_analysis()

    print("\n✅ All examples completed!")

if __name__ == "__main__":
    main()
