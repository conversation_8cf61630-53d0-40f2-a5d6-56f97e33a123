/**
 * Cipher-Spy Network Monitor - Popup Script
 *
 * Handles the popup interface for monitoring control and data visualization.
 */

// Global state
let isMonitoring = false;
let sessionId = null;
let sessionStartTime = null;
let requests = [];
let settings = {};

// DOM elements
const elements = {
  toggleMonitoring: document.getElementById('toggleMonitoring'),
  clearRequests: document.getElementById('clearRequests'),
  statusIndicator: document.getElementById('statusIndicator'),
  statusText: document.getElementById('statusText'),
  requestCount: document.getElementById('requestCount'),
  apiCount: document.getElementById('apiCount'),
  errorCount: document.getElementById('errorCount'),
  sessionId: document.getElementById('sessionId'),
  sessionDuration: document.getElementById('sessionDuration'),
  requestList: document.getElementById('requestList'),
  refreshRequests: document.getElementById('refreshRequests'),
  exportData: document.getElementById('exportData'),
  analyzeSession: document.getElementById('analyzeSession'),
  openDevtools: document.getElementById('openDevtools'),
  settingsToggle: document.getElementById('settingsToggle'),
  settingsPanel: document.getElementById('settingsPanel'),
  saveSettings: document.getElementById('saveSettings'),
  resetSettings: document.getElementById('resetSettings'),
  captureHeaders: document.getElementById('captureHeaders'),
  capturePayloads: document.getElementById('capturePayloads'),
  autoAnalysis: document.getElementById('autoAnalysis'),
  backendUrl: document.getElementById('backendUrl'),
  filterPatterns: document.getElementById('filterPatterns'),
  excludePatterns: document.getElementById('excludePatterns')
};

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  console.log('Cipher-Spy popup loaded');

  // Load initial data
  await loadSettings();
  await loadMonitoringStatus();
  await loadRequests();

  // Set up event listeners
  setupEventListeners();

  // Start periodic updates
  startPeriodicUpdates();
});

// Event listeners
function setupEventListeners() {
  // Main controls
  elements.toggleMonitoring.addEventListener('click', toggleMonitoring);
  elements.clearRequests.addEventListener('click', clearRequests);
  elements.refreshRequests.addEventListener('click', loadRequests);

  // Actions
  elements.exportData.addEventListener('click', exportData);
  elements.analyzeSession.addEventListener('click', analyzeSession);
  elements.openDevtools.addEventListener('click', openDevtools);

  // Settings
  elements.settingsToggle.addEventListener('click', toggleSettings);
  elements.saveSettings.addEventListener('click', saveSettings);
  elements.resetSettings.addEventListener('click', resetSettings);
}

// Monitoring control
async function toggleMonitoring() {
  try {
    if (isMonitoring) {
      const response = await sendMessageWithRetry({ action: 'stopMonitoring' });
      if (response && !response.error) {
        isMonitoring = false;
        sessionStartTime = null;
        showSuccess('Monitoring stopped');
      } else {
        throw new Error(response?.error || 'Failed to stop monitoring');
      }
    } else {
      const response = await sendMessageWithRetry({ action: 'startMonitoring' });
      if (response && !response.error) {
        isMonitoring = true;
        sessionStartTime = Date.now();
        showSuccess('Monitoring started');
      } else {
        throw new Error(response?.error || 'Failed to start monitoring');
      }
    }

    updateMonitoringUI();
  } catch (error) {
    console.error('Error toggling monitoring:', error);
    showError('Failed to toggle monitoring: ' + error.message);
  }
}

async function clearRequests() {
  try {
    const response = await sendMessageWithRetry({ action: 'clearRequests' });
    if (response && !response.error) {
      requests = [];
      updateRequestsUI();
      updateStats();
      showSuccess('Requests cleared');
    } else {
      throw new Error(response?.error || 'Failed to clear requests');
    }
  } catch (error) {
    console.error('Error clearing requests:', error);
    showError('Failed to clear requests: ' + error.message);
  }
}

// Data loading
async function loadMonitoringStatus() {
  try {
    // Try to get monitoring status from background script
    const response = await sendMessageWithRetry({ action: 'getMonitoringStatus' });
    if (response && !response.error) {
      isMonitoring = response.isMonitoring || false;
      sessionId = response.sessionId;
    } else {
      // Fallback to storage
      const sessionData = await chrome.storage.local.get(['sessionId']);
      sessionId = sessionData.sessionId;
    }

    // Update UI
    updateMonitoringUI();
  } catch (error) {
    console.error('Error loading monitoring status:', error);
    showError('Failed to load monitoring status');
  }
}

async function loadRequests() {
  try {
    const response = await sendMessageWithRetry({ action: 'getRequests' });
    if (response && !response.error) {
      requests = response.requests || [];
    } else {
      requests = [];
    }
    updateRequestsUI();
    updateStats();
  } catch (error) {
    console.error('Error loading requests:', error);
    requests = [];
    updateRequestsUI();
    updateStats();
  }
}

async function loadSettings() {
  try {
    const response = await sendMessageWithRetry({ action: 'getSettings' });
    if (response && !response.error) {
      settings = response.settings || {};
    } else {
      // Use default settings if background script fails
      settings = getDefaultSettings();
    }
    updateSettingsUI();
  } catch (error) {
    console.error('Error loading settings:', error);
    settings = getDefaultSettings();
    updateSettingsUI();
  }
}

// UI updates
function updateMonitoringUI() {
  if (isMonitoring) {
    elements.toggleMonitoring.textContent = 'Stop Monitoring';
    elements.toggleMonitoring.className = 'btn btn-secondary';
    elements.statusIndicator.className = 'status-indicator active';
    elements.statusText.textContent = 'Active';
  } else {
    elements.toggleMonitoring.textContent = 'Start Monitoring';
    elements.toggleMonitoring.className = 'btn btn-primary';
    elements.statusIndicator.className = 'status-indicator';
    elements.statusText.textContent = 'Stopped';
  }

  // Update session info
  elements.sessionId.textContent = sessionId ? sessionId.substring(0, 12) + '...' : '-';

  if (sessionStartTime) {
    const duration = Math.floor((Date.now() - sessionStartTime) / 1000);
    elements.sessionDuration.textContent = formatDuration(duration);
  } else {
    elements.sessionDuration.textContent = '-';
  }
}

function updateRequestsUI() {
  if (requests.length === 0) {
    elements.requestList.innerHTML = '<div class="empty-state">No requests captured yet</div>';
    return;
  }

  // Show last 10 requests
  const recentRequests = requests.slice(-10).reverse();

  elements.requestList.innerHTML = recentRequests.map(request => {
    const url = new URL(request.url);
    const path = url.pathname + url.search;
    const time = new Date(request.timestamp).toLocaleTimeString();

    return `
      <div class="request-item">
        <span class="request-method method-${request.method}">${request.method}</span>
        <div class="request-url" title="${request.url}">${path}</div>
        <div class="request-time">${time}</div>
      </div>
    `;
  }).join('');
}

function updateStats() {
  elements.requestCount.textContent = requests.length;

  // Count API requests (those matching filter patterns)
  const apiRequests = requests.filter(req => {
    const url = req.url.toLowerCase();
    return settings.filterPatterns?.some(pattern => url.includes(pattern.toLowerCase())) || false;
  });
  elements.apiCount.textContent = apiRequests.length;

  // Count errors
  const errorRequests = requests.filter(req => req.error || (req.response && req.response.statusCode >= 400));
  elements.errorCount.textContent = errorRequests.length;
}

function updateSettingsUI() {
  if (!settings) return;

  elements.captureHeaders.checked = settings.captureHeaders || false;
  elements.capturePayloads.checked = settings.capturePayloads || false;
  elements.autoAnalysis.checked = settings.autoAnalysis || false;
  elements.backendUrl.value = settings.backendUrl || '';
  elements.filterPatterns.value = settings.filterPatterns?.join(', ') || '';
  elements.excludePatterns.value = settings.excludePatterns?.join(', ') || '';
}

// Actions
async function exportData() {
  try {
    const response = await sendMessageWithRetry({ action: 'exportRequests' });
    if (response && response.data && !response.error) {
      const data = response.data;

      // Create download
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `cipher-spy-export-${new Date().toISOString().slice(0, 19)}.json`;
      a.click();

      URL.revokeObjectURL(url);

      showSuccess('Data exported successfully');
    } else {
      throw new Error(response?.error || 'No data to export');
    }
  } catch (error) {
    console.error('Error exporting data:', error);
    showError('Failed to export data: ' + error.message);
  }
}

async function analyzeSession() {
  try {
    const response = await sendMessageWithRetry({ action: 'analyzeRequests' });
    if (response && !response.error) {
      showSuccess('Analysis started - check DevTools for results');
    } else {
      throw new Error(response?.error || 'Analysis failed');
    }
  } catch (error) {
    console.error('Error analyzing session:', error);
    showError('Failed to start analysis: ' + error.message);
  }
}

function openDevtools() {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (tabs[0]) {
      chrome.tabs.sendMessage(tabs[0].id, { action: 'openDevtools' });
    }
  });
}

// Settings
function toggleSettings() {
  const panel = elements.settingsPanel;
  if (panel.style.display === 'none') {
    panel.style.display = 'block';
    elements.settingsToggle.textContent = 'Hide Settings';
  } else {
    panel.style.display = 'none';
    elements.settingsToggle.textContent = 'Settings';
  }
}

async function saveSettings() {
  try {
    const newSettings = {
      ...settings,
      captureHeaders: elements.captureHeaders.checked,
      capturePayloads: elements.capturePayloads.checked,
      autoAnalysis: elements.autoAnalysis.checked,
      backendUrl: elements.backendUrl.value.trim(),
      filterPatterns: elements.filterPatterns.value.split(',').map(p => p.trim()).filter(p => p),
      excludePatterns: elements.excludePatterns.value.split(',').map(p => p.trim()).filter(p => p)
    };

    const response = await sendMessageWithRetry({ action: 'updateSettings', settings: newSettings });
    if (response && !response.error) {
      settings = newSettings;
      showSuccess('Settings saved successfully');
    } else {
      throw new Error(response?.error || 'Failed to save settings');
    }
  } catch (error) {
    console.error('Error saving settings:', error);
    showError('Failed to save settings: ' + error.message);
  }
}

async function resetSettings() {
  try {
    const defaultSettings = getDefaultSettings();

    const response = await sendMessageWithRetry({ action: 'updateSettings', settings: defaultSettings });
    if (response && !response.error) {
      settings = defaultSettings;
      updateSettingsUI();
      showSuccess('Settings reset to defaults');
    } else {
      throw new Error(response?.error || 'Failed to reset settings');
    }
  } catch (error) {
    console.error('Error resetting settings:', error);
    showError('Failed to reset settings: ' + error.message);
  }
}

// Periodic updates
function startPeriodicUpdates() {
  // Update every 2 seconds
  setInterval(async () => {
    if (isMonitoring) {
      await loadRequests();
      updateMonitoringUI();
    }
  }, 2000);
}

// Utility functions
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

// Message sending with retry logic
async function sendMessageWithRetry(message, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await chrome.runtime.sendMessage(message);
      return response;
    } catch (error) {
      console.warn(`Message attempt ${i + 1} failed:`, error);

      if (i === maxRetries - 1) {
        throw error;
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 100 * (i + 1)));
    }
  }
}

// Default settings fallback
function getDefaultSettings() {
  return {
    monitoringEnabled: true,
    captureHeaders: true,
    capturePayloads: true,
    filterPatterns: ['/api/', '.json', '/graphql', '/rest/'],
    excludePatterns: ['.css', '.js', '.png', '.jpg', '.gif', '.ico'],
    maxRequestsStored: 1000,
    backendUrl: 'http://localhost:8000',
    autoAnalysis: true
  };
}

function showSuccess(message) {
  showNotification(message, 'success');
}

function showError(message) {
  showNotification(message, 'error');
}

function showNotification(message, type) {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    ${type === 'success' ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'}
  `;

  document.body.appendChild(notification);

  // Remove after 3 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}
