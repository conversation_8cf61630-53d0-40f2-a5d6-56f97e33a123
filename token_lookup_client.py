#!/usr/bin/env python3
"""
Enhanced Pump.fun Token Lookup Client

Provides direct token lookup capabilities using mint addresses.
"""

import requests
import time
from typing import Dict, Any, Optional

class PumpTokenLookupClient:
    """Client for looking up specific tokens by mint address."""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://pump.fun',
            'Referer': 'https://pump.fun'
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Base URLs for token lookup
        self.base_url = 'https://frontend-api-v3.pump.fun'
    
    def get_token_info(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed token information by mint address.
        
        Args:
            mint_address: The token's mint address
            
        Returns:
            Dict containing token information or None if not found
        """
        try:
            # Use the direct endpoint pattern
            url = f"{self.base_url}/coins/{mint_address}"
            
            response = self.session.get(url, timeout=15)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                print(f"Token {mint_address} not found")
                return None
            else:
                print(f"API request failed with status {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Error fetching token info: {e}")
            return None
    
    def get_token_info_alt(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """
        Alternative method using query parameter.
        
        Args:
            mint_address: The token's mint address
            
        Returns:
            Dict containing token information or None if not found
        """
        try:
            url = f"{self.base_url}/coins"
            params = {'mint': mint_address}
            
            response = self.session.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                # This endpoint might return different format
                data = response.json()
                return data if data else None
            else:
                return None
                
        except Exception as e:
            print(f"Error fetching token info (alt method): {e}")
            return None
    
    def analyze_token(self, mint_address: str) -> Dict[str, Any]:
        """
        Get comprehensive token analysis.
        
        Args:
            mint_address: The token's mint address
            
        Returns:
            Dict containing analysis results
        """
        token_info = self.get_token_info(mint_address)
        
        if not token_info:
            return {
                'found': False,
                'mint_address': mint_address,
                'error': 'Token not found'
            }
        
        # Extract key metrics
        analysis = {
            'found': True,
            'mint_address': mint_address,
            'basic_info': {
                'name': token_info.get('name'),
                'symbol': token_info.get('symbol'),
                'description': token_info.get('description'),
                'creator': token_info.get('creator'),
                'created_timestamp': token_info.get('created_timestamp')
            },
            'market_data': {
                'market_cap': token_info.get('market_cap'),
                'usd_market_cap': token_info.get('usd_market_cap'),
                'ath_market_cap': token_info.get('ath_market_cap'),
                'ath_timestamp': token_info.get('ath_market_cap_timestamp'),
                'total_supply': token_info.get('total_supply'),
                'last_trade_timestamp': token_info.get('last_trade_timestamp')
            },
            'liquidity_data': {
                'virtual_sol_reserves': token_info.get('virtual_sol_reserves'),
                'virtual_token_reserves': token_info.get('virtual_token_reserves'),
                'real_sol_reserves': token_info.get('real_sol_reserves'),
                'real_token_reserves': token_info.get('real_token_reserves'),
                'bonding_curve': token_info.get('bonding_curve'),
                'associated_bonding_curve': token_info.get('associated_bonding_curve')
            },
            'status': {
                'complete': token_info.get('complete'),
                'raydium_pool': token_info.get('raydium_pool'),
                'hidden': token_info.get('hidden'),
                'nsfw': token_info.get('nsfw'),
                'is_banned': token_info.get('is_banned'),
                'initialized': token_info.get('initialized')
            },
            'social_data': {
                'twitter': token_info.get('twitter'),
                'telegram': token_info.get('telegram'),
                'website': token_info.get('website'),
                'reply_count': token_info.get('reply_count'),
                'last_reply': token_info.get('last_reply')
            },
            'media': {
                'image_uri': token_info.get('image_uri'),
                'metadata_uri': token_info.get('metadata_uri'),
                'video_uri': token_info.get('video_uri'),
                'banner_uri': token_info.get('banner_uri')
            },
            'raw_data': token_info
        }
        
        # Calculate additional metrics
        if token_info.get('market_cap') and token_info.get('ath_market_cap'):
            analysis['performance'] = {
                'ath_ratio': token_info['market_cap'] / token_info['ath_market_cap'],
                'down_from_ath_percent': (1 - (token_info['market_cap'] / token_info['ath_market_cap'])) * 100
            }
        
        return analysis
    
    def format_token_summary(self, analysis: Dict[str, Any]) -> str:
        """Format token analysis into readable summary."""
        if not analysis.get('found'):
            return f"❌ Token {analysis.get('mint_address')} not found"
        
        basic = analysis['basic_info']
        market = analysis['market_data']
        status = analysis['status']
        social = analysis['social_data']
        
        summary = f"""
🪙 TOKEN SUMMARY: {basic['name']} ({basic['symbol']})
{'='*60}

📊 MARKET DATA:
   Market Cap: ${market['market_cap']:,.2f} (${market['usd_market_cap']:,.2f} USD)
   ATH Market Cap: ${market['ath_market_cap']:,.2f}
   Total Supply: {market['total_supply']:,}
   
📈 PERFORMANCE:
"""
        
        if 'performance' in analysis:
            perf = analysis['performance']
            summary += f"   Current vs ATH: {perf['ath_ratio']:.1%} ({perf['down_from_ath_percent']:.1f}% down from ATH)\n"
        
        summary += f"""
🔗 STATUS:
   Complete: {'✅' if status['complete'] else '❌'}
   Raydium Pool: {'✅' if status['raydium_pool'] else '❌'}
   Banned: {'❌' if status['is_banned'] else '✅'}
   NSFW: {'⚠️' if status['nsfw'] else '✅'}

🌐 SOCIAL:
   Twitter: {'✅' if social['twitter'] else '❌'}
   Telegram: {'✅' if social['telegram'] else '❌'}
   Website: {'✅' if social['website'] else '❌'}
   Replies: {social['reply_count'] or 0}

💡 DESCRIPTION:
   {basic['description'] or 'No description available'}

🔍 MINT ADDRESS: {analysis['mint_address']}
"""
        
        return summary

def main():
    """Test the token lookup functionality."""
    client = PumpTokenLookupClient()
    
    # Test with the example mint address
    test_mint = "GtGHchFcr48SRZQHMUN4jTr7PpNZoQSXbpBrexuipump"
    
    print("🔍 Testing Token Lookup Client")
    print("=" * 50)
    print(f"Looking up: {test_mint}")
    print()
    
    # Get comprehensive analysis
    analysis = client.analyze_token(test_mint)
    
    # Display formatted summary
    summary = client.format_token_summary(analysis)
    print(summary)
    
    # Test with a few more tokens from recent listings
    print("\n" + "="*60)
    print("🔍 TESTING WITH ADDITIONAL TOKENS")
    print("="*60)
    
    # Get some recent tokens to test with
    try:
        response = requests.get(
            'https://frontend-api-v3.pump.fun/coins',
            params={'offset': 0, 'limit': 3, 'sort': 'market_cap', 'order': 'DESC'},
            headers=client.headers,
            timeout=10
        )
        
        if response.status_code == 200:
            recent_tokens = response.json()
            
            for i, token in enumerate(recent_tokens[:3], 1):
                mint = token.get('mint')
                if mint:
                    print(f"\n🔍 Test {i}: {mint}")
                    analysis = client.analyze_token(mint)
                    if analysis.get('found'):
                        basic = analysis['basic_info']
                        market = analysis['market_data']
                        print(f"   ✅ {basic['name']} ({basic['symbol']})")
                        print(f"   💰 Market Cap: ${market['market_cap']:,.2f}")
                    else:
                        print(f"   ❌ Not found via lookup endpoint")
                    
                    time.sleep(1)  # Rate limiting
    except Exception as e:
        print(f"Error testing additional tokens: {e}")

if __name__ == "__main__":
    main()
