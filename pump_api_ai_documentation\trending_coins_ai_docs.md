# Trending Coins API

## Overview
The Trending Coins endpoint provides real-time access to cryptocurrency market data, sorted by market capitalization. This endpoint is ideal for applications requiring current market trends, trading dashboards, and market analysis tools.

## Endpoint Information
- **URL**: `https://frontend-api-v3.pump.fun/coins`
- **Method**: GET
- **Content-Type**: application/json; charset=utf-8
- **Average Response Time**: ~414ms

## Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| offset | integer | No | 0 | Number of records to skip |
| limit | integer | No | 50 | Maximum number of records to return (1-100) |
| sort | string | No | "market_cap" | Field to sort by |
| order | string | No | "DESC" | Sort order ("ASC" or "DESC") |
| includeNsfw | boolean | No | false | Include NSFW content in results |

## Response Format
Returns an array of coin objects with detailed market and metadata information.

### Sample Response
```json
[
  {
    "mint": "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",
    "name": "Fartcoin",
    "symbol": "Fartcoin",
    "market_cap": 8300000,
    "usd_market_cap": 1320696000,
    // ... additional fields
  }
]
```

## Code Examples

### Python Example
```python
import requests

def get_trending_coins(limit=50, offset=0):
    url = "https://frontend-api-v3.pump.fun/coins"
    params = {
        "limit": limit,
        "offset": offset,
        "sort": "market_cap",
        "order": "DESC",
        "includeNsfw": False
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching trending coins: {e}")
        return None
```

### cURL Example
```bash
curl -X GET "https://frontend-api-v3.pump.fun/coins?limit=50&offset=0&sort=market_cap&order=DESC&includeNsfw=false" \
  -H "Accept: application/json"
```

### JavaScript Example
```javascript
async function getTrendingCoins(limit = 50, offset = 0) {
  const params = new URLSearchParams({
    limit,
    offset,
    sort: 'market_cap',
    order: 'DESC',
    includeNsfw: false
  });

  try {
    const response = await fetch(
      `https://frontend-api-v3.pump.fun/coins?${params}`
    );
    if (!response.ok) throw new Error('Network response was not ok');
    return await response.json();
  } catch (error) {
    console.error('Error fetching trending coins:', error);
    return null;
  }
}
```

## Response Fields Reference

| Field | Type | Description |
|-------|------|-------------|
| mint | string | Unique identifier for the coin |
| name | string | Display name of the coin |
| symbol | string | Trading symbol |
| market_cap | number | Current market capitalization |
| usd_market_cap | number | Market cap in USD |
| virtual_sol_reserves | number | Current SOL reserves |
| virtual_token_reserves | number | Current token reserves |
| total_supply | number | Total token supply |
| last_trade_timestamp | number | Unix timestamp of last trade |

## Use Cases

1. **Market Dashboard**
   - Display trending coins with market caps
   - Track price movements and trading volume

2. **Portfolio Tracking**
   - Monitor holdings against market trends
   - Calculate portfolio performance

3. **Market Analysis**
   - Identify emerging trends
   - Compare market caps across tokens

## Error Handling

| Status Code | Description | Resolution |
|-------------|-------------|------------|
| 200 | Successful request | Process the returned data |
| 400 | Invalid parameters | Check parameter values |
| 429 | Rate limit exceeded | Implement backoff strategy |
| 500 | Server error | Retry with exponential backoff |

## Rate Limiting & Best Practices

- Implement caching for frequently accessed data
- Limit requests to no more than 1 per second
- Use the offset parameter for pagination
- Cache responses for 60 seconds minimum

## Integration Tips

1. Implement error handling and retries
2. Cache responses to minimize API calls
3. Use pagination for large data sets
4. Monitor response times and implement timeouts
5. Handle null values in response fields

## Performance Considerations

- Average response time: 414ms
- Response size: ~62KB
- Implement request timeouts > 1 second
- Consider gzip compression for responses

This endpoint is suitable for production use with proper error handling and rate limiting implementation.