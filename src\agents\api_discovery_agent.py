"""
API Discovery Agent for Cipher-Spy.

This agent orchestrates comprehensive API discovery through autonomous
web navigation, network interception, and intelligent analysis.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from urllib.parse import urlparse

from ..crawling.playwright_crawler import Playwright<PERSON>rawler
from ..crawling.network_interceptor import NetworkInterceptor
from ..crawling.scope_manager import Scope<PERSON>anager
from .autonomous_navigator import AutonomousNavigator
from .enhanced_pump_navigator import EnhancedPumpNavigator
from ..utils.logging import get_logger
from ..core.exceptions import CrawlingException


class APIDiscoveryAgent:
    """
    Comprehensive API discovery agent that combines autonomous navigation
    with network interception to reverse-engineer web application APIs.
    """

    def __init__(self, target_url: str, config: Optional[Dict[str, Any]] = None):
        self.target_url = target_url
        self.config = config or {}
        self.logger = get_logger(__name__)

        # Parse target
        parsed_url = urlparse(target_url)
        self.domain = parsed_url.netloc

        # Initialize components
        self.scope_manager = ScopeManager(
            base_url=self.target_url,
            allowed_domains=[self.domain],
            respect_robots=self.config.get('respect_robots_txt', True)
        )

        self.network_interceptor = NetworkInterceptor()

        self.crawler = PlaywrightCrawler(
            headless=self.config.get('headless', True),
            delay_ms=self.config.get('crawl_delay_ms', 2000),
            scope_manager=self.scope_manager,
            network_interceptor=self.network_interceptor
        )

        # Discovery results
        self.discovered_apis: List[Dict[str, Any]] = []
        self.interaction_flows: List[Dict[str, Any]] = []
        self.authentication_flows: List[Dict[str, Any]] = []

    async def discover_apis(self) -> Dict[str, Any]:
        """
        Perform comprehensive API discovery on the target.

        Returns:
            Dict containing all discovered API information
        """
        try:
            self.logger.info(f"Starting API discovery for {self.target_url}")

            # Start crawler
            await self.crawler.start()

            # Setup network interception
            page = self.crawler.page
            await self.network_interceptor.setup_page(page)

            # Choose navigator based on target
            if "pump.fun" in self.target_url.lower():
                self.logger.info("🎯 Using enhanced pump.fun navigator")
                navigator = EnhancedPumpNavigator(page, self.network_interceptor)
            else:
                navigator = AutonomousNavigator(page, self.scope_manager)

            # Phase 1: Initial page load and analysis
            self.logger.info("Phase 1: Loading target page and analyzing initial state")
            initial_result = await self._analyze_initial_page()

            # Phase 2: Comprehensive autonomous exploration
            self.logger.info("Phase 2: Comprehensive autonomous exploration")
            if isinstance(navigator, EnhancedPumpNavigator):
                exploration_result = await navigator.comprehensive_exploration()
            else:
                exploration_result = await navigator.explore_page(
                    max_interactions=self.config.get('max_interactions', 20)
                )

            # Phase 3: Deep API analysis
            self.logger.info("Phase 3: Deep API analysis and schema inference")
            api_analysis = await self._analyze_discovered_apis()

            # Phase 4: Authentication flow detection
            self.logger.info("Phase 4: Authentication flow analysis")
            auth_analysis = await self._analyze_authentication_flows()

            # Phase 5: Generate comprehensive report
            self.logger.info("Phase 5: Generating comprehensive API report")
            final_report = await self._generate_api_report(
                initial_result,
                exploration_result,
                api_analysis,
                auth_analysis
            )

            return final_report

        except Exception as e:
            self.logger.error(f"Error during API discovery: {e}")
            raise CrawlingException(f"API discovery failed: {e}")

        finally:
            # Cleanup
            try:
                await self.crawler.stop()
            except:
                pass

    async def _analyze_initial_page(self) -> Dict[str, Any]:
        """Analyze the initial page load for API calls."""
        try:
            # Navigate to target
            page_result = await self.crawler.crawl_page(self.target_url)

            # Wait for initial API calls to complete
            await asyncio.sleep(5)

            # Get initial API endpoints
            initial_endpoints = self.network_interceptor.get_new_endpoints()

            self.logger.info(f"Initial page load triggered {len(initial_endpoints)} API calls")

            return {
                'page_result': page_result,
                'initial_endpoints': initial_endpoints,
                'page_load_time': datetime.utcnow()
            }

        except Exception as e:
            self.logger.error(f"Error analyzing initial page: {e}")
            return {'error': str(e)}

    async def _analyze_discovered_apis(self) -> Dict[str, Any]:
        """Perform deep analysis of discovered API endpoints."""
        try:
            all_endpoints = self.network_interceptor.get_discovered_endpoints()

            # Categorize endpoints
            categorized_endpoints = {
                'authentication': [],
                'data_retrieval': [],
                'data_modification': [],
                'real_time': [],
                'unknown': []
            }

            for endpoint in all_endpoints:
                category = self._categorize_endpoint(endpoint)
                categorized_endpoints[category].append(endpoint)

            # Analyze patterns
            patterns = self._analyze_api_patterns(all_endpoints)

            # Generate schemas
            schemas = await self._generate_api_schemas(all_endpoints)

            return {
                'total_endpoints': len(all_endpoints),
                'categorized_endpoints': categorized_endpoints,
                'patterns': patterns,
                'schemas': schemas,
                'analysis_time': datetime.utcnow()
            }

        except Exception as e:
            self.logger.error(f"Error analyzing APIs: {e}")
            return {'error': str(e)}

    async def _analyze_authentication_flows(self) -> Dict[str, Any]:
        """Analyze authentication and authorization patterns."""
        try:
            all_endpoints = self.network_interceptor.get_discovered_endpoints()

            # Look for authentication endpoints
            auth_endpoints = []
            for endpoint in all_endpoints:
                if self._is_auth_endpoint(endpoint):
                    auth_endpoints.append(endpoint)

            # Analyze token patterns
            token_patterns = self._analyze_token_patterns(all_endpoints)

            # Detect authentication flows
            auth_flows = self._detect_auth_flows(all_endpoints)

            return {
                'auth_endpoints': auth_endpoints,
                'token_patterns': token_patterns,
                'auth_flows': auth_flows,
                'requires_authentication': len(auth_endpoints) > 0
            }

        except Exception as e:
            self.logger.error(f"Error analyzing authentication: {e}")
            return {'error': str(e)}

    def _categorize_endpoint(self, endpoint: Dict[str, Any]) -> str:
        """Categorize an API endpoint based on its characteristics."""
        url = endpoint.get('url', '').lower()
        method = endpoint.get('method', '').upper()

        # Authentication endpoints
        if any(keyword in url for keyword in ['auth', 'login', 'token', 'oauth', 'signin']):
            return 'authentication'

        # Real-time endpoints
        if any(keyword in url for keyword in ['ws', 'websocket', 'stream', 'live', 'realtime']):
            return 'real_time'

        # Data modification endpoints
        if method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return 'data_modification'

        # Data retrieval endpoints
        if method == 'GET':
            return 'data_retrieval'

        return 'unknown'

    def _analyze_api_patterns(self, endpoints: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns in API endpoints."""
        patterns = {
            'base_urls': set(),
            'common_paths': {},
            'parameter_patterns': {},
            'response_patterns': {},
            'methods_used': set()
        }

        for endpoint in endpoints:
            # Extract base URL
            url = endpoint.get('url', '')
            if url:
                parsed = urlparse(url)
                base_url = f"{parsed.scheme}://{parsed.netloc}"
                patterns['base_urls'].add(base_url)

            # Track methods
            method = endpoint.get('method', '')
            if method:
                patterns['methods_used'].add(method)

            # Analyze paths
            path = parsed.path if url else ''
            if path:
                path_parts = path.split('/')
                for part in path_parts:
                    if part:
                        patterns['common_paths'][part] = patterns['common_paths'].get(part, 0) + 1

        # Convert sets to lists for JSON serialization
        patterns['base_urls'] = list(patterns['base_urls'])
        patterns['methods_used'] = list(patterns['methods_used'])

        return patterns

    async def _generate_api_schemas(self, endpoints: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate schemas for API endpoints."""
        schemas = {}

        for endpoint in endpoints:
            endpoint_key = f"{endpoint.get('method', 'GET')} {endpoint.get('url', '')}"

            schema = {
                'endpoint': endpoint_key,
                'method': endpoint.get('method', 'GET'),
                'url': endpoint.get('url', ''),
                'parameters': endpoint.get('parameters', {}),
                'headers': endpoint.get('headers', {}),
                'response_schema': endpoint.get('response_schema'),
                'status_codes': list(endpoint.get('status_codes', [])),
                'content_types': list(endpoint.get('content_types', []))
            }

            schemas[endpoint_key] = schema

        return schemas

    def _is_auth_endpoint(self, endpoint: Dict[str, Any]) -> bool:
        """Check if an endpoint is related to authentication."""
        url = endpoint.get('url', '').lower()
        headers = endpoint.get('headers', {})

        # Check URL patterns
        auth_patterns = ['auth', 'login', 'token', 'oauth', 'signin', 'signup', 'register']
        if any(pattern in url for pattern in auth_patterns):
            return True

        # Check for auth headers
        auth_headers = ['authorization', 'x-auth-token', 'x-api-key']
        if any(header.lower() in headers for header in auth_headers):
            return True

        return False

    def _analyze_token_patterns(self, endpoints: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze token usage patterns."""
        patterns = {
            'bearer_tokens': [],
            'api_keys': [],
            'session_tokens': [],
            'custom_tokens': []
        }

        for endpoint in endpoints:
            headers = endpoint.get('headers', {})

            for header_name, header_value in headers.items():
                header_lower = header_name.lower()

                if header_lower == 'authorization' and 'bearer' in header_value.lower():
                    patterns['bearer_tokens'].append(header_value)
                elif 'api-key' in header_lower or 'apikey' in header_lower:
                    patterns['api_keys'].append(header_value)
                elif 'session' in header_lower or 'token' in header_lower:
                    patterns['session_tokens'].append(header_value)

        return patterns

    def _detect_auth_flows(self, endpoints: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect authentication flows from endpoint patterns."""
        flows = []

        # Look for login -> token -> authenticated request patterns
        auth_endpoints = [ep for ep in endpoints if self._is_auth_endpoint(ep)]

        if auth_endpoints:
            flows.append({
                'type': 'token_based',
                'endpoints': auth_endpoints,
                'description': 'Token-based authentication flow detected'
            })

        return flows

    async def _generate_api_report(self, initial_result: Dict[str, Any],
                                 exploration_result: Dict[str, Any],
                                 api_analysis: Dict[str, Any],
                                 auth_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive API discovery report."""

        # Get final statistics
        final_stats = self.network_interceptor.get_statistics()

        return {
            'discovery_metadata': {
                'target_url': self.target_url,
                'domain': self.domain,
                'discovery_time': datetime.utcnow(),
                'total_duration_seconds': (datetime.utcnow() - initial_result.get('page_load_time', datetime.utcnow())).total_seconds()
            },
            'initial_analysis': initial_result,
            'exploration_results': exploration_result,
            'api_analysis': api_analysis,
            'authentication_analysis': auth_analysis,
            'network_statistics': final_stats,
            'summary': {
                'total_endpoints_discovered': api_analysis.get('total_endpoints', 0),
                'authentication_required': auth_analysis.get('requires_authentication', False),
                'interactions_performed': exploration_result.get('interactions_performed', 0),
                'api_calls_triggered': len(exploration_result.get('api_calls_triggered', [])),
                'discovery_successful': True
            }
        }
