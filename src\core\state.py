"""
State management for Cipher-Spy workflow.

Defines the shared state structures used by agents in the LangGraph workflow.
Handles state transitions, data persistence, and inter-agent communication.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class ScanStatus(str, Enum):
    """Enumeration of possible scan statuses."""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentStatus(str, Enum):
    """Enumeration of possible agent statuses."""
    IDLE = "idle"
    RUNNING = "running"
    WAITING = "waiting"
    COMPLETED = "completed"
    FAILED = "failed"


class ExploitStatus(str, Enum):
    """Enumeration of exploit execution statuses."""
    PENDING = "pending"
    AWAITING_APPROVAL = "awaiting_approval"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TargetInfo:
    """Information about a scan target."""
    url: str
    domain: str
    scope: List[str] = field(default_factory=list)
    credentials: Optional[Dict[str, str]] = None
    headers: Optional[Dict[str, str]] = None
    cookies: Optional[Dict[str, str]] = None


@dataclass
class PageInfo:
    """Information about a discovered page."""
    url: str
    title: Optional[str] = None
    status_code: int = 200
    content_type: Optional[str] = None
    content_hash: Optional[str] = None
    screenshot_path: Optional[str] = None
    discovered_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class EndpointInfo:
    """Information about a discovered API endpoint."""
    url: str
    method: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    response_status: Optional[int] = None
    response_content_type: Optional[str] = None
    response_sample: Optional[str] = None
    discovered_from: Optional[str] = None
    discovered_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class TechnologyInfo:
    """Information about detected technology."""
    name: str
    version: Optional[str] = None
    category: str = "unknown"
    confidence: float = 0.0
    detection_method: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VulnerabilityInfo:
    """Information about a potential vulnerability."""
    id: str
    name: str
    description: str
    severity: str = "unknown"
    cve_id: Optional[str] = None
    affected_technology: Optional[str] = None
    confidence: float = 0.0
    evidence: List[str] = field(default_factory=list)
    discovered_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ExploitPlan:
    """Detailed exploit execution plan."""
    id: str = field(default_factory=lambda: str(uuid4()))
    vulnerability_id: str = ""
    name: str = ""
    description: str = ""
    steps: List[str] = field(default_factory=list)
    payloads: List[str] = field(default_factory=list)
    expected_outcome: str = ""
    risk_level: str = "medium"
    status: ExploitStatus = ExploitStatus.PENDING
    created_at: datetime = field(default_factory=datetime.utcnow)
    approved_at: Optional[datetime] = None
    executed_at: Optional[datetime] = None
    results: Optional[Dict[str, Any]] = None


class AgentState(BaseModel):
    """State for individual agents."""
    agent_id: str
    agent_type: str
    status: AgentStatus = AgentStatus.IDLE
    current_task: Optional[str] = None
    progress: float = 0.0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True


class ScanState(BaseModel):
    """
    Central state object for the entire scan workflow.
    
    This state is shared between all agents and contains all discovered
    information, current progress, and coordination data.
    """
    
    # Scan metadata
    scan_id: str = Field(default_factory=lambda: str(uuid4()))
    status: ScanStatus = ScanStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    # Target information
    target: Optional[TargetInfo] = None
    
    # Agent states
    agents: Dict[str, AgentState] = Field(default_factory=dict)
    current_agent: Optional[str] = None
    
    # Discovered data
    pages: List[PageInfo] = Field(default_factory=list)
    endpoints: List[EndpointInfo] = Field(default_factory=list)
    technologies: List[TechnologyInfo] = Field(default_factory=list)
    vulnerabilities: List[VulnerabilityInfo] = Field(default_factory=list)
    
    # Exploit planning
    exploit_plans: List[ExploitPlan] = Field(default_factory=list)
    pending_approvals: List[str] = Field(default_factory=list)  # Exploit plan IDs
    
    # Crawling state
    crawled_urls: Set[str] = Field(default_factory=set)
    pending_urls: Set[str] = Field(default_factory=set)
    failed_urls: Set[str] = Field(default_factory=set)
    crawl_depth: int = 0
    
    # Configuration
    config: Dict[str, Any] = Field(default_factory=dict)
    
    # Workflow control
    should_continue: bool = True
    human_intervention_required: bool = False
    intervention_message: Optional[str] = None
    
    class Config:
        use_enum_values = True
        arbitrary_types_allowed = True
    
    def add_agent(self, agent_id: str, agent_type: str) -> None:
        """Add a new agent to the state."""
        self.agents[agent_id] = AgentState(
            agent_id=agent_id,
            agent_type=agent_type
        )
    
    def update_agent_status(
        self,
        agent_id: str,
        status: AgentStatus,
        task: Optional[str] = None,
        progress: Optional[float] = None,
        error: Optional[str] = None
    ) -> None:
        """Update agent status and progress."""
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        agent.status = status
        
        if task is not None:
            agent.current_task = task
        if progress is not None:
            agent.progress = progress
        if error is not None:
            agent.error_message = error
        
        if status == AgentStatus.RUNNING and agent.started_at is None:
            agent.started_at = datetime.utcnow()
        elif status in [AgentStatus.COMPLETED, AgentStatus.FAILED]:
            agent.completed_at = datetime.utcnow()
    
    def add_page(self, page: PageInfo) -> None:
        """Add a discovered page to the state."""
        self.pages.append(page)
        self.crawled_urls.add(page.url)
    
    def add_endpoint(self, endpoint: EndpointInfo) -> None:
        """Add a discovered endpoint to the state."""
        self.endpoints.append(endpoint)
    
    def add_technology(self, tech: TechnologyInfo) -> None:
        """Add detected technology to the state."""
        self.technologies.append(tech)
    
    def add_vulnerability(self, vuln: VulnerabilityInfo) -> None:
        """Add a discovered vulnerability to the state."""
        self.vulnerabilities.append(vuln)
    
    def add_exploit_plan(self, plan: ExploitPlan) -> None:
        """Add an exploit plan and mark it for approval."""
        self.exploit_plans.append(plan)
        if plan.status == ExploitStatus.AWAITING_APPROVAL:
            self.pending_approvals.append(plan.id)
            self.human_intervention_required = True
            self.intervention_message = f"Exploit plan '{plan.name}' requires approval"
    
    def approve_exploit(self, plan_id: str) -> bool:
        """Approve an exploit plan for execution."""
        for plan in self.exploit_plans:
            if plan.id == plan_id:
                plan.status = ExploitStatus.APPROVED
                plan.approved_at = datetime.utcnow()
                if plan_id in self.pending_approvals:
                    self.pending_approvals.remove(plan_id)
                
                # Check if any approvals are still pending
                if not self.pending_approvals:
                    self.human_intervention_required = False
                    self.intervention_message = None
                
                return True
        return False
    
    def reject_exploit(self, plan_id: str, reason: Optional[str] = None) -> bool:
        """Reject an exploit plan."""
        for plan in self.exploit_plans:
            if plan.id == plan_id:
                plan.status = ExploitStatus.REJECTED
                if reason:
                    plan.results = {"rejection_reason": reason}
                if plan_id in self.pending_approvals:
                    self.pending_approvals.remove(plan_id)
                
                # Check if any approvals are still pending
                if not self.pending_approvals:
                    self.human_intervention_required = False
                    self.intervention_message = None
                
                return True
        return False
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the current scan state."""
        return {
            "scan_id": self.scan_id,
            "status": self.status,
            "target_url": self.target.url if self.target else None,
            "pages_discovered": len(self.pages),
            "endpoints_discovered": len(self.endpoints),
            "technologies_detected": len(self.technologies),
            "vulnerabilities_found": len(self.vulnerabilities),
            "exploit_plans": len(self.exploit_plans),
            "pending_approvals": len(self.pending_approvals),
            "human_intervention_required": self.human_intervention_required,
            "current_agent": self.current_agent,
            "progress": {
                agent_id: agent.progress 
                for agent_id, agent in self.agents.items()
            }
        }
