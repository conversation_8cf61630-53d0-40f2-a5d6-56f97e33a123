/**
 * Cipher-Spy DevTools Integration
 * 
 * Creates a DevTools panel for advanced network analysis and API discovery.
 */

// Create the Cipher-Spy panel in DevTools
chrome.devtools.panels.create(
  "Cipher-Spy",
  "icons/icon16.png",
  "devtools/panel.html",
  function(panel) {
    console.log("Cipher-Spy DevTools panel created");
    
    // Panel event handlers
    panel.onShown.addListener(function(window) {
      console.log("Cipher-Spy panel shown");
      // Initialize panel when shown
      if (window.initializePanel) {
        window.initializePanel();
      }
    });
    
    panel.onHidden.addListener(function() {
      console.log("Cipher-Spy panel hidden");
    });
  }
);

// Listen for network events
chrome.devtools.network.onRequestFinished.addListener(function(request) {
  // Forward network requests to our panel
  chrome.runtime.sendMessage({
    action: 'devtools_request',
    request: {
      url: request.request.url,
      method: request.request.method,
      headers: request.request.headers,
      response: {
        status: request.response.status,
        headers: request.response.headers,
        content: request.response.content
      }
    }
  });
});
