#!/usr/bin/env python3
"""
Enhanced Pump.fun API Client

Production-ready client with rate limiting, caching, and error handling.
"""

import requests
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

class EnhancedPumpClient:
    """Enhanced client for pump.fun advanced APIs."""

    def __init__(self, rate_limit_per_minute: int = 20):
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://pump.fun',
            'Referer': 'https://pump.fun/advanced/coin?scan=true'
        }

        self.session = requests.Session()
        self.session.headers.update(self.base_headers)

        # Rate limiting
        self.min_interval = 60.0 / rate_limit_per_minute
        self.last_request_time = 0

        # Simple cache
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes

        # Performance tracking
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'errors': 0
        }

    def _rate_limit(self):
        """Implement rate limiting."""
        now = time.time()
        elapsed = now - self.last_request_time

        if elapsed < self.min_interval:
            sleep_time = self.min_interval - elapsed
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def _get_cache_key(self, url: str, params: Dict) -> str:
        """Generate cache key."""
        param_str = json.dumps(params, sort_keys=True)
        return f"{url}:{param_str}"

    def _get_cached(self, cache_key: str) -> Optional[Dict]:
        """Get cached response if valid."""
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                self.stats['cache_hits'] += 1
                return cached_data
            else:
                del self.cache[cache_key]
        return None

    def _cache_response(self, cache_key: str, data: Dict):
        """Cache response data."""
        self.cache[cache_key] = (data, time.time())

    def _make_request(self, url: str, params: Dict = None) -> Dict:
        """Make API request with rate limiting and caching."""
        params = params or {}
        cache_key = self._get_cache_key(url, params)

        # Check cache first
        cached_data = self._get_cached(cache_key)
        if cached_data:
            return cached_data

        # Rate limiting
        self._rate_limit()

        try:
            response = self.session.get(url, params=params, timeout=15)
            self.stats['total_requests'] += 1

            if response.status_code == 200:
                data = response.json()
                self._cache_response(cache_key, data)
                return data
            else:
                self.stats['errors'] += 1
                raise Exception(f"API request failed: {response.status_code}")

        except Exception as e:
            self.stats['errors'] += 1
            raise Exception(f"Request error: {e}")

    def get_advanced_coins(self, sort_by: str = 'creationTime', limit: int = 30, offset: int = 0) -> Dict:
        """Get advanced coin listings."""
        url = 'https://advanced-api-v2.pump.fun/coins/list'
        params = {
            'sortBy': sort_by,
            'limit': limit,
            'offset': offset
        }
        return self._make_request(url, params)

    def get_graduated_coins(self, sort_by: str = 'creationTime', limit: int = 30) -> Dict:
        """Get graduated coins."""
        url = 'https://advanced-api-v2.pump.fun/coins/graduated'
        params = {
            'sortBy': sort_by,
            'limit': limit
        }
        return self._make_request(url, params)

    def find_high_potential_coins(self, min_volume: int = 10000, max_market_cap: int = 100000) -> List[Dict]:
        """Find high potential coins based on criteria."""
        # Get volume-sorted coins
        volume_coins = self.get_advanced_coins(sort_by='volume', limit=200)

        high_potential = []
        for coin in volume_coins.get('coins', []):
            volume = coin.get('volume', 0)
            market_cap = coin.get('marketCap', 0)
            holders = coin.get('numHolders', 0)

            # Apply filters
            if (volume >= min_volume and
                market_cap <= max_market_cap and
                holders >= 50):

                # Calculate opportunity score
                score = self._calculate_opportunity_score(coin)
                coin['opportunity_score'] = score
                high_potential.append(coin)

        # Sort by opportunity score
        high_potential.sort(key=lambda x: x['opportunity_score'], reverse=True)
        return high_potential[:20]

    def _calculate_opportunity_score(self, coin: Dict) -> float:
        """Calculate opportunity score (0-100)."""
        score = 0.0

        volume = coin.get('volume', 0)
        market_cap = coin.get('marketCap', 0)
        holders = coin.get('numHolders', 0)

        # Volume factor (0-30 points)
        if volume > 100000:
            score += 30
        elif volume > 50000:
            score += 25
        elif volume > 10000:
            score += 20

        # Market cap factor (0-30 points)
        if 1000 < market_cap < 50000:
            score += 30
        elif market_cap < 100000:
            score += 20

        # Holders factor (0-25 points)
        if holders > 200:
            score += 25
        elif holders > 100:
            score += 20
        elif holders > 50:
            score += 15

        # Liquidity factor (0-15 points)
        if market_cap > 0:
            liquidity_ratio = volume / market_cap
            if liquidity_ratio > 0.3:
                score += 15
            elif liquidity_ratio > 0.1:
                score += 10

        return min(100, score)

    def get_stats(self) -> Dict:
        """Get client performance statistics."""
        total_requests = max(1, self.stats['total_requests'])
        return {
            'total_requests': self.stats['total_requests'],
            'cache_hit_rate': self.stats['cache_hits'] / total_requests,
            'error_rate': self.stats['errors'] / total_requests,
            'cache_size': len(self.cache)
        }

# Example usage
if __name__ == "__main__":
    client = EnhancedPumpClient()

    # Get latest coins
    latest = client.get_advanced_coins(sort_by='creationTime', limit=50)
    print(f"Latest coins: {len(latest.get('coins', []))}")

    # Find opportunities
    opportunities = client.find_high_potential_coins()
    print(f"High potential coins: {len(opportunities)}")

    for coin in opportunities[:5]:
        print(f"  {coin.get('name', 'Unknown')}: Score {coin['opportunity_score']:.1f}")

    # Show stats
    stats = client.get_stats()
    print(f"Stats: {stats['cache_hit_rate']:.1%} cache hit rate")
