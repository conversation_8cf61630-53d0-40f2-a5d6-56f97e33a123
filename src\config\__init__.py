"""
Configuration module for Cipher-Spy.

This module provides centralized configuration management using Pydantic settings.
It handles environment variables, default values, and configuration validation.
"""

from .settings import Settings, get_settings
from .database import DatabaseManager, init_databases, close_databases

__all__ = [
    "Settings",
    "get_settings", 
    "DatabaseManager",
    "init_databases",
    "close_databases"
]
