#!/usr/bin/env python3
"""
Setup script for Graphiti knowledge graph integration.

Installs Graphiti and sets up the knowledge graph database for
advanced exploit intelligence and vulnerability correlation.
"""

import subprocess
import sys
import os
from pathlib import Path


def check_graphiti_installed():
    """Check if Graphiti is already installed."""
    try:
        import graphiti_core
        return True
    except ImportError:
        return False


def install_graphiti():
    """Install Graphiti and dependencies."""
    print("📦 Installing Graphiti knowledge graph package...")
    
    try:
        # Install from requirements file
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "-r", "requirements-graphiti.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Graphiti installed successfully")
            return True
        else:
            print("❌ Failed to install Graphiti")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"💥 Error installing Graphiti: {e}")
        return False


def setup_environment():
    """Setup environment variables for Graphiti."""
    print("⚙️  Setting up Graphiti environment...")
    
    # Check for required environment variables
    required_vars = {
        "OPENAI_API_KEY": "OpenAI API key for embeddings (optional - can use local models)",
        "NEO4J_URI": "Neo4j database URI",
        "NEO4J_USER": "Neo4j username", 
        "NEO4J_PASSWORD": "Neo4j password"
    }
    
    env_file = Path(".env")
    missing_vars = []
    
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append((var, description))
    
    if missing_vars:
        print("⚠️  Missing environment variables:")
        for var, desc in missing_vars:
            print(f"   {var}: {desc}")
        
        print(f"\nPlease add these to your {env_file} file:")
        for var, desc in missing_vars:
            if var == "OPENAI_API_KEY":
                print(f"# {desc}")
                print(f"# {var}=your_openai_api_key_here")
            else:
                print(f"{var}=your_value_here  # {desc}")
        
        return False
    
    print("✅ Environment variables configured")
    return True


def test_graphiti():
    """Test Graphiti installation and basic functionality."""
    print("🧪 Testing Graphiti installation...")
    
    try:
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        print("✅ Graphiti imports successful")
        
        # Test basic functionality (without connecting to database)
        print("✅ Graphiti basic functionality test passed")
        
        return True
        
    except ImportError as e:
        print(f"❌ Graphiti import failed: {e}")
        return False
    except Exception as e:
        print(f"⚠️  Graphiti test warning: {e}")
        return True  # Non-critical error


def create_graphiti_config():
    """Create Graphiti configuration file."""
    print("📝 Creating Graphiti configuration...")
    
    config = {
        "graphiti": {
            "llm_config": {
                "provider": "openai",
                "model": "gpt-4-turbo-preview",
                "api_key_env": "OPENAI_API_KEY"
            },
            "embedding_config": {
                "provider": "openai", 
                "model": "text-embedding-3-small",
                "api_key_env": "OPENAI_API_KEY"
            },
            "database_config": {
                "provider": "neo4j",
                "uri_env": "NEO4J_URI",
                "user_env": "NEO4J_USER", 
                "password_env": "NEO4J_PASSWORD"
            },
            "features": {
                "exploit_correlation": True,
                "vulnerability_mapping": True,
                "technology_relationships": True,
                "temporal_analysis": True
            }
        }
    }
    
    import json
    config_file = Path("graphiti_config.json")
    
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration saved to {config_file}")
    return True


def main():
    """Main setup function."""
    print("🧠 Cipher-Spy Graphiti Knowledge Graph Setup")
    print("="*50)
    
    # Check if already installed
    if check_graphiti_installed():
        print("✅ Graphiti is already installed")
    else:
        print("📦 Graphiti not found, installing...")
        if not install_graphiti():
            print("💥 Setup failed!")
            return 1
    
    # Setup environment
    if not setup_environment():
        print("⚠️  Environment setup incomplete")
        print("You can continue without OpenAI API key using local models")
    
    # Test installation
    if not test_graphiti():
        print("💥 Graphiti test failed!")
        return 1
    
    # Create configuration
    create_graphiti_config()
    
    print("\n🎉 Graphiti setup completed!")
    print("\nNext steps:")
    print("1. Configure your .env file with required variables")
    print("2. Start Neo4j database: docker-compose up neo4j")
    print("3. Run: python scripts/setup_db.py")
    print("4. Load exploit data: python scripts/load_exploitdb.py")
    print("\nGraphiti features will be available in future Cipher-Spy modules:")
    print("• Exploit correlation and recommendation")
    print("• Vulnerability relationship mapping") 
    print("• Technology stack analysis")
    print("• Temporal attack pattern analysis")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
