#!/usr/bin/env python3
"""
Create Chrome Extension Icons for Cipher-Spy

Creates simple placeholder icons for the Chrome extension.
"""

import os
from pathlib import Path


def create_simple_icons():
    """Create simple SVG-based icons and convert to PNG."""
    
    # Create icons directory
    icons_dir = Path("chrome_extension/icons")
    icons_dir.mkdir(exist_ok=True)
    
    # Try to use PIL for better quality icons
    try:
        from PIL import Image, ImageDraw, ImageFont
        create_pil_icons(icons_dir)
        print("✅ Created high-quality PNG icons using PIL")
        return
    except ImportError:
        print("📝 PIL not available, creating simple placeholder icons...")
    
    # Fallback: Create simple placeholder files
    create_placeholder_icons(icons_dir)


def create_pil_icons(icons_dir):
    """Create icons using PIL for better quality."""
    from PIL import Image, ImageDraw, ImageFont
    
    sizes = [16, 32, 48, 128]
    bg_color = '#1a73e8'  # Cipher-Spy blue
    text_color = '#ffffff'  # White text
    
    for size in sizes:
        # Create image with blue background
        img = Image.new('RGBA', (size, size), bg_color)
        draw = ImageDraw.Draw(img)
        
        # Calculate font size
        font_size = max(6, size // 4)
        
        try:
            # Try to load a better font
            font = ImageFont.load_default()
        except:
            font = None
        
        # Draw 'CS' text (Cipher-Spy)
        text = '🔍' if size >= 32 else 'CS'
        
        if font and text == 'CS':
            # Get text dimensions
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            # Estimate text size
            text_width = len(text) * (font_size // 2) if text == 'CS' else size // 2
            text_height = font_size
        
        # Center the text
        x = (size - text_width) // 2
        y = (size - text_height) // 2
        
        # Draw text
        if text == '🔍':
            # For emoji, use larger font
            try:
                emoji_font = ImageFont.truetype("seguiemj.ttf", size // 2) if size >= 32 else font
                draw.text((x, y), text, font=emoji_font, embedded_color=True)
            except:
                # Fallback to CS text
                draw.text((x, y), 'CS', fill=text_color, font=font)
        else:
            draw.text((x, y), text, fill=text_color, font=font)
        
        # Add subtle border
        border_color = '#0d47a1'
        draw.rectangle([0, 0, size-1, size-1], outline=border_color, width=1)
        
        # Add subtle gradient effect for larger icons
        if size >= 48:
            # Add a subtle highlight
            highlight_color = '#4285f4'
            draw.ellipse([2, 2, size//3, size//3], fill=highlight_color)
        
        # Save icon
        icon_path = icons_dir / f"icon{size}.png"
        img.save(icon_path)
        print(f"   ✅ Created {icon_path}")


def create_placeholder_icons(icons_dir):
    """Create simple placeholder icon files."""
    
    # Simple base64 encoded 16x16 PNG (blue square with white CS)
    icon_data = {
        16: b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\x00\x00\x00\x19tEXtSoftware\x00Adobe ImageReadyq\xc9e<\x00\x00\x00\x0eIDATx\xdab\x00\x02\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82',
        32: b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00 \x00\x00\x00 \x08\x06\x00\x00\x00szz\xf4\x00\x00\x00\x19tEXtSoftware\x00Adobe ImageReadyq\xc9e<\x00\x00\x00\x0eIDATx\xdab\x00\x02\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82',
        48: b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x000\x00\x00\x000\x08\x06\x00\x00\x00W\x02\xf9\x87\x00\x00\x00\x19tEXtSoftware\x00Adobe ImageReadyq\xc9e<\x00\x00\x00\x0eIDATx\xdab\x00\x02\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82',
        128: b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x80\x00\x00\x00\x80\x08\x06\x00\x00\x00\xc3>a\xcb\x00\x00\x00\x19tEXtSoftware\x00Adobe ImageReadyq\xc9e<\x00\x00\x00\x0eIDATx\xdab\x00\x02\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
    }
    
    # Create simple colored squares as placeholders
    for size in [16, 32, 48, 128]:
        icon_path = icons_dir / f"icon{size}.png"
        
        # Create a simple colored square using a minimal PNG
        # This is a very basic approach - in production you'd want proper icons
        try:
            # Create a simple SVG and convert it
            svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{size}" height="{size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="{size}" height="{size}" fill="#1a73e8"/>
  <text x="{size//2}" y="{size//2 + 4}" text-anchor="middle" fill="white" font-family="Arial" font-size="{max(8, size//4)}">CS</text>
</svg>'''
            
            # Save as SVG first, then we'll note that it needs conversion
            svg_path = icons_dir / f"icon{size}.svg"
            with open(svg_path, 'w') as f:
                f.write(svg_content)
            
            # Create a minimal PNG placeholder
            # This is a 1x1 blue pixel PNG that Chrome can at least load
            minimal_png = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x0cIDATx\x9cc\xf8\x0f\x00\x00\x01\x00\x01\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
            
            with open(icon_path, 'wb') as f:
                f.write(minimal_png)
            
            print(f"   📝 Created placeholder {icon_path}")
            
        except Exception as e:
            print(f"   ⚠️ Could not create {icon_path}: {e}")


def create_better_icons_with_canvas():
    """Create better icons using HTML5 Canvas approach."""
    
    html_template = '''<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas" width="{size}" height="{size}"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Fill background
        ctx.fillStyle = '#1a73e8';
        ctx.fillRect(0, 0, {size}, {size});
        
        // Add text
        ctx.fillStyle = 'white';
        ctx.font = '{font_size}px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('🔍', {size}/2, {size}/2);
        
        // Convert to blob and download
        canvas.toBlob(function(blob) {{
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'icon{size}.png';
            a.click();
        }});
    </script>
</body>
</html>'''
    
    # This would be used in a browser environment
    # For now, we'll stick with the PIL approach
    pass


def main():
    """Main function to create icons."""
    print("🎨 Creating Chrome Extension Icons for Cipher-Spy...")
    
    try:
        create_simple_icons()
        print("\n✅ Icons created successfully!")
        print("\n📋 Created files:")
        
        icons_dir = Path("chrome_extension/icons")
        for icon_file in sorted(icons_dir.glob("*.png")):
            print(f"   - {icon_file}")
        
        print("\n💡 Tips:")
        print("   - For production, replace with professional icons")
        print("   - Icons should be square and work at small sizes")
        print("   - Consider using a design tool like Figma or Adobe Illustrator")
        
    except Exception as e:
        print(f"❌ Failed to create icons: {e}")
        print("\n🔧 Manual fix:")
        print("   1. Create 16x16, 32x32, 48x48, and 128x128 PNG files")
        print("   2. Save them as icon16.png, icon32.png, icon48.png, icon128.png")
        print("   3. Place them in chrome_extension/icons/ directory")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
