"""
Cipher-Spy Universal API Discovery Framework

A generalized system for discovering, analyzing, and reverse-engineering APIs
from any website. This framework abstracts the pump.fun-specific implementation
into a universal system that can be applied to any target website.

Key Components:
- BaseAnalyzer: Abstract base class for all API analyzers
- TargetConfig: Configuration system for target websites
- DiscoveryStrategies: Modular endpoint detection strategies
- NavigationPatterns: Adaptable website navigation patterns
- SchemaInference: Universal response schema analysis
- DocumentationGenerator: Automated API documentation creation

Author: Cipher-Spy Development Team
License: MIT
Version: 2.0.0
"""

from .base_analyzer import BaseAPIAnalyzer
from .target_config import TargetConfig, APIEndpointConfig
from .universal_analyzer import UniversalAPIAnalyzer
from .discovery_engine import DiscoveryEngine
from .parameter_fuzzer import <PERSON>meterFuzzer
from .schema_analyzer import SchemaAnalyzer
from .documentation_generator import DocumentationGenerator

__version__ = "2.0.0"
__author__ = "Cipher-Spy Development Team"
__license__ = "MIT"

__all__ = [
    "BaseAPIAnalyzer",
    "TargetConfig", 
    "APIEndpointConfig",
    "UniversalAPIAnalyzer",
    "DiscoveryEngine",
    "ParameterFuzzer",
    "SchemaAnalyzer",
    "DocumentationGenerator"
]
