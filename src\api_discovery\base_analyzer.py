#!/usr/bin/env python3
"""
Base API Analyzer for Cipher-Spy Universal API Discovery Framework

Abstract base class that defines the interface for all API analyzers.
Provides common functionality and enforces consistent implementation
across different website-specific analyzers.
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
import requests
from urllib.parse import urlparse, urljoin

from .target_config import TargetConfig, APIEndpointConfig


@dataclass
class ParameterTest:
    """Test result for a specific parameter combination."""
    parameter_name: str
    parameter_value: Any
    test_type: str
    success: bool
    response_code: int
    response_data: Optional[Any]
    response_time: float
    error_message: Optional[str]
    timestamp: str
    endpoint_url: str


@dataclass
class APISchema:
    """Complete schema definition for an API endpoint."""
    endpoint_url: str
    method: str
    parameters: Dict[str, Dict[str, Any]]
    response_schema: Dict[str, Any]
    error_responses: Dict[int, Dict[str, Any]]
    rate_limits: Dict[str, Any]
    business_logic: Dict[str, Any]
    examples: List[Dict[str, Any]]
    timestamp: str
    confidence_score: float


@dataclass
class DiscoveryResult:
    """Result of API discovery process."""
    target_domain: str
    discovered_endpoints: List[APISchema]
    parameter_tests: List[ParameterTest]
    business_intelligence: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    discovery_metadata: Dict[str, Any]
    timestamp: str
    total_duration: float


class BaseAPIAnalyzer(ABC):
    """
    Abstract base class for all API analyzers in the Cipher-Spy framework.

    This class defines the interface that all website-specific analyzers must implement,
    while providing common functionality for parameter testing, schema analysis,
    and result management.
    """

    def __init__(self, target_config: TargetConfig, results_dir: Optional[Path] = None):
        """
        Initialize the base API analyzer.

        Args:
            target_config: Configuration for the target website
            results_dir: Directory to save analysis results
        """
        self.target_config = target_config
        self.results_dir = results_dir or Path(f"api_discovery_results_{target_config.domain}")
        self.results_dir.mkdir(exist_ok=True)

        # Create specialized subdirectories
        for subdir in ["schemas", "documentation", "clients", "intelligence", "testing", "monitoring"]:
            (self.results_dir / subdir).mkdir(exist_ok=True)

        # Analysis state
        self.discovered_schemas: Dict[str, APISchema] = {}
        self.parameter_tests: List[ParameterTest] = []
        self.session = requests.Session()

        # Configure session with target-specific headers
        self._configure_session()

        # Discovery metrics
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None

    def _configure_session(self):
        """Configure the requests session with target-specific settings."""
        headers = {
            'User-Agent': self.target_config.user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
        }

        # Add target-specific headers
        if self.target_config.custom_headers:
            headers.update(self.target_config.custom_headers)

        self.session.headers.update(headers)

        # Configure rate limiting
        if self.target_config.rate_limit_delay:
            self.rate_limit_delay = self.target_config.rate_limit_delay
        else:
            self.rate_limit_delay = 1.0  # Default 1 second

    @abstractmethod
    async def discover_endpoints(self) -> List[APIEndpointConfig]:
        """
        Discover API endpoints specific to the target website.

        Returns:
            List[APIEndpointConfig]: Discovered API endpoints
        """
        pass

    @abstractmethod
    async def extract_business_logic(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract business logic and intelligence from API responses.

        Args:
            endpoint_data: Raw endpoint response data

        Returns:
            Dict[str, Any]: Extracted business intelligence
        """
        pass

    @abstractmethod
    def get_parameter_test_sets(self) -> Dict[str, List[str]]:
        """
        Get parameter test sets specific to the target domain.

        Returns:
            Dict[str, List[str]]: Parameter categories and test values
        """
        pass

    @abstractmethod
    def get_test_values(self) -> Dict[str, List[Any]]:
        """
        Get test values specific to the target domain.

        Returns:
            Dict[str, List[Any]]: Test value categories and values
        """
        pass

    async def perform_comprehensive_analysis(self) -> DiscoveryResult:
        """
        Perform comprehensive API discovery and analysis.

        Returns:
            DiscoveryResult: Complete analysis results
        """
        self.start_time = datetime.now()
        print(f"🔬 Starting Universal API Discovery for {self.target_config.domain}")
        print("="*80)

        try:
            # Phase 1: Endpoint Discovery
            print("🎯 Phase 1: Endpoint Discovery")
            print("-" * 50)
            discovered_endpoints = await self.discover_endpoints()

            # Phase 2: Parameter Discovery
            print("\n🔍 Phase 2: Parameter Discovery")
            print("-" * 50)
            parameter_results = await self._systematic_parameter_discovery(discovered_endpoints)

            # Phase 3: Schema Analysis
            print("\n📊 Phase 3: Schema Analysis")
            print("-" * 50)
            schema_results = await self._comprehensive_schema_analysis(discovered_endpoints)

            # Phase 4: Business Intelligence
            print("\n🧠 Phase 4: Business Intelligence Extraction")
            print("-" * 50)
            intelligence_results = await self._extract_business_intelligence(schema_results)

            # Phase 5: Performance Analysis
            print("\n⚡ Phase 5: Performance Analysis")
            print("-" * 50)
            performance_results = await self._analyze_performance_characteristics()

            # Generate final result
            self.end_time = datetime.now()
            total_duration = (self.end_time - self.start_time).total_seconds()

            result = DiscoveryResult(
                target_domain=self.target_config.domain,
                discovered_endpoints=list(self.discovered_schemas.values()),
                parameter_tests=self.parameter_tests,
                business_intelligence=intelligence_results,
                performance_metrics=performance_results,
                discovery_metadata={
                    'analyzer_type': self.__class__.__name__,
                    'target_config': asdict(self.target_config),
                    'endpoints_discovered': len(discovered_endpoints),
                    'parameters_tested': len(self.parameter_tests),
                    'successful_tests': len([t for t in self.parameter_tests if t.success])
                },
                timestamp=self.start_time.isoformat(),
                total_duration=total_duration
            )

            # Save results
            await self._save_results(result)

            print(f"\n✅ API Discovery completed in {total_duration:.2f} seconds!")
            print(f"📁 Results saved to: {self.results_dir}")

            return result

        except Exception as e:
            print(f"\n💥 API Discovery failed: {e}")
            import traceback
            traceback.print_exc()
            raise

    async def _systematic_parameter_discovery(self, endpoints: List[APIEndpointConfig]) -> Dict[str, Any]:
        """Systematically discover parameters for all endpoints."""
        print("🔍 Discovering parameters through systematic testing...")

        parameter_test_sets = self.get_parameter_test_sets()
        test_values = self.get_test_values()

        total_tests = 0
        successful_discoveries = 0

        for endpoint in endpoints:
            print(f"\n   📡 Testing {endpoint.name}: {endpoint.url}")

            for category, params in parameter_test_sets.items():
                print(f"      🎯 Testing {category} parameters...")

                for param in params:
                    for value_type, values in test_values.items():
                        if self._should_test_combination(param, value_type):
                            for value in values[:3]:  # Test first 3 values
                                test_result = await self._test_parameter(
                                    endpoint.url, param, value, endpoint.name
                                )

                                total_tests += 1
                                if test_result.success:
                                    successful_discoveries += 1
                                    print(f"         ✅ Confirmed: {param}={value}")

                                # Rate limiting
                                await asyncio.sleep(self.rate_limit_delay)

        return {
            'total_tests': total_tests,
            'successful_discoveries': successful_discoveries,
            'discovery_rate': successful_discoveries / total_tests * 100 if total_tests > 0 else 0
        }

    def _should_test_combination(self, param: str, value_type: str) -> bool:
        """Determine if a parameter/value type combination should be tested."""
        # Default implementation - can be overridden by subclasses
        return True

    async def _test_parameter(self, url: str, param: str, value: Any, endpoint_name: str) -> ParameterTest:
        """Test a specific parameter with a specific value."""
        start_time = time.time()

        try:
            params = {param: value}
            response = self.session.get(url, params=params, timeout=10)

            response_time = time.time() - start_time

            # Try to parse JSON response
            response_data = None
            try:
                response_data = response.json()
            except:
                response_data = response.text[:200] if response.text else None

            test_result = ParameterTest(
                parameter_name=param,
                parameter_value=value,
                test_type='single_parameter',
                success=response.status_code == 200,
                response_code=response.status_code,
                response_data=response_data,
                response_time=response_time,
                error_message=None,
                timestamp=datetime.now().isoformat(),
                endpoint_url=url
            )

        except Exception as e:
            response_time = time.time() - start_time
            test_result = ParameterTest(
                parameter_name=param,
                parameter_value=value,
                test_type='single_parameter',
                success=False,
                response_code=0,
                response_data=None,
                response_time=response_time,
                error_message=str(e),
                timestamp=datetime.now().isoformat(),
                endpoint_url=url
            )

        self.parameter_tests.append(test_result)
        return test_result

    async def _comprehensive_schema_analysis(self, endpoints: List[APIEndpointConfig]) -> Dict[str, Any]:
        """Perform comprehensive schema analysis for all endpoints."""
        print("📊 Analyzing response schemas...")

        schema_results = {}

        for endpoint in endpoints:
            print(f"\n   🔍 Analyzing {endpoint.name} schema...")

            # Collect response samples
            response_samples = []

            try:
                # Base response
                response = self.session.get(endpoint.url, timeout=15)
                if response.status_code == 200:
                    response_samples.append(response.json())
                    print(f"      ✅ Base response collected")

                # Test with different parameters if available
                if hasattr(endpoint, 'test_parameters'):
                    for params in endpoint.test_parameters:
                        try:
                            response = self.session.get(endpoint.url, params=params, timeout=15)
                            if response.status_code == 200:
                                response_samples.append(response.json())
                            await asyncio.sleep(self.rate_limit_delay)
                        except Exception:
                            continue

            except Exception as e:
                print(f"      ❌ Schema analysis failed: {e}")
                continue

            # Analyze schema from samples
            if response_samples:
                schema_analysis = self._analyze_response_schema(response_samples, endpoint.name)
                schema_results[endpoint.name] = schema_analysis

                # Create API schema object
                api_schema = APISchema(
                    endpoint_url=endpoint.url,
                    method=endpoint.method,
                    parameters=schema_analysis.get('parameters', {}),
                    response_schema=schema_analysis.get('response_schema', {}),
                    error_responses=schema_analysis.get('error_responses', {}),
                    rate_limits=schema_analysis.get('rate_limits', {}),
                    business_logic=schema_analysis.get('business_logic', {}),
                    examples=schema_analysis.get('examples', []),
                    timestamp=datetime.now().isoformat(),
                    confidence_score=schema_analysis.get('confidence_score', 0.0)
                )

                self.discovered_schemas[endpoint.name] = api_schema

                print(f"      📊 Schema analysis completed")

        return schema_results

    def _analyze_response_schema(self, samples: List[Dict], endpoint_name: str) -> Dict[str, Any]:
        """Analyze response schema from multiple samples."""
        schema = {
            'endpoint_name': endpoint_name,
            'sample_count': len(samples),
            'fields': {},
            'data_patterns': {},
            'confidence_score': 0.0,
            'timestamp': datetime.now().isoformat()
        }

        # Analyze each sample
        for i, sample in enumerate(samples):
            self._extract_fields_recursive(sample, schema['fields'], f"sample_{i}")

        # Calculate confidence score based on consistency
        if len(samples) > 1:
            schema['confidence_score'] = self._calculate_schema_confidence(schema['fields'], len(samples))
        else:
            schema['confidence_score'] = 0.5  # Medium confidence for single sample

        return schema

    def _extract_fields_recursive(self, data: Any, fields_dict: Dict, path: str = "", max_depth: int = 5):
        """Recursively extract all fields from response data."""
        if max_depth <= 0:
            return

        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key

                if current_path not in fields_dict:
                    fields_dict[current_path] = {
                        'type': type(value).__name__,
                        'examples': [],
                        'null_count': 0,
                        'occurrence_count': 0
                    }

                fields_dict[current_path]['occurrence_count'] += 1

                if value is None:
                    fields_dict[current_path]['null_count'] += 1
                else:
                    if len(fields_dict[current_path]['examples']) < 5:
                        if isinstance(value, (str, int, float, bool)):
                            fields_dict[current_path]['examples'].append(value)

                # Recurse into nested structures
                if isinstance(value, (dict, list)):
                    self._extract_fields_recursive(value, fields_dict, current_path, max_depth - 1)

        elif isinstance(data, list) and len(data) > 0:
            for i, item in enumerate(data[:3]):
                self._extract_fields_recursive(item, fields_dict, f"{path}[{i}]", max_depth - 1)

    def _calculate_schema_confidence(self, fields: Dict, sample_count: int) -> float:
        """Calculate confidence score for schema based on field consistency."""
        if not fields:
            return 0.0

        total_fields = len(fields)
        consistent_fields = 0

        for field_info in fields.values():
            # Field is consistent if it appears in most samples
            consistency_ratio = field_info['occurrence_count'] / sample_count
            if consistency_ratio >= 0.7:  # Appears in 70% or more samples
                consistent_fields += 1

        return consistent_fields / total_fields if total_fields > 0 else 0.0

    async def _extract_business_intelligence(self, schema_results: Dict[str, Any]) -> Dict[str, Any]:
        """Extract business intelligence from schema analysis."""
        print("🧠 Extracting business intelligence...")

        intelligence = {
            'domain_insights': {},
            'data_patterns': {},
            'business_metrics': {},
            'competitive_analysis': {},
            'timestamp': datetime.now().isoformat()
        }

        # Use the abstract method implemented by subclasses
        for endpoint_name, schema_data in schema_results.items():
            endpoint_intelligence = await self.extract_business_logic(schema_data)
            intelligence['domain_insights'][endpoint_name] = endpoint_intelligence

        return intelligence

    async def _analyze_performance_characteristics(self) -> Dict[str, Any]:
        """Analyze performance characteristics of discovered APIs."""
        print("⚡ Analyzing performance characteristics...")

        performance = {
            'response_times': {},
            'success_rates': {},
            'rate_limits': {},
            'reliability_metrics': {},
            'timestamp': datetime.now().isoformat()
        }

        # Analyze response times from parameter tests
        if self.parameter_tests:
            response_times = [test.response_time for test in self.parameter_tests if test.success]
            if response_times:
                performance['response_times'] = {
                    'average': sum(response_times) / len(response_times),
                    'min': min(response_times),
                    'max': max(response_times),
                    'count': len(response_times)
                }

            # Calculate success rates
            total_tests = len(self.parameter_tests)
            successful_tests = len([test for test in self.parameter_tests if test.success])
            performance['success_rates'] = {
                'overall': successful_tests / total_tests if total_tests > 0 else 0,
                'total_tests': total_tests,
                'successful_tests': successful_tests
            }

        return performance

    async def _save_results(self, result: DiscoveryResult):
        """Save analysis results to files."""
        # Save main result
        result_file = self.results_dir / f"discovery_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(result_file, 'w') as f:
            json.dump(asdict(result), f, indent=2, default=str)

        # Save individual schemas
        for endpoint_name, schema in self.discovered_schemas.items():
            schema_file = self.results_dir / "schemas" / f"{endpoint_name}_schema.json"
            with open(schema_file, 'w') as f:
                json.dump(asdict(schema), f, indent=2, default=str)

        # Save parameter tests
        tests_file = self.results_dir / "testing" / f"parameter_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(tests_file, 'w') as f:
            json.dump([asdict(test) for test in self.parameter_tests], f, indent=2, default=str)
