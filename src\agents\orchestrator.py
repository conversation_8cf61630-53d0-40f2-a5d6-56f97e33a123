"""
Orchestrator Agent for Cipher-Spy.

The central coordinator that manages the overall workflow and controls
the execution sequence of other agents. Acts as the supervisor in the
LangGraph workflow, making decisions about which agent to invoke next
based on the current state and progress.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

# Optional LangChain imports
try:
    from langchain.tools import BaseTool
    from langchain.schema import BaseMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    BaseTool = None
    BaseMessage = None
    LANGCHAIN_AVAILABLE = False

from .base_agent import BaseAgent
from ..core.state import ScanState, ScanStatus, AgentStatus
from ..core.exceptions import CipherSpyException
from ..utils.logging import get_logger


class OrchestratorAgent(BaseAgent):
    """
    Orchestrator agent that coordinates the red team swarm workflow.

    Responsibilities:
    - Initialize and coordinate other agents
    - Manage workflow state transitions
    - Handle human intervention points
    - Monitor overall progress
    - Make routing decisions between agents
    """

    def __init__(self, **kwargs):
        super().__init__(
            agent_id="orchestrator",
            agent_type="orchestrator",
            **kwargs
        )
        self.logger = get_logger(f"{__name__}.orchestrator")

        # Workflow configuration
        self.workflow_steps = [
            "crawler",
            "fingerprinter",
            "graph_rag",
            "exploit_planner",
            "executor"
        ]

        # Agent instances (to be initialized)
        self.agents: Dict[str, BaseAgent] = {}

        # Workflow state
        self.current_step = 0
        self.workflow_started = False
        self.workflow_completed = False

    async def execute(self, state: ScanState) -> ScanState:
        """
        Execute the orchestrator's main coordination logic.

        Args:
            state: Current scan state

        Returns:
            ScanState: Updated scan state with orchestration decisions
        """
        await self.start_execution(state)

        try:
            # Initialize workflow if not started
            if not self.workflow_started:
                await self._initialize_workflow(state)

            # Check for human intervention requirements
            if state.human_intervention_required:
                await self._handle_human_intervention(state)
                return state

            # Determine next action based on current state
            next_action = await self._determine_next_action(state)

            if next_action == "complete":
                await self._complete_workflow(state)
            elif next_action == "wait":
                await self._wait_for_completion(state)
            elif next_action in self.workflow_steps:
                await self._route_to_agent(state, next_action)
            else:
                raise CipherSpyException(f"Unknown action: {next_action}")

            await self.complete_execution(state, success=True)

        except Exception as e:
            await self.handle_error(state, e)
            raise

        return state

    async def _initialize_workflow(self, state: ScanState) -> None:
        """
        Initialize the workflow and prepare agents.

        Args:
            state: Current scan state
        """
        self.log_progress("Initializing workflow", 0.1)

        # Validate target information
        if not state.target:
            raise CipherSpyException("No target specified for scan")

        # Set scan status
        state.status = ScanStatus.RUNNING
        state.started_at = datetime.utcnow()

        # Initialize agent states
        for step in self.workflow_steps:
            state.add_agent(step, step)

        # Mark workflow as started
        self.workflow_started = True
        state.current_agent = self.workflow_steps[0]

        self.logger.info(f"Workflow initialized for target: {state.target.url}")

    async def _determine_next_action(self, state: ScanState) -> str:
        """
        Determine the next action based on current state.

        Args:
            state: Current scan state

        Returns:
            str: Next action to take
        """
        # Check if workflow is complete
        if self._is_workflow_complete(state):
            return "complete"

        # Check if current agent is still running
        current_agent = state.current_agent
        if current_agent and current_agent in state.agents:
            agent_state = state.agents[current_agent]
            if agent_state.status == AgentStatus.RUNNING:
                return "wait"

        # Determine next agent to run
        next_agent = self._get_next_agent(state)
        if next_agent:
            return next_agent

        # If no next agent, workflow is complete
        return "complete"

    def _is_workflow_complete(self, state: ScanState) -> bool:
        """
        Check if the workflow is complete.

        Args:
            state: Current scan state

        Returns:
            bool: True if workflow is complete
        """
        # Check if all agents have completed
        for step in self.workflow_steps:
            if step not in state.agents:
                return False

            agent_state = state.agents[step]
            if agent_state.status not in [AgentStatus.COMPLETED, AgentStatus.FAILED]:
                return False

        # Check if there are pending approvals
        if state.pending_approvals:
            return False

        return True

    def _get_next_agent(self, state: ScanState) -> Optional[str]:
        """
        Get the next agent to execute.

        Args:
            state: Current scan state

        Returns:
            Optional[str]: Next agent ID or None if complete
        """
        for step in self.workflow_steps:
            if step not in state.agents:
                continue

            agent_state = state.agents[step]

            # If agent is idle and prerequisites are met, run it
            if agent_state.status == AgentStatus.IDLE:
                if self._check_prerequisites(state, step):
                    return step

        return None

    def _check_prerequisites(self, state: ScanState, agent_type: str) -> bool:
        """
        Check if prerequisites are met for running an agent.

        Args:
            state: Current scan state
            agent_type: Type of agent to check

        Returns:
            bool: True if prerequisites are met
        """
        if agent_type == "crawler":
            # Crawler can always run first
            return True

        elif agent_type == "fingerprinter":
            # Requires some pages to be crawled
            return len(state.pages) > 0

        elif agent_type == "graph_rag":
            # Requires technologies to be identified
            return len(state.technologies) > 0

        elif agent_type == "exploit_planner":
            # Requires vulnerabilities to be found
            return len(state.vulnerabilities) > 0

        elif agent_type == "executor":
            # Requires approved exploit plans
            approved_plans = [
                plan for plan in state.exploit_plans
                if plan.status.value == "approved"
            ]
            return len(approved_plans) > 0

        return False

    async def _route_to_agent(self, state: ScanState, agent_type: str) -> None:
        """
        Route execution to a specific agent.

        Args:
            state: Current scan state
            agent_type: Type of agent to route to
        """
        self.log_progress(f"Routing to {agent_type} agent", 0.3)

        # Update current agent
        state.current_agent = agent_type

        # Update agent status to running
        state.update_agent_status(
            agent_id=agent_type,
            status=AgentStatus.RUNNING,
            task=f"Starting {agent_type} execution"
        )

        self.logger.info(f"Routed execution to {agent_type} agent")

    async def _handle_human_intervention(self, state: ScanState) -> None:
        """
        Handle human intervention requirements.

        Args:
            state: Current scan state
        """
        self.log_progress("Waiting for human intervention", 0.5)

        # Log intervention details
        self.logger.warning(
            f"Human intervention required: {state.intervention_message}"
        )

        # Update orchestrator status
        self.update_state(
            state,
            status=AgentStatus.WAITING,
            task="Waiting for human approval"
        )

    async def _wait_for_completion(self, state: ScanState) -> None:
        """
        Wait for current agent to complete.

        Args:
            state: Current scan state
        """
        current_agent = state.current_agent
        if current_agent:
            self.log_progress(f"Waiting for {current_agent} to complete", 0.7)

    async def _complete_workflow(self, state: ScanState) -> None:
        """
        Complete the workflow and update final state.

        Args:
            state: Current scan state
        """
        self.log_progress("Completing workflow", 0.9)

        # Update scan status
        state.status = ScanStatus.COMPLETED
        state.completed_at = datetime.utcnow()
        state.current_agent = None

        # Mark workflow as completed
        self.workflow_completed = True

        # Log summary
        summary = state.get_summary()
        self.logger.info(f"Workflow completed successfully: {summary}")

    def get_tools(self) -> List:
        """
        Get tools available to the orchestrator.

        Returns:
            List: Available tools (orchestrator typically has none)
        """
        return []

    async def get_workflow_status(self, state: ScanState) -> Dict[str, Any]:
        """
        Get detailed workflow status.

        Args:
            state: Current scan state

        Returns:
            Dict[str, Any]: Workflow status information
        """
        return {
            "workflow_started": self.workflow_started,
            "workflow_completed": self.workflow_completed,
            "current_step": self.current_step,
            "total_steps": len(self.workflow_steps),
            "current_agent": state.current_agent,
            "human_intervention_required": state.human_intervention_required,
            "intervention_message": state.intervention_message,
            "agent_statuses": {
                agent_id: {
                    "status": agent.status,
                    "progress": agent.progress,
                    "current_task": agent.current_task
                }
                for agent_id, agent in state.agents.items()
            },
            "overall_progress": self._calculate_overall_progress(state)
        }

    def _calculate_overall_progress(self, state: ScanState) -> float:
        """
        Calculate overall workflow progress.

        Args:
            state: Current scan state

        Returns:
            float: Overall progress (0.0 to 1.0)
        """
        if not state.agents:
            return 0.0

        total_progress = sum(agent.progress for agent in state.agents.values())
        return total_progress / len(state.agents)
