#!/usr/bin/env python3
"""
Live API Monitor for Cipher-Spy

Real-time monitoring of API calls while browsing pump.fun.
Shows network traffic as it happens with detailed analysis.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor
from src.utils.logging import setup_logging


class LiveAPIMonitor:
    """Real-time API monitoring system."""
    
    def __init__(self, target_url: str = "https://pump.fun"):
        self.target_url = target_url
        self.crawler = None
        self.interceptor = None
        self.api_calls = []
        self.running = False
        
    async def start_monitoring(self):
        """Start live API monitoring."""
        print("🔍 Cipher-Spy Live API Monitor")
        print("="*50)
        print(f"🎯 Target: {self.target_url}")
        print("🚀 Starting browser and network interception...")
        
        try:
            # Initialize components
            self.interceptor = NetworkInterceptor()
            self.crawler = PlaywrightCrawler(
                headless=False,  # Show browser so user can interact
                delay_ms=1000
            )
            
            # Start crawler
            await self.crawler.start()
            page = self.crawler.page
            
            # Setup enhanced network monitoring
            await self._setup_enhanced_monitoring(page)
            
            print("✅ Browser started and network monitoring active")
            print(f"🌐 Navigating to {self.target_url}...")
            
            # Navigate to target
            await page.goto(self.target_url, wait_until="networkidle")
            
            print("\n📡 LIVE API MONITORING ACTIVE")
            print("="*50)
            print("Interact with the website in the browser window.")
            print("API calls will be displayed here in real-time.")
            print("Press Ctrl+C to stop monitoring.")
            print()
            
            # Start monitoring loop
            self.running = True
            await self._monitoring_loop()
            
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped by user")
        except Exception as e:
            print(f"💥 Error: {e}")
        finally:
            await self._cleanup()
    
    async def _setup_enhanced_monitoring(self, page):
        """Setup enhanced network monitoring with real-time display."""
        
        # Setup network interceptor
        await self.interceptor.setup_page(page)
        
        # Add custom request handler for real-time display
        async def log_request(request):
            timestamp = datetime.now().strftime("%H:%M:%S")
            method = request.method
            url = request.url
            
            # Check if it's an API call
            if self._is_likely_api_call(url, request.headers):
                print(f"🎯 [{timestamp}] API REQUEST: {method} {url}")
                
                # Store for analysis
                self.api_calls.append({
                    'timestamp': timestamp,
                    'method': method,
                    'url': url,
                    'headers': dict(request.headers),
                    'type': 'request'
                })
            else:
                print(f"📄 [{timestamp}] {method} {self._shorten_url(url)}")
        
        async def log_response(response):
            timestamp = datetime.now().strftime("%H:%M:%S")
            status = response.status
            url = response.url
            
            # Check if it's an API response
            if self._is_likely_api_call(url, response.headers):
                content_type = response.headers.get('content-type', '')
                print(f"📡 [{timestamp}] API RESPONSE: {status} {url}")
                print(f"    Content-Type: {content_type}")
                
                # Try to get response body for JSON APIs
                if 'application/json' in content_type.lower():
                    try:
                        body = await response.text()
                        if len(body) < 500:
                            print(f"    Body: {body}")
                        else:
                            print(f"    Body: {body[:200]}... ({len(body)} chars)")
                    except:
                        print("    Body: [Could not read]")
                
                # Store for analysis
                self.api_calls.append({
                    'timestamp': timestamp,
                    'status': status,
                    'url': url,
                    'headers': dict(response.headers),
                    'content_type': content_type,
                    'type': 'response'
                })
                
                print()  # Add spacing after API responses
        
        # Attach event listeners
        page.on("request", log_request)
        page.on("response", log_response)
    
    def _is_likely_api_call(self, url: str, headers: dict) -> bool:
        """Determine if this looks like an API call."""
        url_lower = url.lower()
        
        # Common API indicators
        api_indicators = [
            '/api/', '/v1/', '/v2/', '/v3/', '/graphql', '/rest/',
            'api.', '.json', '/json', '/data/', '/fetch',
            '/ajax', '/xhr', '/rpc'
        ]
        
        for indicator in api_indicators:
            if indicator in url_lower:
                return True
        
        # Check content type
        content_type = headers.get('content-type', '').lower()
        if 'application/json' in content_type or 'application/xml' in content_type:
            return True
        
        # Check accept header
        accept = headers.get('accept', '').lower()
        if 'application/json' in accept:
            return True
        
        # Check for AJAX
        if headers.get('x-requested-with') == 'XMLHttpRequest':
            return True
        
        # Skip obvious static assets
        static_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.ttf', '.mp4', '.webm']
        if any(url_lower.endswith(ext) for ext in static_extensions):
            return False
        
        # If it has query parameters and isn't a static asset, might be API
        if '?' in url and not any(url_lower.endswith(ext) for ext in static_extensions):
            return True
        
        return False
    
    def _shorten_url(self, url: str, max_length: int = 80) -> str:
        """Shorten URL for display."""
        if len(url) <= max_length:
            return url
        
        parsed = urlparse(url)
        path = parsed.path
        if len(path) > max_length - 20:
            path = path[:max_length-23] + "..."
        
        return f"{parsed.netloc}{path}"
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        try:
            while self.running:
                await asyncio.sleep(1)
                
                # Periodically show summary
                if len(self.api_calls) > 0 and len(self.api_calls) % 10 == 0:
                    await self._show_summary()
        
        except KeyboardInterrupt:
            self.running = False
    
    async def _show_summary(self):
        """Show summary of captured API calls."""
        if not self.api_calls:
            return
        
        print("\n" + "="*50)
        print(f"📊 SUMMARY: {len(self.api_calls)} API calls captured")
        
        # Group by endpoint
        endpoints = {}
        for call in self.api_calls:
            if call['type'] == 'request':
                url = call['url']
                method = call['method']
                key = f"{method} {url}"
                endpoints[key] = endpoints.get(key, 0) + 1
        
        print("🔗 Unique API Endpoints:")
        for endpoint, count in sorted(endpoints.items(), key=lambda x: x[1], reverse=True):
            print(f"  • {endpoint} ({count}x)")
        
        print("="*50 + "\n")
    
    async def _cleanup(self):
        """Cleanup resources."""
        try:
            if self.crawler:
                await self.crawler.stop()
            
            # Save captured data
            if self.api_calls:
                await self._save_captured_data()
        
        except Exception as e:
            print(f"Error during cleanup: {e}")
    
    async def _save_captured_data(self):
        """Save captured API calls to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"api_calls_captured_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.api_calls, f, indent=2, default=str)
        
        print(f"\n💾 Captured API calls saved to: {filename}")
        
        # Generate summary report
        await self._generate_summary_report(timestamp)
    
    async def _generate_summary_report(self, timestamp: str):
        """Generate a summary report of captured API calls."""
        if not self.api_calls:
            return
        
        # Analyze captured data
        requests = [call for call in self.api_calls if call['type'] == 'request']
        responses = [call for call in self.api_calls if call['type'] == 'response']
        
        # Group by endpoint
        endpoints = {}
        for call in requests:
            url = call['url']
            method = call['method']
            key = f"{method} {url}"
            if key not in endpoints:
                endpoints[key] = {
                    'method': method,
                    'url': url,
                    'count': 0,
                    'headers': call['headers']
                }
            endpoints[key]['count'] += 1
        
        # Generate markdown report
        report = []
        report.append("# API Monitoring Report")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Target: {self.target_url}")
        report.append("")
        report.append(f"## Summary")
        report.append(f"- Total API calls: {len(self.api_calls)}")
        report.append(f"- Unique endpoints: {len(endpoints)}")
        report.append(f"- Requests: {len(requests)}")
        report.append(f"- Responses: {len(responses)}")
        report.append("")
        report.append("## Discovered Endpoints")
        report.append("")
        
        for endpoint_key, endpoint_data in sorted(endpoints.items(), key=lambda x: x[1]['count'], reverse=True):
            report.append(f"### {endpoint_key}")
            report.append(f"- Called {endpoint_data['count']} times")
            report.append(f"- URL: {endpoint_data['url']}")
            report.append("")
        
        # Save report
        report_filename = f"api_monitoring_report_{timestamp}.md"
        with open(report_filename, 'w') as f:
            f.write('\n'.join(report))
        
        print(f"📄 Summary report saved to: {report_filename}")


async def main():
    """Main entry point."""
    setup_logging(level="INFO", environment="development")
    
    print("🔍 Cipher-Spy Live API Monitor")
    print("Real-time API call monitoring and analysis")
    print()
    
    # Get target URL
    target = input("Enter target URL (or press Enter for pump.fun): ").strip()
    if not target:
        target = "https://pump.fun"
    
    monitor = LiveAPIMonitor(target)
    await monitor.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main())
