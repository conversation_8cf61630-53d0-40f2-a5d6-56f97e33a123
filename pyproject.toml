[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "cipher-spy"
version = "1.0.0"
description = "AI-Driven Red Team Swarm for Penetration Testing"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Cipher-Spy Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Cipher-Spy Development Team", email = "<EMAIL>"}
]
keywords = [
    "penetration-testing",
    "security",
    "ai",
    "red-team",
    "vulnerability-assessment",
    "web-security",
    "automation"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Security",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Software Development :: Testing",
]
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "langchain>=0.0.350",
    "langgraph>=0.0.20",
    "playwright>=1.40.0",
    "sqlalchemy>=2.0.23",
    "asyncpg>=0.29.0",
    "neo4j>=5.15.0",
    "chromadb>=0.4.18",
    "sentence-transformers>=2.2.2",
    "python-Wappalyzer>=0.3.1",
    "wafw00f>=2.2.0",
    "requests>=2.31.0",
    "httpx>=0.25.2",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "python-dotenv>=1.0.0",
    "click>=8.1.7",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
    "factory-boy>=3.3.0",
]
graphiti = [
    "graphiti-core>=0.3.0",
    "openai>=1.0.0",
    "tiktoken>=0.5.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
]
docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocstrings[python]>=0.24.0",
]
security = [
    "bandit>=1.7.5",
    "safety>=2.3.5",
    "semgrep>=1.45.0",
]

[project.urls]
Homepage = "https://github.com/cipher-spy/cipher-spy"
Documentation = "https://cipher-spy.readthedocs.io"
Repository = "https://github.com/cipher-spy/cipher-spy"
"Bug Tracker" = "https://github.com/cipher-spy/cipher-spy/issues"
Changelog = "https://github.com/cipher-spy/cipher-spy/blob/main/CHANGELOG.md"

[project.scripts]
cipher-spy = "src.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.yml", "*.yaml", "*.json"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = [
    "fastapi",
    "pydantic",
    "sqlalchemy",
    "langchain",
    "playwright",
    "neo4j",
    "pytest"
]

# MyPy configuration
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "wappalyzer.*",
    "wafw00f.*",
    "graphiti.*",
    "chromadb.*",
    "sentence_transformers.*",
    "playwright.*",
    "neo4j.*"
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "security: marks tests as security-related",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linting
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection for tests

# Flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# See setup.cfg for flake8 configuration
