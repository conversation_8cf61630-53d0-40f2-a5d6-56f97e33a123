"""
Agent module for Cipher-Spy.

Contains all AI agents that form the red team swarm. Each agent is specialized
for a specific phase of the penetration testing workflow:

- Orchestrator: Coordinates the overall workflow
- Crawler: Performs web reconnaissance and mapping
- Fingerprinter: Identifies technologies and services
- Graph-RAG: Queries exploit knowledge base
- Exploit Planner: Generates exploit strategies
- Executor: Executes approved exploits

All agents inherit from BaseAgent and implement the LangChain agent interface.
"""

from .base_agent import BaseAgent
from .orchestrator import OrchestratorAgent
from .crawler import CrawlerAgent
# TODO: Import other agents when implemented
# from .fingerprinter import FingerprintingAgent
# from .graph_rag import GraphRAGAgent
# from .exploit_planner import ExploitPlannerAgent
# from .executor import ExecutorAgent

__all__ = [
    "BaseAgent",
    "OrchestratorAgent",
    "CrawlerAgent",
    # TODO: Add other agents when implemented
    # "FingerprintingAgent",
    # "GraphRAGAgent",
    # "ExploitPlannerAgent",
    # "ExecutorAgent"
]
