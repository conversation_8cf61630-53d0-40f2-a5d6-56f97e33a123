#!/usr/bin/env python3
"""
Target Configuration System for Cipher-Spy Universal API Discovery

Defines configuration classes for target websites, API endpoints, and
discovery parameters. This system allows the framework to be easily
adapted to any website by providing appropriate configuration.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Set
from enum import Enum
from urllib.parse import urlparse


class APIType(Enum):
    """Types of APIs that can be discovered."""
    REST = "rest"
    GRAPHQL = "graphql"
    WEBSOCKET = "websocket"
    RPC = "rpc"
    UNKNOWN = "unknown"


class AuthenticationType(Enum):
    """Types of authentication methods."""
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH = "oauth"
    COOKIE = "cookie"
    CUSTOM = "custom"


@dataclass
class APIEndpointConfig:
    """Configuration for a specific API endpoint."""
    name: str
    url: str
    method: str = "GET"
    api_type: APIType = APIType.REST
    description: str = ""
    business_value: str = ""
    known_parameters: List[str] = field(default_factory=list)
    test_parameters: List[Dict[str, Any]] = field(default_factory=list)
    expected_response_fields: List[str] = field(default_factory=list)
    rate_limit: Optional[float] = None
    requires_auth: bool = False
    auth_type: AuthenticationType = AuthenticationType.NONE
    custom_headers: Dict[str, str] = field(default_factory=dict)


@dataclass
class NavigationConfig:
    """Configuration for website navigation patterns."""
    start_urls: List[str] = field(default_factory=list)
    interactive_selectors: List[str] = field(default_factory=list)
    form_selectors: List[str] = field(default_factory=list)
    navigation_patterns: List[str] = field(default_factory=list)
    spa_mode: bool = False
    wait_for_js: bool = True
    max_depth: int = 3
    follow_external_links: bool = False


@dataclass
class ParameterDiscoveryConfig:
    """Configuration for parameter discovery strategies."""
    parameter_categories: Dict[str, List[str]] = field(default_factory=dict)
    test_values: Dict[str, List[Any]] = field(default_factory=dict)
    fuzzing_enabled: bool = True
    combination_testing: bool = True
    max_parameters_per_test: int = 5
    timeout_seconds: float = 10.0
    retry_attempts: int = 3


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting and request management."""
    requests_per_second: float = 1.0
    burst_limit: int = 5
    backoff_strategy: str = "exponential"  # linear, exponential, fixed
    max_retry_delay: float = 60.0
    respect_retry_after: bool = True


@dataclass
class TargetConfig:
    """
    Complete configuration for a target website.
    
    This class defines all the parameters needed to configure the
    universal API discovery framework for a specific target website.
    """
    
    # Basic target information
    domain: str
    name: str
    description: str = ""
    base_urls: List[str] = field(default_factory=list)
    
    # Known API endpoints
    known_endpoints: List[APIEndpointConfig] = field(default_factory=list)
    
    # Discovery configuration
    navigation_config: NavigationConfig = field(default_factory=NavigationConfig)
    parameter_config: ParameterDiscoveryConfig = field(default_factory=ParameterDiscoveryConfig)
    rate_limit_config: RateLimitConfig = field(default_factory=RateLimitConfig)
    
    # Request configuration
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    custom_headers: Dict[str, str] = field(default_factory=dict)
    cookies: Dict[str, str] = field(default_factory=dict)
    
    # Authentication
    auth_type: AuthenticationType = AuthenticationType.NONE
    auth_config: Dict[str, Any] = field(default_factory=dict)
    
    # Scope and filtering
    allowed_domains: Set[str] = field(default_factory=set)
    blocked_patterns: List[str] = field(default_factory=list)
    api_path_patterns: List[str] = field(default_factory=list)
    
    # Analysis configuration
    business_context: Dict[str, Any] = field(default_factory=dict)
    competitive_intelligence: bool = True
    deep_analysis: bool = True
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Ensure domain is properly formatted
        if not self.domain:
            raise ValueError("Domain is required")
        
        # Parse domain from URL if needed
        if self.domain.startswith(('http://', 'https://')):
            parsed = urlparse(self.domain)
            self.domain = parsed.netloc
        
        # Set default base URLs if not provided
        if not self.base_urls:
            self.base_urls = [f"https://{self.domain}"]
        
        # Add domain to allowed domains
        if not self.allowed_domains:
            self.allowed_domains = {self.domain}
        else:
            self.allowed_domains.add(self.domain)
        
        # Set default API path patterns if not provided
        if not self.api_path_patterns:
            self.api_path_patterns = [
                r'/api/',
                r'/v\d+/',
                r'\.json$',
                r'/graphql',
                r'/rest/',
                r'/endpoints?/',
                r'/services?/'
            ]

    @classmethod
    def create_pump_fun_config(cls) -> 'TargetConfig':
        """Create a configuration for pump.fun (example implementation)."""
        return cls(
            domain="pump.fun",
            name="Pump.fun Cryptocurrency Platform",
            description="Decentralized cryptocurrency trading platform",
            base_urls=["https://pump.fun", "https://advanced-api-v2.pump.fun"],
            known_endpoints=[
                APIEndpointConfig(
                    name="advanced_coin_listing",
                    url="https://advanced-api-v2.pump.fun/coins/list",
                    method="GET",
                    business_value="CRITICAL - Advanced coin discovery engine",
                    known_parameters=["sortBy", "limit", "offset"],
                    test_parameters=[
                        {"sortBy": "creationTime", "limit": 50},
                        {"sortBy": "marketCap", "limit": 20},
                        {"sortBy": "volume", "limit": 100}
                    ]
                ),
                APIEndpointConfig(
                    name="graduated_coins",
                    url="https://advanced-api-v2.pump.fun/coins/graduated",
                    method="GET",
                    business_value="CRITICAL - Graduation intelligence",
                    known_parameters=["sortBy", "limit", "offset"]
                )
            ],
            navigation_config=NavigationConfig(
                start_urls=["https://pump.fun/advanced/coin?scan=true"],
                spa_mode=True,
                wait_for_js=True,
                max_depth=3
            ),
            parameter_config=ParameterDiscoveryConfig(
                parameter_categories={
                    'filtering': [
                        'minMarketCap', 'maxMarketCap', 'minVolume', 'maxVolume',
                        'minHolders', 'maxHolders', 'minAge', 'maxAge'
                    ],
                    'sorting': ['sortBy', 'orderBy', 'direction'],
                    'pagination': ['limit', 'offset', 'page'],
                    'search': ['search', 'query', 'symbol', 'name']
                },
                test_values={
                    'numeric': [1, 10, 50, 100, 500, 1000],
                    'string': ['test', 'pump', 'meme', 'coin'],
                    'sort_options': ['creationTime', 'marketCap', 'volume']
                }
            ),
            custom_headers={
                'Origin': 'https://pump.fun',
                'Referer': 'https://pump.fun/advanced/coin?scan=true'
            },
            business_context={
                'industry': 'cryptocurrency',
                'platform_type': 'trading',
                'key_metrics': ['market_cap', 'volume', 'holders', 'graduation_rate']
            }
        )

    @classmethod
    def create_generic_config(cls, domain: str, name: str = None) -> 'TargetConfig':
        """Create a generic configuration for any website."""
        return cls(
            domain=domain,
            name=name or f"Generic API Discovery for {domain}",
            description=f"Automated API discovery for {domain}",
            navigation_config=NavigationConfig(
                start_urls=[f"https://{domain}"],
                interactive_selectors=[
                    'button', 'a[href]', 'input[type="submit"]',
                    '[role="button"]', '.btn', '.button'
                ],
                form_selectors=['form', '[role="form"]'],
                spa_mode=False,
                wait_for_js=True,
                max_depth=2
            ),
            parameter_config=ParameterDiscoveryConfig(
                parameter_categories={
                    'pagination': ['page', 'limit', 'offset', 'size', 'count'],
                    'sorting': ['sort', 'order', 'sortBy', 'orderBy'],
                    'filtering': ['filter', 'search', 'query', 'q'],
                    'format': ['format', 'type', 'output']
                },
                test_values={
                    'numeric': [1, 10, 25, 50, 100],
                    'string': ['test', 'example', 'sample'],
                    'boolean': [True, False, 'true', 'false']
                }
            ),
            business_context={
                'discovery_mode': 'generic',
                'analysis_depth': 'standard'
            }
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'domain': self.domain,
            'name': self.name,
            'description': self.description,
            'base_urls': self.base_urls,
            'known_endpoints': [
                {
                    'name': ep.name,
                    'url': ep.url,
                    'method': ep.method,
                    'business_value': ep.business_value,
                    'known_parameters': ep.known_parameters
                }
                for ep in self.known_endpoints
            ],
            'navigation_config': {
                'start_urls': self.navigation_config.start_urls,
                'spa_mode': self.navigation_config.spa_mode,
                'max_depth': self.navigation_config.max_depth
            },
            'rate_limit_config': {
                'requests_per_second': self.rate_limit_config.requests_per_second,
                'burst_limit': self.rate_limit_config.burst_limit
            },
            'business_context': self.business_context
        }

    @property
    def rate_limit_delay(self) -> float:
        """Get the delay between requests based on rate limit configuration."""
        return 1.0 / self.rate_limit_config.requests_per_second
