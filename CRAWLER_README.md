# Cipher-Spy Autonomous Web Reconnaissance Agent

This document describes the implementation of the **Autonomous Web Reconnaissance and API Discovery Agent** - the first core module of the Cipher-Spy project.

## 🎯 Overview

The crawler agent is a LangGraph-orchestrated autonomous system that intelligently navigates websites, interacts with page elements, and comprehensively reverse-engineers API traffic to generate detailed reconnaissance reports.

## 🏗️ Architecture

### Core Components

1. **CrawlerAgent** (`src/agents/crawler.py`)
   - Main orchestration agent
   - Coordinates all crawling activities
   - Manages state and progress tracking

2. **PlaywrightCrawler** (`src/crawling/playwright_crawler.py`)
   - Browser automation using Playwright
   - Intelligent DOM interaction
   - Screenshot capture and page analysis

3. **NetworkInterceptor** (`src/crawling/network_interceptor.py`)
   - Complete HTTP traffic capture
   - API endpoint discovery and analysis
   - Response schema inference

4. **FormHandler** (`src/crawling/form_handler.py`)
   - Automatic form detection and interaction
   - Safe form filling with test data
   - Authentication flow handling

5. **ScopeManager** (`src/crawling/scope_manager.py`)
   - Domain boundary enforcement
   - Robots.txt compliance
   - URL filtering and validation

6. **Workflow** (`src/core/workflow.py`)
   - LangGraph-based orchestration
   - State management and transitions
   - Agent coordination

## 🚀 Features

### Autonomous Navigation
- **Intelligent Element Interaction**: Automatically clicks buttons, fills forms, navigates menus
- **Modal/Popup Handling**: Detects and handles dynamic content
- **DOM Heuristics**: Uses smart selectors to find interactive elements
- **Loop Prevention**: Tracks visited states to avoid infinite loops

### Network Traffic Analysis
- **Complete Request/Response Capture**: Records all HTTP traffic
- **API Endpoint Discovery**: Identifies REST APIs, GraphQL endpoints, and AJAX calls
- **Authentication Flow Detection**: Captures login sequences and token usage
- **Schema Inference**: Automatically generates JSON schemas from responses

### Intelligent Form Handling
- **Automatic Form Detection**: Finds and analyzes all forms on pages
- **Safe Test Data**: Uses appropriate test values for different field types
- **Authentication Support**: Handles login forms with provided credentials
- **Submission Safety**: Only submits forms that are safe for testing

### Scope Management
- **Domain Boundaries**: Enforces crawling within specified domains
- **Robots.txt Compliance**: Respects website crawling policies
- **Pattern Filtering**: Excludes dangerous or irrelevant URLs
- **Rate Limiting**: Implements respectful crawling delays

## 📊 Output Capabilities

### Discovered Data
- **Page Inventory**: Complete list of discovered pages with metadata
- **API Endpoint Catalog**: Comprehensive API documentation with schemas
- **Form Analysis**: Detailed form structure and field analysis
- **Technology Detection**: Identified frameworks and libraries
- **Screenshot Archive**: Visual documentation of pages

### Generated Reports
- **API Documentation**: Pydantic models and OpenAPI specifications
- **Authentication Flows**: Step-by-step login and token handling
- **Interactive Elements**: Mapping of buttons/links to API calls
- **Test Harnesses**: Replayable request collections

## 🛠️ Installation & Setup

### Prerequisites
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers
python setup_playwright.py
```

### Configuration
The crawler can be configured through environment variables or the scan configuration:

```python
config = {
    "max_crawl_depth": 5,
    "max_pages_per_domain": 1000,
    "crawl_delay_ms": 1000,
    "respect_robots_txt": True,
    "safe_mode": True,
    "headless": True
}
```

## 🧪 Testing

### Quick Test
```bash
# Test the crawler on pump.fun
python test_crawler.py
```

### Component Tests
```python
# Test individual components
from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor

# Initialize and test components
crawler = PlaywrightCrawler()
interceptor = NetworkInterceptor()
```

## 📝 Usage Examples

### Basic Crawling
```python
from src.core.state import ScanState, TargetInfo
from src.core.workflow import create_workflow

# Create target
target = TargetInfo(
    url="https://example.com",
    domain="example.com",
    scope=["example.com", "*.example.com"]
)

# Create scan state
state = ScanState(target=target)

# Execute workflow
workflow = create_workflow()
result = await workflow.execute(state)

# Access results
pages = result.pages
endpoints = result.endpoints
```

### Advanced Configuration
```python
# Custom crawler configuration
workflow_config = {
    "crawler": {
        "max_crawl_depth": 3,
        "max_pages_per_domain": 50,
        "crawl_delay_ms": 2000,
        "headless": False,  # Show browser for debugging
        "user_agent": "Custom-Agent/1.0"
    }
}

workflow = create_workflow(workflow_config)
```

### Authentication
```python
# Target with credentials
target = TargetInfo(
    url="https://app.example.com",
    domain="app.example.com",
    credentials={
        "username": "testuser",
        "password": "testpass"
    }
)
```

## 🔒 Security Considerations

### Safe Mode
- **Default Enabled**: Prevents destructive actions
- **Form Submission Filtering**: Only submits safe forms
- **URL Pattern Exclusion**: Avoids logout, delete, and payment URLs

### Scope Enforcement
- **Domain Boundaries**: Strict enforcement of allowed domains
- **Robots.txt Respect**: Honors website crawling policies
- **Rate Limiting**: Prevents server overload

### Data Privacy
- **Local Processing**: All data stays on your infrastructure
- **Credential Handling**: Secure storage and transmission
- **Screenshot Management**: Organized local storage

## 📈 Performance Metrics

The crawler tracks comprehensive metrics:

- **Pages Discovered**: Total number of unique pages found
- **Endpoints Discovered**: API endpoints with full documentation
- **Forms Analyzed**: Interactive forms with field mapping
- **Interactions Performed**: Button clicks, form submissions, etc.
- **Network Requests**: Complete HTTP traffic analysis
- **Crawl Duration**: Time taken for complete reconnaissance

## 🔧 Troubleshooting

### Common Issues

1. **Playwright Installation**
   ```bash
   # Reinstall browsers
   python -m playwright install --force
   ```

2. **Permission Errors**
   ```bash
   # Install system dependencies
   python -m playwright install-deps
   ```

3. **Network Timeouts**
   - Increase `crawl_delay_ms` in configuration
   - Check target website availability

4. **Scope Issues**
   - Verify domain configuration
   - Check robots.txt compliance

### Debug Mode
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run with visible browser
config = {"crawler": {"headless": False}}
```

## 🚀 Next Steps

This crawler implementation provides the foundation for the complete Cipher-Spy system. Future enhancements will include:

1. **Technology Fingerprinting**: Wappalyzer and WafW00f integration
2. **Knowledge Graph Analysis**: Exploit-DB and CVE matching
3. **LLM-Powered Analysis**: Intelligent vulnerability assessment
4. **Exploit Planning**: Automated exploit generation
5. **Human-in-the-Loop**: Approval workflows for testing

## 📚 API Reference

### CrawlerAgent Methods
- `execute(state)`: Main crawling execution
- `get_crawl_statistics()`: Detailed crawling metrics

### PlaywrightCrawler Methods
- `start()`: Initialize browser
- `crawl_page(url)`: Crawl single page
- `stop()`: Cleanup resources

### NetworkInterceptor Methods
- `setup_page(page)`: Setup traffic interception
- `get_discovered_endpoints()`: Get API endpoints
- `get_statistics()`: Network traffic stats

### ScopeManager Methods
- `is_in_scope(url)`: Check URL scope
- `filter_urls(urls)`: Filter URL list
- `get_scope_statistics()`: Scope metrics

---

**Cipher-Spy Crawler** - Autonomous reconnaissance for ethical security testing.
