# Pump.fun Advanced API Usage Guide

## Quick Start

### Python Example

```python
import requests
import time

class PumpAPIClient:
    def __init__(self):
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://pump.fun',
            'Referer': 'https://pump.fun/advanced/coin?scan=true'
        }

    def get_latest_coins(self, limit=30):
        url = 'https://advanced-api-v2.pump.fun/coins/list'
        params = {'sortBy': 'creationTime', 'limit': limit}

        response = requests.get(url, params=params, headers=self.base_headers)
        return response.json()

    def get_top_volume_coins(self, limit=30):
        url = 'https://advanced-api-v2.pump.fun/coins/list'
        params = {'sortBy': 'volume', 'limit': limit}

        response = requests.get(url, params=params, headers=self.base_headers)
        return response.json()

    def get_graduated_coins(self, limit=30):
        url = 'https://advanced-api-v2.pump.fun/coins/graduated'
        params = {'limit': limit}

        response = requests.get(url, params=params, headers=self.base_headers)
        return response.json()

# Usage
client = PumpAPIClient()

# Get latest coins
latest = client.get_latest_coins(50)
print(f"Found {len(latest.get('coins', []))} latest coins")

# Get high volume coins
volume_coins = client.get_top_volume_coins(50)
print(f"Found {len(volume_coins.get('coins', []))} high volume coins")

# Get graduated coins
graduated = client.get_graduated_coins(100)
print(f"Found {len(graduated.get('coins', []))} graduated coins")
```

## Best Practices

1. **Rate Limiting**: Implement delays between requests
2. **Error Handling**: Handle HTTP errors and timeouts
3. **Caching**: Cache responses to reduce API calls
4. **Monitoring**: Track API performance and availability

## Investment Strategies

### Early Discovery
- Monitor `sortBy=creationTime` for newest coins
- Filter by creation time (last 24-48 hours)
- Analyze volume and holder growth

### Graduation Tracking
- Compare regular coins with graduated thresholds
- Identify coins approaching graduation criteria
- Monitor graduation announcements

### Volume Analysis
- Track `sortBy=volume` for momentum plays
- Identify sudden volume spikes
- Correlate volume with price movements

---

Generated by Cipher-Spy Deep API Analysis System
