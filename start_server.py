#!/usr/bin/env python3
"""
Cipher-Spy Server Startup Script

Simple script to start the Cipher-Spy backend server with proper error handling
and environment setup.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    print("📦 Checking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install -r requirements.txt")
        return False
    
    return True


def setup_environment():
    """Setup environment variables."""
    print("⚙️ Setting up environment...")
    
    # Set default environment variables if not present
    env_vars = {
        'CIPHER_SPY_ENV': 'development',
        'CIPHER_SPY_LOG_LEVEL': 'INFO',
        'CIPHER_SPY_CORS_ENABLED': 'true',
        'CIPHER_SPY_CORS_ORIGINS': '["http://localhost:3000", "chrome-extension://*"]'
    }
    
    for key, value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"   ✅ Set {key}={value}")
        else:
            print(f"   ✅ {key} already set")


def start_server():
    """Start the Cipher-Spy server."""
    print("\n🚀 Starting Cipher-Spy Server...")
    print("="*50)
    
    try:
        # Run the server
        result = subprocess.run([
            sys.executable, "-m", "src.main", "--mode", "server"
        ], cwd=Path.cwd())
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n⏹️ Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Failed to start server: {e}")
        return 1


def main():
    """Main startup function."""
    print("🔍 Cipher-Spy Backend Server")
    print("="*30)
    
    # Check requirements
    if not check_python_version():
        return 1
    
    if not check_dependencies():
        return 1
    
    # Setup environment
    setup_environment()
    
    # Start server
    return start_server()


if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⏹️ Startup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Startup failed: {e}")
        sys.exit(1)
