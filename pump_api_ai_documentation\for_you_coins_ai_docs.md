# For You Coins API

## Overview
The For You Coins endpoint provides personalized cryptocurrency recommendations based on user preferences and platform activity. This endpoint returns a curated list of coins with detailed metadata, market metrics, and social information, enabling developers to build personalized discovery experiences.

## Endpoint Information
- **URL**: `https://frontend-api-v3.pump.fun/coins/for-you`
- **Method**: GET
- **Content-Type**: application/json
- **Average Response Time**: ~900ms

## Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| offset | integer | No | 0 | Number of items to skip for pagination |
| limit | integer | No | 48 | Maximum number of items to return (1-100) |
| includeNsfw | boolean | No | false | Whether to include NSFW-flagged coins |

## Response Format
Returns an array of coin objects with detailed metadata and market information.

### Sample Response
```json
{
  "mint": "6HT7WYHPntjD5ieo7wXTYfRHuzUMBRR3pBQzDx18pump",
  "name": "Jesi",
  "symbol": "Jesi",
  "market_cap": 31.158153858,
  "usd_market_cap": 4957.88544188496,
  "virtual_sol_reserves": 31669874846,
  "total_supply": 1000000000000000
  // ... additional fields
}
```

## Code Examples

### Python Example
```python
import requests

def get_recommended_coins(offset=0, limit=48, include_nsfw=False):
    url = "https://frontend-api-v3.pump.fun/coins/for-you"
    
    params = {
        "offset": offset,
        "limit": limit,
        "includeNsfw": str(include_nsfw).lower()
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching recommendations: {e}")
        return None
```

### cURL Example
```bash
curl -X GET "https://frontend-api-v3.pump.fun/coins/for-you?offset=0&limit=48&includeNsfw=false" \
  -H "Accept: application/json"
```

### JavaScript Example
```javascript
async function getRecommendedCoins(offset = 0, limit = 48, includeNsfw = false) {
  const url = new URL('https://frontend-api-v3.pump.fun/coins/for-you');
  url.searchParams.append('offset', offset);
  url.searchParams.append('limit', limit);
  url.searchParams.append('includeNsfw', includeNsfw);

  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    throw error;
  }
}
```

## Response Fields Reference

| Field | Type | Description |
|-------|------|-------------|
| mint | string | Unique identifier for the coin |
| name | string | Display name of the coin |
| symbol | string | Trading symbol |
| description | string | Coin description |
| market_cap | number | Market capitalization in SOL |
| usd_market_cap | number | Market capitalization in USD |
| virtual_sol_reserves | number | Current SOL reserves |
| total_supply | number | Total token supply |
| is_currently_live | boolean | Trading status |
| created_timestamp | number | Creation timestamp (ms) |

## Use Cases
1. **Discovery Feed**: Build a personalized coin discovery interface
2. **Market Analysis**: Track new and trending coins
3. **Portfolio Recommendations**: Suggest diversification opportunities
4. **Market Monitoring**: Track market caps and trading activity

## Error Handling

| Status Code | Description | Resolution |
|-------------|-------------|------------|
| 400 | Invalid parameters | Check parameter values and types |
| 429 | Rate limit exceeded | Implement exponential backoff |
| 500 | Server error | Retry with backoff strategy |

## Rate Limiting & Best Practices
- Implement caching for responses (recommended TTL: 5 minutes)
- Limit requests to 100 per minute per IP
- Use pagination for large result sets
- Handle rate limiting with exponential backoff

## Integration Tips
1. Implement error handling with retries
2. Cache responses to reduce API load
3. Use pagination for infinite scroll implementations
4. Monitor response times and implement timeouts
5. Handle network errors gracefully

## Performance Considerations
- Average response time: 902ms
- Response size: ~53KB
- Consider implementing response compression
- Use appropriate timeout values (recommended: 3000ms)

This endpoint is designed for building responsive, personalized cryptocurrency discovery experiences. For optimal performance, implement caching and proper error handling in your integration.