# Pump.fun Advanced Scanner API Documentation

## Overview
The Pump.fun Advanced Scanner API provides a comprehensive suite of endpoints for cryptocurrency discovery, analysis, and monitoring. This API ecosystem enables real-time tracking of coin metrics, market data, and social signals across multiple blockchain networks.

## Authentication & Access
Currently, all documented endpoints are publicly accessible without authentication requirements. However, implementing rate limiting best practices is recommended for production use.

## Core Endpoints

### Advanced Coin Listing API
```http
GET https://advanced-api-v2.pump.fun/coins/list
```

Provides detailed market data and metrics for cryptocurrency tokens.

**Parameters:**
- `sortBy`: Sorting criteria (e.g., "marketCap", "volume", "holders")
- `limit`: Number of results per page (default: 30)
- `offset`: Pagination offset

**Response Schema:**
```json
{
  "coins": [{
    "coinMint": "string",
    "dev": "string",
    "name": "string",
    "ticker": "string",
    "imageUrl": "string",
    "creationTime": "timestamp",
    "numHolders": "number",
    "marketCap": "number",
    "volume": "number",
    "currentMarketPrice": "number"
  }],
  "pagination": {
    "lastScore": "number",
    "hasMore": "boolean"
  }
}
```

### Graduated Coins API
```http
GET https://advanced-api-v2.pump.fun/coins/graduated
```

Tracks tokens that have met specific market criteria for graduation.

**Parameters:**
- Identical to Advanced Coin Listing API
- Additional graduation-specific filters available

### Feature Flags API
```http
GET https://pump.fun/api/flags
```

Controls feature availability and platform capabilities.

**Response Schema:**
```json
{
  "trending_carousel_enabled": "boolean",
  "semantic_search_enabled": "boolean",
  "similar_coins_enabled": "boolean",
  "trade_history_recs_enabled": "boolean",
  "multi_column_advanced_enabled": "boolean",
  "hybrid_search_enabled": "boolean",
  "search_ranked_enabled": "boolean",
  "homepage_v2_enabled": "boolean",
  "livestreams_enabled": "boolean",
  "create_coin_v2_enabled": "boolean"
}
```

## Integration Examples

### Python Client Implementation
```python
import requests
import json

class PumpFunClient:
    def __init__(self):
        self.base_url = "https://advanced-api-v2.pump.fun"
        
    def get_coin_list(self, sort_by="marketCap", limit=30, offset=0):
        endpoint = f"{self.base_url}/coins/list"
        params = {
            "sortBy": sort_by,
            "limit": limit,
            "offset": offset
        }
        
        response = requests.get(endpoint, params=params)
        return response.json()
        
    def get_graduated_coins(self, sort_by="marketCap", limit=30):
        endpoint = f"{self.base_url}/coins/graduated"
        params = {
            "sortBy": sort_by,
            "limit": limit
        }
        
        response = requests.get(endpoint, params=params)
        return response.json()
        
    def get_feature_flags(self):
        response = requests.get("https://pump.fun/api/flags")
        return response.json()
```

## Rate Limiting & Best Practices

1. Implement exponential backoff for retry logic
2. Cache responses when possible
3. Limit concurrent requests
4. Monitor response times and adjust accordingly

```python
from tenacity import retry, stop_after_attempt, wait_exponential

class RateLimitedClient(PumpFunClient):
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def get_coin_list(self, *args, **kwargs):
        return super().get_coin_list(*args, **kwargs)
```

## Error Handling

Implement comprehensive error handling for common scenarios:

```python
class PumpFunError(Exception):
    pass

class PumpFunClient:
    def _handle_response(self, response):
        if response.status_code == 429:
            raise PumpFunError("Rate limit exceeded")
        elif response.status_code >= 500:
            raise PumpFunError("Server error")
        elif response.status_code >= 400:
            raise PumpFunError(f"Client error: {response.status_code}")
            
        return response.json()
```

## Business Use Cases

1. Market Analysis Tools
   - Real-time token discovery
   - Market trend analysis
   - Graduation tracking

2. Trading Systems
   - Price monitoring
   - Volume analysis
   - Holder metrics tracking

3. Portfolio Management
   - Token metrics aggregation
   - Performance tracking
   - Market cap monitoring

## Response Schemas
All endpoints return JSON responses with consistent pagination patterns:

```typescript
interface PaginatedResponse {
  pagination: {
    lastScore: number;
    hasMore: boolean;
  };
}

interface CoinData {
  coinMint: string;
  dev: string;
  name: string;
  ticker: string;
  imageUrl: string;
  creationTime: number;
  numHolders: number;
  marketCap: number;
  volume: number;
  currentMarketPrice: number;
}
```

This documentation provides a foundation for integrating with the Pump.fun Advanced Scanner API. For production deployments, implement proper error handling, rate limiting, and monitoring systems.