/**
 * Cipher-Spy Network Monitor - Background Script
 *
 * Handles network traffic interception, analysis, and communication
 * with the Cipher-Spy backend for real-time API discovery.
 */

// Configuration
const CIPHER_SPY_BACKEND = 'http://localhost:8000';
const STORAGE_KEYS = {
  CAPTURED_REQUESTS: 'capturedRequests',
  SETTINGS: 'cipherSpySettings',
  SESSION_ID: 'sessionId'
};

// Global state
let capturedRequests = [];
let isMonitoring = false;
let currentSessionId = null;
let connectedTabs = new Set();

// Initialize extension
chrome.runtime.onInstalled.addListener(async () => {
  console.log('Cipher-Spy Network Monitor installed');
  await initializeExtension();
});

// Initialize on startup as well
chrome.runtime.onStartup.addListener(async () => {
  console.log('Cipher-Spy Network Monitor starting up');
  await initializeExtension();
});

// Initialize immediately when background script loads
(async () => {
  console.log('Cipher-Spy Network Monitor background script loaded');
  await initializeExtension();
})();

// Centralized initialization function
async function initializeExtension() {
  try {
    // Initialize storage
    await initializeStorage();

    // Generate session ID if not exists
    const stored = await chrome.storage.local.get([STORAGE_KEYS.SESSION_ID]);
    if (!stored[STORAGE_KEYS.SESSION_ID]) {
      currentSessionId = generateSessionId();
      await chrome.storage.local.set({ [STORAGE_KEYS.SESSION_ID]: currentSessionId });
    } else {
      currentSessionId = stored[STORAGE_KEYS.SESSION_ID];
    }

    console.log('Extension initialized with session:', currentSessionId);
  } catch (error) {
    console.error('Failed to initialize extension:', error);
  }
}

// Get default settings
function getDefaultSettings() {
  return {
    monitoringEnabled: true,
    captureHeaders: true,
    capturePayloads: true,
    filterPatterns: ['/api/', '.json', '/graphql', '/rest/'],
    excludePatterns: ['.css', '.js', '.png', '.jpg', '.gif', '.ico'],
    maxRequestsStored: 1000,
    backendUrl: CIPHER_SPY_BACKEND,
    autoAnalysis: true
  };
}

// Initialize storage with default settings
async function initializeStorage() {
  try {
    const stored = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
    if (!stored[STORAGE_KEYS.SETTINGS]) {
      const defaultSettings = getDefaultSettings();
      await chrome.storage.local.set({ [STORAGE_KEYS.SETTINGS]: defaultSettings });
      console.log('Initialized default settings');
    }

    // Initialize empty requests array if not exists
    const requests = await chrome.storage.local.get(STORAGE_KEYS.CAPTURED_REQUESTS);
    if (!requests[STORAGE_KEYS.CAPTURED_REQUESTS]) {
      await chrome.storage.local.set({ [STORAGE_KEYS.CAPTURED_REQUESTS]: [] });
    }
  } catch (error) {
    console.error('Failed to initialize storage:', error);
  }
}

// Generate unique session ID
function generateSessionId() {
  return `cs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Network request interception
chrome.webRequest.onBeforeRequest.addListener(
  async (details) => {
    if (!isMonitoring) return;

    const settings = await getSettings();
    if (!settings.monitoringEnabled) return;

    // Filter requests based on patterns
    if (!shouldCaptureRequest(details.url, settings)) return;

    const requestData = {
      id: generateRequestId(),
      sessionId: currentSessionId,
      timestamp: Date.now(),
      url: details.url,
      method: details.method,
      type: details.type,
      tabId: details.tabId,
      frameId: details.frameId,
      requestBody: settings.capturePayloads ? details.requestBody : null,
      initiator: details.initiator
    };

    // Store request for response matching
    await storeRequest(requestData);

    // Notify content script
    notifyContentScript(details.tabId, 'requestCaptured', requestData);
  },
  { urls: ['<all_urls>'] },
  ['requestBody']
);

// Response interception
chrome.webRequest.onCompleted.addListener(
  async (details) => {
    if (!isMonitoring) return;

    const settings = await getSettings();
    if (!settings.monitoringEnabled) return;

    if (!shouldCaptureRequest(details.url, settings)) return;

    // Find matching request
    const requestData = await findStoredRequest(details.requestId);
    if (!requestData) return;

    // Complete request data with response info
    const completeData = {
      ...requestData,
      response: {
        statusCode: details.statusCode,
        statusLine: details.statusLine,
        responseHeaders: settings.captureHeaders ? details.responseHeaders : null,
        timestamp: Date.now()
      }
    };

    // Store complete request
    await storeCompleteRequest(completeData);

    // Analyze if auto-analysis is enabled
    if (settings.autoAnalysis) {
      await analyzeRequest(completeData);
    }

    // Notify UI
    notifyUI('requestCompleted', completeData);
  },
  { urls: ['<all_urls>'] },
  ['responseHeaders']
);

// Error handling
chrome.webRequest.onErrorOccurred.addListener(
  async (details) => {
    if (!isMonitoring) return;

    const requestData = await findStoredRequest(details.requestId);
    if (!requestData) return;

    const errorData = {
      ...requestData,
      error: {
        error: details.error,
        timestamp: Date.now()
      }
    };

    await storeCompleteRequest(errorData);
    notifyUI('requestError', errorData);
  },
  { urls: ['<all_urls>'] }
);

// Message handling
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  handleMessage(message, sender, sendResponse);
  return true; // Keep message channel open for async response
});

async function handleMessage(message, sender, sendResponse) {
  try {
    console.log('Handling message:', message.action);

    switch (message.action) {
      case 'startMonitoring':
        await startMonitoring();
        sendResponse({ success: true });
        break;

      case 'stopMonitoring':
        await stopMonitoring();
        sendResponse({ success: true });
        break;

      case 'getRequests':
        const requests = await getCapturedRequests();
        sendResponse({ requests });
        break;

      case 'clearRequests':
        await clearCapturedRequests();
        sendResponse({ success: true });
        break;

      case 'exportRequests':
        const exportData = await exportCapturedRequests();
        sendResponse({ data: exportData });
        break;

      case 'analyzeRequests':
        await analyzeAllRequests();
        sendResponse({ success: true });
        break;

      case 'getSettings':
        const settings = await getSettings();
        sendResponse({ settings });
        break;

      case 'updateSettings':
        await updateSettings(message.settings);
        sendResponse({ success: true });
        break;

      case 'getMonitoringStatus':
        sendResponse({
          isMonitoring,
          sessionId: currentSessionId,
          requestCount: capturedRequests.length
        });
        break;

      default:
        console.warn('Unknown action:', message.action);
        sendResponse({ error: 'Unknown action' });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    sendResponse({ error: error.message });
  }
}

// Monitoring control
async function startMonitoring() {
  isMonitoring = true;
  currentSessionId = generateSessionId();
  await chrome.storage.local.set({ [STORAGE_KEYS.SESSION_ID]: currentSessionId });

  // Notify all tabs
  const tabs = await chrome.tabs.query({});
  tabs.forEach(tab => {
    notifyContentScript(tab.id, 'monitoringStarted', { sessionId: currentSessionId });
  });

  console.log('Network monitoring started, session:', currentSessionId);
}

async function stopMonitoring() {
  isMonitoring = false;

  // Notify all tabs
  const tabs = await chrome.tabs.query({});
  tabs.forEach(tab => {
    notifyContentScript(tab.id, 'monitoringStopped');
  });

  console.log('Network monitoring stopped');
}

// Request filtering
function shouldCaptureRequest(url, settings) {
  // Check exclude patterns first
  if (settings.excludePatterns.some(pattern => url.includes(pattern))) {
    return false;
  }

  // Check include patterns
  if (settings.filterPatterns.length === 0) {
    return true; // Capture all if no filters
  }

  return settings.filterPatterns.some(pattern => url.includes(pattern));
}

// Request storage
async function storeRequest(requestData) {
  const stored = await chrome.storage.local.get(STORAGE_KEYS.CAPTURED_REQUESTS);
  const requests = stored[STORAGE_KEYS.CAPTURED_REQUESTS] || [];

  requests.push(requestData);

  // Limit storage size
  const settings = await getSettings();
  if (requests.length > settings.maxRequestsStored) {
    requests.splice(0, requests.length - settings.maxRequestsStored);
  }

  await chrome.storage.local.set({ [STORAGE_KEYS.CAPTURED_REQUESTS]: requests });
  capturedRequests = requests;
}

async function storeCompleteRequest(requestData) {
  const stored = await chrome.storage.local.get(STORAGE_KEYS.CAPTURED_REQUESTS);
  const requests = stored[STORAGE_KEYS.CAPTURED_REQUESTS] || [];

  // Find and update the request
  const index = requests.findIndex(req => req.id === requestData.id);
  if (index !== -1) {
    requests[index] = requestData;
  } else {
    requests.push(requestData);
  }

  await chrome.storage.local.set({ [STORAGE_KEYS.CAPTURED_REQUESTS]: requests });
  capturedRequests = requests;
}

async function findStoredRequest(requestId) {
  const stored = await chrome.storage.local.get(STORAGE_KEYS.CAPTURED_REQUESTS);
  const requests = stored[STORAGE_KEYS.CAPTURED_REQUESTS] || [];
  return requests.find(req => req.id === requestId);
}

async function getCapturedRequests() {
  const stored = await chrome.storage.local.get(STORAGE_KEYS.CAPTURED_REQUESTS);
  return stored[STORAGE_KEYS.CAPTURED_REQUESTS] || [];
}

async function clearCapturedRequests() {
  await chrome.storage.local.set({ [STORAGE_KEYS.CAPTURED_REQUESTS]: [] });
  capturedRequests = [];
}

// Analysis functions
async function analyzeRequest(requestData) {
  try {
    const settings = await getSettings();

    // Send to Cipher-Spy backend for analysis
    const response = await fetch(`${settings.backendUrl}/api/extension/analyze-request`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: currentSessionId,
        request: requestData
      })
    });

    if (response.ok) {
      const analysis = await response.json();
      console.log('Request analyzed:', analysis);

      // Store analysis result
      requestData.analysis = analysis;
      await storeCompleteRequest(requestData);
    }
  } catch (error) {
    console.error('Error analyzing request:', error);
  }
}

async function analyzeAllRequests() {
  const requests = await getCapturedRequests();
  const settings = await getSettings();

  try {
    const response = await fetch(`${settings.backendUrl}/api/extension/analyze-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: currentSessionId,
        requests: requests
      })
    });

    if (response.ok) {
      const analysis = await response.json();
      console.log('Session analyzed:', analysis);

      // Notify UI of analysis completion
      notifyUI('sessionAnalyzed', analysis);
    }
  } catch (error) {
    console.error('Error analyzing session:', error);
  }
}

// Export functionality
async function exportCapturedRequests() {
  const requests = await getCapturedRequests();
  const settings = await getSettings();

  return {
    sessionId: currentSessionId,
    timestamp: Date.now(),
    settings: settings,
    requestCount: requests.length,
    requests: requests
  };
}

// Settings management
async function getSettings() {
  try {
    const stored = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
    const settings = stored[STORAGE_KEYS.SETTINGS];

    // Return default settings if none exist
    if (!settings) {
      console.log('No settings found, returning defaults');
      const defaultSettings = getDefaultSettings();
      await chrome.storage.local.set({ [STORAGE_KEYS.SETTINGS]: defaultSettings });
      return defaultSettings;
    }

    return settings;
  } catch (error) {
    console.error('Error getting settings:', error);
    return getDefaultSettings();
  }
}

async function updateSettings(newSettings) {
  try {
    await chrome.storage.local.set({ [STORAGE_KEYS.SETTINGS]: newSettings });
    console.log('Settings updated successfully');
  } catch (error) {
    console.error('Error updating settings:', error);
    throw error;
  }
}

// Communication helpers
function notifyContentScript(tabId, action, data = {}) {
  chrome.tabs.sendMessage(tabId, { action, data }).catch(() => {
    // Ignore errors for tabs without content script
  });
}

function notifyUI(action, data = {}) {
  // Notify popup and devtools
  chrome.runtime.sendMessage({ action, data }).catch(() => {
    // Ignore if no listeners
  });
}

// Utility functions
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Tab management
chrome.tabs.onRemoved.addListener((tabId) => {
  connectedTabs.delete(tabId);
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && isMonitoring) {
    notifyContentScript(tabId, 'monitoringStatus', {
      isMonitoring,
      sessionId: currentSessionId
    });
  }
});

// Initialize monitoring state on startup
chrome.runtime.onStartup.addListener(async () => {
  const stored = await chrome.storage.local.get([STORAGE_KEYS.SESSION_ID, STORAGE_KEYS.SETTINGS]);
  currentSessionId = stored[STORAGE_KEYS.SESSION_ID] || generateSessionId();

  const settings = stored[STORAGE_KEYS.SETTINGS];
  if (settings && settings.monitoringEnabled) {
    isMonitoring = true;
  }
});
