#!/usr/bin/env python3
"""
Quick crawler entry point that bypasses complex dependencies.

This provides a simple way to test the core crawling functionality
without the full agent orchestration system.
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def quick_crawl(target_url: str, max_pages: int = 5):
    """
    Perform a quick crawl of a target URL.
    
    Args:
        target_url: URL to crawl
        max_pages: Maximum number of pages to crawl
    """
    print(f"🕷️  Quick Crawl: {target_url}")
    print(f"📊 Max pages: {max_pages}")
    print("="*50)
    
    try:
        # Import crawler components
        from src.crawling.playwright_crawler import PlaywrightCrawler
        from src.crawling.network_interceptor import NetworkInterceptor
        from src.crawling.scope_manager import ScopeManager
        
        # Parse target URL
        parsed = urlparse(target_url)
        domain = parsed.netloc
        
        # Create scope manager
        scope_manager = ScopeManager(
            allowed_domains=[domain],
            respect_robots_txt=True
        )
        
        # Create network interceptor
        network_interceptor = NetworkInterceptor()
        
        # Create crawler
        crawler = PlaywrightCrawler(
            headless=True,
            delay_ms=2000,
            scope_manager=scope_manager,
            network_interceptor=network_interceptor
        )
        
        print("🚀 Starting crawler...")
        await crawler.start()
        
        # Crawl the main page
        print(f"📄 Crawling: {target_url}")
        result = await crawler.crawl_page(target_url)
        
        if result:
            print(f"✅ Successfully crawled: {result.title}")
            print(f"   Status: {result.status_code}")
            print(f"   Content-Type: {result.content_type}")
            
            # Get discovered endpoints
            endpoints = network_interceptor.get_discovered_endpoints()
            print(f"🔗 Discovered {len(endpoints)} API endpoints")
            
            # Show some endpoints
            for i, endpoint in enumerate(endpoints[:5], 1):
                print(f"   {i}. {endpoint.method} {endpoint.url}")
            
            # Get statistics
            stats = network_interceptor.get_statistics()
            print(f"📊 Network Statistics:")
            print(f"   Requests: {stats.get('total_requests', 0)}")
            print(f"   Responses: {stats.get('total_responses', 0)}")
            
            # Save results
            results_dir = Path("quick_crawl_results")
            results_dir.mkdir(exist_ok=True)
            
            # Save basic report
            report = {
                "target_url": target_url,
                "crawl_time": datetime.now().isoformat(),
                "page_info": {
                    "url": result.url,
                    "title": result.title,
                    "status_code": result.status_code,
                    "content_type": result.content_type
                },
                "endpoints": [
                    {
                        "method": ep.method,
                        "url": ep.url,
                        "status": ep.response_status
                    }
                    for ep in endpoints
                ],
                "statistics": stats
            }
            
            report_file = results_dir / f"crawl_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            print(f"💾 Report saved: {report_file}")
            
        else:
            print("❌ Failed to crawl page")
        
        # Cleanup
        await crawler.stop()
        print("✅ Crawler stopped")
        
        return True
        
    except Exception as e:
        print(f"💥 Crawl failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main entry point."""
    print("🔬 Cipher-Spy Quick Crawler")
    print("="*40)
    
    # Get target URL from user or use default
    if len(sys.argv) > 1:
        target_url = sys.argv[1]
    else:
        target_url = input("Enter target URL (or press Enter for httpbin.org): ").strip()
        if not target_url:
            target_url = "https://httpbin.org/html"
    
    # Validate URL
    if not target_url.startswith(('http://', 'https://')):
        target_url = f"https://{target_url}"
    
    print(f"🎯 Target: {target_url}")
    
    # Run crawl
    success = await quick_crawl(target_url)
    
    if success:
        print("\n🎉 Quick crawl completed successfully!")
        print("Check the quick_crawl_results/ directory for output.")
        return 0
    else:
        print("\n❌ Quick crawl failed!")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Crawl interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
