# Pump Flags API

## Overview
The Pump Flags API endpoint provides access to pump.fun's global feature configuration and system status flags. This endpoint enables developers to programmatically check which platform features are currently enabled or disabled, allowing for dynamic feature availability in client applications and integrations.

## Endpoint Information
- **URL**: `https://pump.fun/api/flags`
- **Method**: GET
- **Authentication**: None required
- **Content Type**: application/json
- **Average Response Time**: ~320ms

## Parameters
This endpoint does not accept any parameters.

## Response Format
Returns a JSON object containing boolean flags that indicate the status of various platform features.

```json
{
  "feature_name": boolean
}
```

## Code Examples

### Python Example
```python
import requests

def get_pump_flags():
    url = "https://pump.fun/api/flags"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching flags: {e}")
        return None

# Usage
flags = get_pump_flags()
if flags and flags.get('semantic_search_enabled'):
    print("Semantic search is enabled")
```

### cURL Example
```bash
curl -X GET "https://pump.fun/api/flags" \
  -H "Accept: application/json"
```

### JavaScript/Node.js Example
```javascript
async function getPumpFlags() {
  try {
    const response = await fetch('https://pump.fun/api/flags');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const flags = await response.json();
    return flags;
  } catch (error) {
    console.error('Error fetching flags:', error);
    return null;
  }
}

// Usage
getPumpFlags().then(flags => {
  if (flags?.semantic_search_enabled) {
    console.log('Semantic search is enabled');
  }
});
```

## Response Fields Reference

| Field | Type | Description |
|-------|------|-------------|
| trending_carousel_enabled | boolean | Controls visibility of trending items carousel |
| semantic_search_enabled | boolean | Enables advanced semantic search functionality |
| similar_coins_enabled | boolean | Shows similar coin recommendations |
| trade_history_recs_enabled | boolean | Enables trade history recommendations |
| multi_column_advanced_enabled | boolean | Activates multi-column advanced view |
| hybrid_search_enabled | boolean | Enables hybrid search algorithm |
| homepage_v2_enabled | boolean | Toggles new homepage version |
| livestreams_enabled | boolean | Controls livestreaming functionality |
| coinpage_v2_enabled | boolean | Enables updated coin page design |
| creator_rewards_rollout_stage | boolean | Controls creator rewards program availability |
| coin_creation_button_disabled | boolean | Indicates if coin creation is temporarily disabled |
| screener_table_enabled | boolean | Controls availability of screening tools |

## Use Cases

1. **Feature Availability Checking**
   ```javascript
   if (flags.semantic_search_enabled) {
     enableSearchFeature();
   }
   ```

2. **UI Component Rendering**
   ```javascript
   function renderDashboard(flags) {
     return (
       <Dashboard>
         {flags.trending_carousel_enabled && <TrendingCarousel />}
         {flags.screener_table_enabled && <ScreenerTable />}
       </Dashboard>
     );
   }
   ```

3. **System Status Monitoring**
   ```python
   def check_system_status(flags):
       critical_features = ['semantic_search_enabled', 'hybrid_search_enabled']
       return all(flags.get(feature) for feature in critical_features)
   ```

## Error Handling

| Status Code | Description | Resolution |
|-------------|-------------|------------|
| 200 | Successful response | Process the returned flags |
| 429 | Rate limit exceeded | Implement exponential backoff |
| 500 | Server error | Retry with backoff strategy |
| 503 | Service unavailable | Cache previous values, retry later |

## Rate Limiting & Best Practices
- Cache responses for 5-10 minutes to reduce API load
- Implement exponential backoff for retries
- Handle network timeouts gracefully
- Set reasonable request timeouts (2-5 seconds)

## Integration Tips
1. Implement local caching to prevent excessive API calls
2. Use default values for flags when API is unavailable
3. Consider implementing a circuit breaker pattern for reliability
4. Log flag changes for debugging and analytics
5. Implement graceful degradation when features are disabled

This endpoint is designed for periodic polling rather than real-time updates. For optimal performance, cache the response and refresh periodically rather than querying on every user action.