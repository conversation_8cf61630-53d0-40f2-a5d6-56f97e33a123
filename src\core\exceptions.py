"""
Custom exception classes for Cipher-Spy.

Defines a hierarchy of exceptions for different components and error types.
Each exception includes status codes for HTTP responses and detailed error information.
"""

from typing import Optional, Dict, Any


class CipherSpyException(Exception):
    """
    Base exception class for all Cipher-Spy errors.
    
    Provides structured error handling with HTTP status codes and detailed messages.
    """
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize exception.
        
        Args:
            message: Human-readable error message
            status_code: HTTP status code for API responses
            details: Additional error details and context
        """
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}: {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization."""
        return {
            "error": self.__class__.__name__,
            "message": self.message,
            "status_code": self.status_code,
            "details": self.details
        }


class CrawlingException(CipherSpyException):
    """Exception raised during web crawling operations."""
    
    def __init__(
        self,
        message: str,
        url: Optional[str] = None,
        status_code: int = 400,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if url:
            details["url"] = url
        super().__init__(message, status_code, details)


class FingerprintingException(CipherSpyException):
    """Exception raised during technology fingerprinting."""
    
    def __init__(
        self,
        message: str,
        tool: Optional[str] = None,
        target: Optional[str] = None,
        status_code: int = 400,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if tool:
            details["tool"] = tool
        if target:
            details["target"] = target
        super().__init__(message, status_code, details)


class ExploitException(CipherSpyException):
    """Exception raised during exploit planning or execution."""
    
    def __init__(
        self,
        message: str,
        exploit_id: Optional[str] = None,
        vulnerability: Optional[str] = None,
        status_code: int = 400,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if exploit_id:
            details["exploit_id"] = exploit_id
        if vulnerability:
            details["vulnerability"] = vulnerability
        super().__init__(message, status_code, details)


class DatabaseException(CipherSpyException):
    """Exception raised during database operations."""
    
    def __init__(
        self,
        message: str,
        database: Optional[str] = None,
        operation: Optional[str] = None,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if database:
            details["database"] = database
        if operation:
            details["operation"] = operation
        super().__init__(message, status_code, details)


class LLMException(CipherSpyException):
    """Exception raised during LLM operations."""
    
    def __init__(
        self,
        message: str,
        model: Optional[str] = None,
        provider: Optional[str] = None,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if model:
            details["model"] = model
        if provider:
            details["provider"] = provider
        super().__init__(message, status_code, details)


class ValidationException(CipherSpyException):
    """Exception raised during input validation."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        status_code: int = 422,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)
        super().__init__(message, status_code, details)


class AuthenticationException(CipherSpyException):
    """Exception raised during authentication operations."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        status_code: int = 401,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, status_code, details)


class AuthorizationException(CipherSpyException):
    """Exception raised during authorization checks."""
    
    def __init__(
        self,
        message: str = "Access denied",
        resource: Optional[str] = None,
        action: Optional[str] = None,
        status_code: int = 403,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if resource:
            details["resource"] = resource
        if action:
            details["action"] = action
        super().__init__(message, status_code, details)


class ConfigurationException(CipherSpyException):
    """Exception raised due to configuration errors."""
    
    def __init__(
        self,
        message: str,
        setting: Optional[str] = None,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if setting:
            details["setting"] = setting
        super().__init__(message, status_code, details)


class TimeoutException(CipherSpyException):
    """Exception raised when operations timeout."""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        operation: Optional[str] = None,
        status_code: int = 408,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if timeout_seconds:
            details["timeout_seconds"] = timeout_seconds
        if operation:
            details["operation"] = operation
        super().__init__(message, status_code, details)


class RateLimitException(CipherSpyException):
    """Exception raised when rate limits are exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        status_code: int = 429,
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        if retry_after:
            details["retry_after"] = retry_after
        super().__init__(message, status_code, details)
