#!/usr/bin/env python3
"""
Chrome Extension Verification Script

Verifies that all required files for the Cipher-Spy Chrome extension
are present and properly configured.
"""

import json
import os
from pathlib import Path


def verify_extension():
    """Verify Chrome extension is ready for installation."""
    print("🔍 Verifying Cipher-Spy Chrome Extension")
    print("="*50)
    
    extension_dir = Path("chrome_extension")
    
    if not extension_dir.exists():
        print("❌ chrome_extension directory not found!")
        return False
    
    # Required files
    required_files = [
        "manifest.json",
        "background.js",
        "content.js",
        "popup/popup.html",
        "popup/popup.css", 
        "popup/popup.js",
        "devtools/devtools.html",
        "devtools/devtools.js",
        "devtools/panel.html",
        "devtools/panel.css",
        "devtools/panel.js",
        "icons/icon16.png",
        "icons/icon32.png",
        "icons/icon48.png",
        "icons/icon128.png"
    ]
    
    print("📁 Checking required files...")
    missing_files = []
    
    for file_path in required_files:
        full_path = extension_dir / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ {len(missing_files)} files are missing!")
        print("Run the following to fix:")
        
        if any("icon" in f for f in missing_files):
            print("   python create_extension_icons.py")
        
        return False
    
    # Verify manifest.json
    print("\n📋 Verifying manifest.json...")
    try:
        with open(extension_dir / "manifest.json", "r") as f:
            manifest = json.load(f)
        
        # Check required fields
        required_fields = ["manifest_version", "name", "version", "permissions"]
        for field in required_fields:
            if field in manifest:
                print(f"   ✅ {field}: {manifest[field]}")
            else:
                print(f"   ❌ Missing required field: {field}")
                return False
        
        # Check manifest version
        if manifest.get("manifest_version") != 3:
            print("   ⚠️ Warning: Using Manifest V2 (V3 recommended)")
        
        # Check permissions
        permissions = manifest.get("permissions", [])
        required_permissions = ["activeTab", "storage", "webRequest"]
        for perm in required_permissions:
            if perm in permissions:
                print(f"   ✅ Permission: {perm}")
            else:
                print(f"   ⚠️ Missing permission: {perm}")
        
    except json.JSONDecodeError as e:
        print(f"   ❌ Invalid JSON in manifest.json: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Error reading manifest.json: {e}")
        return False
    
    # Check file sizes (basic validation)
    print("\n📊 Checking file sizes...")
    for file_path in required_files:
        full_path = extension_dir / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            if size == 0:
                print(f"   ⚠️ {file_path} is empty")
            elif size < 100 and file_path.endswith(('.js', '.html', '.css')):
                print(f"   ⚠️ {file_path} seems very small ({size} bytes)")
            else:
                print(f"   ✅ {file_path} ({size} bytes)")
    
    # Check icon files
    print("\n🎨 Verifying icons...")
    icon_sizes = [16, 32, 48, 128]
    for size in icon_sizes:
        icon_path = extension_dir / f"icons/icon{size}.png"
        if icon_path.exists():
            file_size = icon_path.stat().st_size
            if file_size > 0:
                print(f"   ✅ icon{size}.png ({file_size} bytes)")
            else:
                print(f"   ❌ icon{size}.png is empty")
        else:
            print(f"   ❌ icon{size}.png missing")
    
    print("\n🎉 Extension verification completed!")
    print("\n📋 Installation Instructions:")
    print("1. Open Chrome and go to chrome://extensions/")
    print("2. Enable 'Developer mode' (toggle in top-right)")
    print("3. Click 'Load unpacked'")
    print("4. Select the 'chrome_extension' folder")
    print("5. The extension should appear in your extensions list")
    
    print("\n🔧 Optional: Start the backend server")
    print("   python -m src.main")
    
    print("\n✅ Extension is ready for installation!")
    return True


def create_missing_files():
    """Create any missing files with basic content."""
    print("\n🛠️ Creating missing files...")
    
    extension_dir = Path("chrome_extension")
    extension_dir.mkdir(exist_ok=True)
    
    # Create directories
    (extension_dir / "popup").mkdir(exist_ok=True)
    (extension_dir / "devtools").mkdir(exist_ok=True)
    (extension_dir / "icons").mkdir(exist_ok=True)
    (extension_dir / "utils").mkdir(exist_ok=True)
    
    # Create basic files if they don't exist
    files_to_create = {
        "utils/README.md": "# Utility files for Chrome extension\n\nThis directory contains utility files that may be accessed by web pages.",
    }
    
    for file_path, content in files_to_create.items():
        full_path = extension_dir / file_path
        if not full_path.exists():
            with open(full_path, "w") as f:
                f.write(content)
            print(f"   ✅ Created {file_path}")


def main():
    """Main verification function."""
    try:
        # Create any missing basic files
        create_missing_files()
        
        # Verify the extension
        success = verify_extension()
        
        if success:
            print("\n🚀 Ready to install the Chrome extension!")
            return 0
        else:
            print("\n❌ Extension verification failed!")
            print("Please fix the issues above and run this script again.")
            return 1
            
    except Exception as e:
        print(f"\n💥 Verification failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
