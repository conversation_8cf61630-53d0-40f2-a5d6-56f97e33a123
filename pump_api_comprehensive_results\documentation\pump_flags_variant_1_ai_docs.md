# Pump.fun Feature Flags API

## Overview
The `/api/flags` endpoint provides real-time access to Pump.fun's feature flag configuration, enabling developers to determine which platform features are currently enabled or disabled. This endpoint is essential for implementing feature-aware clients and progressive feature rollouts.

## Base URL
```
https://pump.fun/api/flags
```

## Authentication
This endpoint does not require authentication.

## Use Cases
1. **Dynamic Feature Implementation**
   - Check which features should be enabled in client applications
   - Implement progressive feature rollouts
   - Support A/B testing scenarios

2. **Platform Status Monitoring**
   - Monitor the availability of specific platform features
   - Track feature deployment status
   - Implement feature-dependent functionality

3. **User Experience Customization**
   - Customize UI based on available features
   - Show/hide feature-dependent UI elements
   - Implement fallback behaviors for disabled features

## Request

### HTTP Method
`GET`

### Parameters
This endpoint does not accept any parameters.

## Response

### Response Format
Returns a JSON object containing boolean flags for various platform features.

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `trending_carousel_enabled` | boolean | Controls visibility of trending carousel feature |
| `semantic_search_enabled` | boolean | Enables advanced semantic search capabilities |
| `similar_coins_enabled` | boolean | Controls similar coins recommendation feature |
| `trade_history_recs_enabled` | boolean | Enables trade history recommendations |
| `multi_column_advanced_enabled` | boolean | Controls multi-column layout features |
| `hybrid_search_enabled` | boolean | Enables hybrid search functionality |
| `homepage_v2_enabled` | boolean | Controls new homepage version availability |
| `livestreams_enabled` | boolean | Controls livestreaming feature availability |
| `create_coin_v2_enabled` | boolean | Enables new coin creation interface |

## Code Examples

### Python
```python
import requests

def get_feature_flags():
    url = "https://pump.fun/api/flags"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching feature flags: {e}")
        return None

# Usage
flags = get_feature_flags()
if flags and flags.get('semantic_search_enabled'):
    # Implement semantic search features
    pass
```

### cURL
```bash
curl -X GET "https://pump.fun/api/flags" \
     -H "Accept: application/json"
```

## Error Handling

### Common Errors

| Status Code | Description | Solution |
|------------|-------------|----------|
| 429 | Too Many Requests | Implement rate limiting and backoff strategy |
| 500 | Internal Server Error | Retry with exponential backoff |
| 503 | Service Unavailable | Retry after a delay |

### Error Response Format
```json
{
  "error": "error_code",
  "message": "Human readable error message"
}
```

## Rate Limiting

- Implement caching of responses (recommended cache duration: 5 minutes)
- Maximum 100 requests per minute per IP
- Use exponential backoff when rate limited

## Integration Tips

1. **Caching**
   - Cache feature flags locally
   - Implement a background refresh mechanism
   - Use stale-while-revalidate pattern

2. **Error Handling**
   - Implement fallback values for all features
   - Use default conservative values when API is unavailable
   - Log and monitor API failures

3. **Feature Implementation**
   ```javascript
   function isFeatureEnabled(flags, featureName, defaultValue = false) {
     return flags?.[featureName] ?? defaultValue;
   }
   ```

4. **Performance**
   - Batch feature flag checks
   - Implement client-side caching
   - Use appropriate cache headers

## Best Practices

1. Always check feature flags before enabling feature-dependent functionality
2. Implement graceful degradation for disabled features
3. Cache feature flags appropriately to reduce API load
4. Monitor feature flag changes in your application
5. Implement proper error handling and fallbacks

## See Also
- [Pump.fun API Documentation](https://pump.fun/docs)
- [Feature Flags Best Practices](https://pump.fun/docs/features)
- [API Status Page](https://status.pump.fun)