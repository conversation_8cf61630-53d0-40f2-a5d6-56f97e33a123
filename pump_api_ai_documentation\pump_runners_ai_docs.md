# Pump Runners API

## Overview
The Pump Runners endpoint provides real-time access to featured cryptocurrency tokens and coins on the Pump.fun platform. This endpoint is essential for developers building applications that need to track trending tokens, monitor market activity, or display featured content from the Pump.fun ecosystem.

## Endpoint Information
- **URL**: `https://pump.fun/api/runners`
- **Method**: GET
- **Content-Type**: application/json
- **Authentication**: Not required
- **Average Response Time**: ~300ms

## Parameters
This endpoint does not require any parameters.

## Response Format
Returns an array of runner objects, each containing detailed coin information and metadata.

```json
[
  {
    "coin": {
      "mint": "string",
      "name": "string",
      "symbol": "string",
      "description": "string",
      "image_uri": "string",
      "market_cap": "number",
      // ... additional fields
    },
    "description": "string",
    "modifiedBy": "string"
  }
]
```

## Code Examples

### Python Example
```python
import requests

def get_pump_runners():
    url = "https://pump.fun/api/runners"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching runners: {e}")
        return None

# Usage
runners = get_pump_runners()
if runners:
    for runner in runners:
        print(f"Token: {runner['coin']['name']} ({runner['coin']['symbol']})")
        print(f"Market Cap: ${runner['coin']['market_cap']:,.2f}")
```

### cURL Example
```bash
curl -X GET "https://pump.fun/api/runners" \
  -H "Accept: application/json"
```

### JavaScript/Node.js Example
```javascript
async function getPumpRunners() {
  try {
    const response = await fetch('https://pump.fun/api/runners');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching runners:', error);
    return null;
  }
}

// Usage
getPumpRunners().then(runners => {
  if (runners) {
    runners.forEach(runner => {
      console.log(`${runner.coin.name} - Market Cap: $${runner.coin.market_cap}`);
    });
  }
});
```

## Response Fields Reference

### Coin Object
| Field | Type | Description |
|-------|------|-------------|
| mint | string | Unique identifier for the token |
| name | string | Display name of the token |
| symbol | string | Trading symbol |
| description | string | Token description |
| market_cap | number | Current market capitalization in USD |
| virtual_sol_reserves | number | Current SOL reserves |
| total_supply | number | Total token supply |
| created_timestamp | number | Creation timestamp (milliseconds) |
| is_currently_live | boolean | Active trading status |

## Use Cases
1. **Market Analysis**
   - Track trending tokens
   - Monitor market capitalization changes
   - Analyze new token launches

2. **Portfolio Applications**
   - Display featured tokens to users
   - Show real-time market data
   - Track token performance

3. **Trading Bots**
   - Identify new trading opportunities
   - Monitor market activity
   - Execute automated trading strategies

## Error Handling
Common HTTP status codes:
- 200: Successful request
- 429: Rate limit exceeded
- 500: Server error

Handle errors by implementing retry logic with exponential backoff for 429 responses.

## Rate Limiting & Best Practices
- Implement caching for responses (recommended: 60 seconds)
- Limit requests to 100 per minute per IP
- Include error handling and retry logic
- Monitor response times and implement timeouts

## Integration Tips
1. Cache responses to reduce API load
2. Implement error handling for network issues
3. Use webhook notifications for real-time updates when available
4. Monitor market_cap and virtual_sol_reserves for significant changes
5. Validate data integrity before processing

This endpoint is optimized for real-time market data applications. For historical data or detailed analytics, consider using additional endpoints or implementing local data storage.