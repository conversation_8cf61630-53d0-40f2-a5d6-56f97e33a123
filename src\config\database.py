"""
Database configuration and connection management.

Handles PostgreSQL and Neo4j database connections, connection pooling,
and database initialization. Provides async context managers for
database operations.
"""

import logging
from typing import Optional
from contextlib import asynccontextmanager

import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from neo4j import AsyncGraphDatabase, AsyncDriver

# Optional Graphiti import
try:
    from graphiti_core import Graphiti
    GRAPHITI_AVAILABLE = True
except ImportError:
    Graphiti = None
    GRAPHITI_AVAILABLE = False

from .settings import get_settings

# Global instances
settings = get_settings()
logger = logging.getLogger(__name__)

# SQLAlchemy setup
Base = declarative_base()
async_engine = None
async_session_maker = None

# Neo4j setup
neo4j_driver: Optional[AsyncDriver] = None
graphiti_client: Optional[Graphiti] = None


class DatabaseManager:
    """
    Database connection manager for PostgreSQL and Neo4j.

    Provides connection pooling, health checks, and graceful shutdown.
    """

    def __init__(self):
        self.postgres_engine = None
        self.postgres_session_maker = None
        self.neo4j_driver = None
        self.graphiti_client = None

    async def init_postgres(self):
        """Initialize PostgreSQL connection and session maker."""
        try:
            # Create async engine with connection pooling
            self.postgres_engine = create_async_engine(
                settings.database_url,
                pool_size=settings.database_pool_size,
                max_overflow=10,
                pool_pre_ping=True,
                echo=settings.debug
            )

            # Create session maker
            self.postgres_session_maker = async_sessionmaker(
                bind=self.postgres_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            # Test connection
            async with self.postgres_engine.begin() as conn:
                await conn.execute("SELECT 1")

            logger.info("PostgreSQL connection initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL: {e}")
            raise

    async def init_neo4j(self):
        """Initialize Neo4j connection and Graphiti client."""
        try:
            # Create Neo4j driver
            self.neo4j_driver = AsyncGraphDatabase.driver(
                settings.neo4j_uri,
                auth=(settings.neo4j_user, settings.neo4j_password),
                max_connection_pool_size=settings.neo4j_pool_size
            )

            # Test connection
            await self.neo4j_driver.verify_connectivity()

            # Initialize Graphiti client if available
            if GRAPHITI_AVAILABLE:
                self.graphiti_client = Graphiti(
                    uri=settings.neo4j_uri,
                    user=settings.neo4j_user,
                    password=settings.neo4j_password
                )
                logger.info("Neo4j and Graphiti initialized successfully")
            else:
                logger.warning("Graphiti not available - knowledge graph features disabled")
                logger.info("Neo4j initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Neo4j: {e}")
            raise

    async def close_postgres(self):
        """Close PostgreSQL connections."""
        if self.postgres_engine:
            await self.postgres_engine.dispose()
            logger.info("PostgreSQL connections closed")

    async def close_neo4j(self):
        """Close Neo4j connections."""
        if self.neo4j_driver:
            await self.neo4j_driver.close()
            logger.info("Neo4j connections closed")

        if self.graphiti_client:
            await self.graphiti_client.close()
            logger.info("Graphiti client closed")

    @asynccontextmanager
    async def get_postgres_session(self):
        """
        Get PostgreSQL session context manager.

        Yields:
            AsyncSession: Database session
        """
        if not self.postgres_session_maker:
            raise RuntimeError("PostgreSQL not initialized")

        async with self.postgres_session_maker() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    @asynccontextmanager
    async def get_neo4j_session(self):
        """
        Get Neo4j session context manager.

        Yields:
            AsyncSession: Neo4j session
        """
        if not self.neo4j_driver:
            raise RuntimeError("Neo4j not initialized")

        async with self.neo4j_driver.session() as session:
            yield session

    async def health_check(self) -> dict:
        """
        Perform health check on all database connections.

        Returns:
            dict: Health status for each database
        """
        health = {
            "postgres": {"status": "unknown", "error": None},
            "neo4j": {"status": "unknown", "error": None}
        }

        # Check PostgreSQL
        try:
            if self.postgres_engine:
                async with self.postgres_engine.begin() as conn:
                    await conn.execute("SELECT 1")
                health["postgres"]["status"] = "healthy"
            else:
                health["postgres"]["status"] = "not_initialized"
        except Exception as e:
            health["postgres"]["status"] = "unhealthy"
            health["postgres"]["error"] = str(e)

        # Check Neo4j
        try:
            if self.neo4j_driver:
                await self.neo4j_driver.verify_connectivity()
                health["neo4j"]["status"] = "healthy"
            else:
                health["neo4j"]["status"] = "not_initialized"
        except Exception as e:
            health["neo4j"]["status"] = "unhealthy"
            health["neo4j"]["error"] = str(e)

        return health


# Global database manager instance
db_manager = DatabaseManager()


async def init_databases():
    """Initialize all database connections."""
    await db_manager.init_postgres()
    await db_manager.init_neo4j()

    # Set global variables for backward compatibility
    global async_engine, async_session_maker, neo4j_driver, graphiti_client
    async_engine = db_manager.postgres_engine
    async_session_maker = db_manager.postgres_session_maker
    neo4j_driver = db_manager.neo4j_driver
    graphiti_client = db_manager.graphiti_client


async def close_databases():
    """Close all database connections."""
    await db_manager.close_postgres()
    await db_manager.close_neo4j()


# Dependency functions for FastAPI
async def get_postgres_session():
    """FastAPI dependency for PostgreSQL session."""
    async with db_manager.get_postgres_session() as session:
        yield session


async def get_neo4j_session():
    """FastAPI dependency for Neo4j session."""
    async with db_manager.get_neo4j_session() as session:
        yield session


async def get_graphiti_client():
    """FastAPI dependency for Graphiti client."""
    if not GRAPHITI_AVAILABLE:
        raise RuntimeError("Graphiti not available - install graphiti-core package")
    if not db_manager.graphiti_client:
        raise RuntimeError("Graphiti client not initialized")
    return db_manager.graphiti_client
