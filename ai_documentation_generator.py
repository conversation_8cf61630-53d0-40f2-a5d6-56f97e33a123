#!/usr/bin/env python3
"""
AI-Powered Documentation Generator for Pump.fun APIs

Uses Claude via OpenRouter to generate comprehensive documentation.
"""

import json
import requests
import asyncio
import time
from pathlib import Path
from datetime import datetime

# Direct API key from .env file
OPENROUTER_API_KEY = "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"

class AIPoweredDocumentationGenerator:
    """Generate AI-powered documentation for pump.fun APIs."""
    
    def __init__(self):
        self.results_dir = Path("pump_api_ai_documentation")
        self.results_dir.mkdir(exist_ok=True)
        
        # Known working endpoints with sample data
        self.endpoints = {
            'trending_coins': {
                'url': 'https://frontend-api-v3.pump.fun/coins',
                'method': 'GET',
                'params': {'offset': 0, 'limit': 50, 'sort': 'market_cap', 'order': 'DESC', 'includeNsfw': 'false'},
                'description': 'Get trending coins by market cap',
                'category': 'Market Data'
            },
            'for_you_coins': {
                'url': 'https://frontend-api-v3.pump.fun/coins/for-you',
                'method': 'GET',
                'params': {'offset': 0, 'limit': 48, 'includeNsfw': 'false'},
                'description': 'Get personalized coin recommendations',
                'category': 'Recommendations'
            },
            'pump_flags': {
                'url': 'https://pump.fun/api/flags',
                'method': 'GET',
                'params': {},
                'description': 'Get pump.fun feature flags and configuration',
                'category': 'System'
            },
            'pump_runners': {
                'url': 'https://pump.fun/api/runners',
                'method': 'GET',
                'params': {},
                'description': 'Get featured/runner coins data',
                'category': 'Featured Content'
            }
        }
    
    async def generate_comprehensive_documentation(self):
        """Generate comprehensive AI documentation for all endpoints."""
        print("🤖 AI-Powered Pump.fun API Documentation Generator")
        print("="*60)
        print(f"Using Claude 3.5 Sonnet via OpenRouter...")
        print()
        
        # Test each endpoint and generate documentation
        for endpoint_name, config in self.endpoints.items():
            print(f"📡 Testing and documenting {endpoint_name}...")
            
            # Test the endpoint first
            endpoint_data = await self._test_endpoint(endpoint_name, config)
            
            if endpoint_data['success']:
                print(f"   ✅ Endpoint working - generating AI documentation...")
                
                # Generate AI documentation
                ai_docs = await self._generate_ai_documentation(endpoint_name, config, endpoint_data)
                
                if ai_docs:
                    # Save documentation
                    doc_file = self.results_dir / f"{endpoint_name}_ai_docs.md"
                    with open(doc_file, 'w', encoding='utf-8') as f:
                        f.write(ai_docs)
                    
                    print(f"   📚 AI documentation saved: {doc_file}")
                else:
                    print(f"   ❌ Failed to generate AI documentation")
            else:
                print(f"   ❌ Endpoint failed: {endpoint_data.get('error', 'Unknown error')}")
            
            print()
            await asyncio.sleep(2)  # Rate limiting
        
        # Generate master documentation
        print("📖 Generating master API documentation...")
        master_docs = await self._generate_master_documentation()
        
        if master_docs:
            master_file = self.results_dir / "pump_fun_complete_api_guide.md"
            with open(master_file, 'w', encoding='utf-8') as f:
                f.write(master_docs)
            
            print(f"📚 Master documentation saved: {master_file}")
        
        print(f"\n🎉 AI documentation generation complete!")
        print(f"📁 All files saved to: {self.results_dir}")
    
    async def _test_endpoint(self, name: str, config: dict) -> dict:
        """Test an endpoint and capture response data."""
        try:
            start_time = time.time()
            
            response = requests.get(
                config['url'],
                params=config['params'],
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json',
                    'Referer': 'https://pump.fun/'
                },
                timeout=15
            )
            
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response_time_ms': response_time,
                        'response_size': len(response.content),
                        'data': data,
                        'content_type': response.headers.get('content-type', '')
                    }
                except:
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response_time_ms': response_time,
                        'response_size': len(response.content),
                        'data': response.text[:1000],
                        'content_type': response.headers.get('content-type', '')
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}",
                    'response_text': response.text[:500]
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _generate_ai_documentation(self, endpoint_name: str, config: dict, endpoint_data: dict) -> str:
        """Generate AI documentation for a single endpoint."""
        
        # Prepare comprehensive prompt for Claude
        prompt = f"""You are a senior technical writer creating API documentation for developers. Generate comprehensive, professional documentation for this pump.fun API endpoint.

ENDPOINT DETAILS:
- Name: {endpoint_name}
- URL: {config['url']}
- Method: {config['method']}
- Category: {config['category']}
- Description: {config['description']}
- Parameters: {json.dumps(config['params'], indent=2)}

PERFORMANCE METRICS:
- Response Time: {endpoint_data.get('response_time_ms', 0):.0f}ms
- Response Size: {endpoint_data.get('response_size', 0)} bytes
- Status Code: {endpoint_data.get('status_code', 'N/A')}
- Content Type: {endpoint_data.get('content_type', 'N/A')}

SAMPLE RESPONSE DATA:
{json.dumps(endpoint_data.get('data'), indent=2)[:3000] if endpoint_data.get('data') else 'No data available'}

Create comprehensive documentation with these sections:

# {endpoint_name.replace('_', ' ').title()} API

## Overview
[Clear description of what this endpoint does and its business value]

## Endpoint Information
[Technical details: URL, method, authentication requirements]

## Parameters
[Detailed parameter documentation with types, requirements, and examples]

## Response Format
[Response structure with field descriptions and data types]

## Code Examples

### Python Example
[Complete, working Python code example]

### cURL Example
[Complete cURL command that works]

### JavaScript/Node.js Example
[Complete JavaScript example]

## Response Fields Reference
[Detailed documentation of all response fields with descriptions]

## Use Cases
[3-5 practical use cases with examples]

## Error Handling
[Common errors and how to handle them]

## Rate Limiting & Best Practices
[Guidelines for responsible API usage]

## Integration Tips
[Practical advice for developers]

Make this documentation:
- Professional and comprehensive
- Immediately useful for developers
- Include working code examples
- Cover edge cases and error handling
- Provide business context and use cases

Format as clean markdown suitable for developer documentation.
"""
        
        try:
            response = requests.post(
                'https://openrouter.ai/api/v1/chat/completions',
                headers={
                    'Authorization': f'Bearer {OPENROUTER_API_KEY}',
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://cipher-spy.com',
                    'X-Title': 'Cipher-Spy API Documentation Generator'
                },
                json={
                    'model': 'anthropic/claude-3.5-sonnet',
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 6000,
                    'temperature': 0.1
                },
                timeout=45
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f"      ❌ OpenRouter API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"      ❌ AI documentation generation failed: {e}")
            return None
    
    async def _generate_master_documentation(self) -> str:
        """Generate master documentation combining all endpoints."""
        
        prompt = f"""You are a senior technical writer creating a comprehensive API guide. Create a master documentation guide for pump.fun APIs that have been successfully reverse engineered.

DISCOVERED APIS:
{json.dumps(self.endpoints, indent=2)}

Create a comprehensive master guide with these sections:

# Pump.fun API Complete Developer Guide

## Executive Summary
[Overview of what was discovered and the business value]

## Getting Started
[Quick start guide for developers]

## API Overview
[High-level architecture and how the APIs work together]

## Authentication & Security
[Authentication requirements (none found) and security considerations]

## API Categories

### Market Data APIs
[Trending coins, market information]

### Recommendation APIs  
[Personalized content and recommendations]

### System APIs
[Feature flags, configuration]

### Featured Content APIs
[Runners, featured coins]

## Rate Limiting & Best Practices
[Guidelines for responsible usage]

## Integration Patterns
[Common integration patterns and architectures]

## Business Use Cases
[Real-world applications and use cases]

## Error Handling
[Comprehensive error handling strategies]

## Code Examples
[Multi-language examples showing common patterns]

## Troubleshooting
[Common issues and solutions]

## API Reference Summary
[Quick reference table of all endpoints]

## Advanced Topics
[Performance optimization, caching strategies, etc.]

## Resources & Support
[Additional resources for developers]

Make this a comprehensive, professional guide that serves as the definitive resource for integrating with pump.fun APIs. Include practical examples and real-world advice.

Format as clean markdown suitable for a developer portal or comprehensive README.
"""
        
        try:
            response = requests.post(
                'https://openrouter.ai/api/v1/chat/completions',
                headers={
                    'Authorization': f'Bearer {OPENROUTER_API_KEY}',
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://cipher-spy.com',
                    'X-Title': 'Cipher-Spy Master Documentation Generator'
                },
                json={
                    'model': 'anthropic/claude-3.5-sonnet',
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 8000,
                    'temperature': 0.1
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f"❌ Master documentation generation failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Master documentation generation error: {e}")
            return None


async def main():
    """Main entry point."""
    generator = AIPoweredDocumentationGenerator()
    await generator.generate_comprehensive_documentation()
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
