"""
SQLAlchemy database models for Cipher-Spy.

Defines the database schema for storing scan results, discovered endpoints,
vulnerabilities, and other reconnaissance data.
"""

from datetime import datetime
from typing import Dict, Any, Optional
import json

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

Base = declarative_base()


class Target(Base):
    """Target website or application."""
    __tablename__ = "targets"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(String(2048), nullable=False, index=True)
    name = Column(String(255))
    description = Column(Text)
    scope = Column(JSON)  # List of allowed domains/URLs
    credentials = Column(JSON)  # Authentication credentials (encrypted)
    headers = Column(JSON)  # Custom headers
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    scans = relationship("Scan", back_populates="target", cascade="all, delete-orphan")


class Scan(Base):
    """Security scan instance."""
    __tablename__ = "scans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    target_id = Column(UUID(as_uuid=True), ForeignKey("targets.id"), nullable=False)
    status = Column(String(50), nullable=False, default="pending")
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    error_message = Column(Text)

    # Configuration
    max_depth = Column(Integer, default=5)
    max_pages = Column(Integer, default=1000)
    safe_mode = Column(Boolean, default=True)
    respect_robots = Column(Boolean, default=True)

    # Progress tracking
    current_agent = Column(String(100))
    overall_progress = Column(Float, default=0.0)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    target = relationship("Target", back_populates="scans")
    pages = relationship("Page", back_populates="scan", cascade="all, delete-orphan")
    endpoints = relationship("Endpoint", back_populates="scan", cascade="all, delete-orphan")
    technologies = relationship("Technology", back_populates="scan", cascade="all, delete-orphan")
    vulnerabilities = relationship("Vulnerability", back_populates="scan", cascade="all, delete-orphan")
    exploit_plans = relationship("ExploitPlan", back_populates="scan", cascade="all, delete-orphan")


class Page(Base):
    """Discovered web page."""
    __tablename__ = "pages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    url = Column(String(2048), nullable=False, index=True)
    title = Column(String(500))
    status_code = Column(Integer)
    content_type = Column(String(100))
    content_hash = Column(String(64))  # SHA-256 hash of content
    screenshot_path = Column(String(500))

    # Page metadata
    meta_description = Column(Text)
    frameworks = Column(JSON)  # Detected frameworks
    link_count = Column(Integer, default=0)
    form_count = Column(Integer, default=0)
    button_count = Column(Integer, default=0)

    discovered_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    scan = relationship("Scan", back_populates="pages")


class Endpoint(Base):
    """Discovered API endpoint."""
    __tablename__ = "endpoints"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    url = Column(String(2048), nullable=False, index=True)
    method = Column(String(10), nullable=False)

    # Request/Response data
    parameters = Column(JSON)  # Query and body parameters
    headers = Column(JSON)  # Request headers
    request_body_schema = Column(JSON)  # Inferred request schema
    response_schema = Column(JSON)  # Inferred response schema
    response_status = Column(Integer)
    response_content_type = Column(String(100))
    response_sample = Column(Text)  # Sample response data

    # Metadata
    authentication_required = Column(Boolean, default=False)
    discovered_from = Column(String(2048))  # Page URL where discovered
    first_seen = Column(DateTime, default=datetime.utcnow)
    last_seen = Column(DateTime, default=datetime.utcnow)
    request_count = Column(Integer, default=1)

    # Relationships
    scan = relationship("Scan", back_populates="endpoints")


class Technology(Base):
    """Detected technology stack component."""
    __tablename__ = "technologies"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    name = Column(String(255), nullable=False, index=True)
    version = Column(String(100))
    category = Column(String(100))
    confidence = Column(Float, default=0.0)
    detection_method = Column(String(100))
    details = Column(JSON)

    discovered_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    scan = relationship("Scan", back_populates="technologies")


class Vulnerability(Base):
    """Identified vulnerability."""
    __tablename__ = "vulnerabilities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    name = Column(String(500), nullable=False)
    description = Column(Text, nullable=False)
    severity = Column(String(50), default="unknown")
    cve_id = Column(String(20))
    affected_technology = Column(String(255))
    confidence = Column(Float, default=0.0)
    evidence = Column(JSON)  # List of evidence

    discovered_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    scan = relationship("Scan", back_populates="vulnerabilities")
    exploit_plans = relationship("ExploitPlan", back_populates="vulnerability", cascade="all, delete-orphan")


class ExploitPlan(Base):
    """Generated exploit plan."""
    __tablename__ = "exploit_plans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    vulnerability_id = Column(UUID(as_uuid=True), ForeignKey("vulnerabilities.id"), nullable=False)
    name = Column(String(500), nullable=False)
    description = Column(Text, nullable=False)
    steps = Column(JSON)  # List of execution steps
    payloads = Column(JSON)  # List of exploit payloads
    expected_outcome = Column(Text)
    risk_level = Column(String(50), default="medium")
    status = Column(String(50), default="pending")

    # Approval tracking
    created_at = Column(DateTime, default=datetime.utcnow)
    approved_at = Column(DateTime)
    executed_at = Column(DateTime)
    results = Column(JSON)  # Execution results

    # Relationships
    scan = relationship("Scan", back_populates="exploit_plans")
    vulnerability = relationship("Vulnerability", back_populates="exploit_plans")


class AgentExecution(Base):
    """Agent execution tracking."""
    __tablename__ = "agent_executions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    agent_id = Column(String(100), nullable=False)
    agent_type = Column(String(100), nullable=False)
    status = Column(String(50), nullable=False)
    progress = Column(Float, default=0.0)
    current_task = Column(String(500))
    error_message = Column(Text)

    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    execution_metadata = Column(JSON)

    # Relationships
    scan = relationship("Scan")


class NetworkRequest(Base):
    """Captured network request."""
    __tablename__ = "network_requests"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    request_id = Column(String(100), nullable=False, index=True)
    url = Column(String(2048), nullable=False)
    method = Column(String(10), nullable=False)
    headers = Column(JSON)
    post_data = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    scan = relationship("Scan")


class NetworkResponse(Base):
    """Captured network response."""
    __tablename__ = "network_responses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    request_id = Column(String(100), nullable=False, index=True)
    url = Column(String(2048), nullable=False)
    status = Column(Integer, nullable=False)
    headers = Column(JSON)
    body = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    scan = relationship("Scan")


class Form(Base):
    """Discovered form."""
    __tablename__ = "forms"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scan_id = Column(UUID(as_uuid=True), ForeignKey("scans.id"), nullable=False)
    page_url = Column(String(2048), nullable=False)
    action = Column(String(2048))
    method = Column(String(10))
    fields = Column(JSON)  # List of form fields
    submit_buttons = Column(JSON)  # List of submit buttons

    discovered_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    scan = relationship("Scan")
