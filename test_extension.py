#!/usr/bin/env python3
"""
Chrome Extension Test Script

Tests the Cipher-Spy Chrome extension functionality and verifies
that all components are working correctly.
"""

import json
import time
from pathlib import Path


def test_extension_files():
    """Test that all extension files are present and valid."""
    print("🧪 Testing Chrome Extension Files")
    print("="*40)
    
    extension_dir = Path("chrome_extension")
    
    # Test manifest.json
    print("📋 Testing manifest.json...")
    try:
        with open(extension_dir / "manifest.json", "r") as f:
            manifest = json.load(f)
        
        # Check required fields
        required_fields = ["manifest_version", "name", "version", "permissions"]
        for field in required_fields:
            if field in manifest:
                print(f"   ✅ {field}: {manifest[field]}")
            else:
                print(f"   ❌ Missing: {field}")
                return False
        
        # Check permissions
        permissions = manifest.get("permissions", [])
        required_perms = ["activeTab", "storage", "webRequest"]
        for perm in required_perms:
            if perm in permissions:
                print(f"   ✅ Permission: {perm}")
            else:
                print(f"   ⚠️ Missing permission: {perm}")
        
    except Exception as e:
        print(f"   ❌ Error reading manifest.json: {e}")
        return False
    
    # Test JavaScript files for syntax errors
    print("\n🔧 Testing JavaScript files...")
    js_files = [
        "background.js",
        "content.js", 
        "popup/popup.js",
        "devtools/devtools.js",
        "devtools/panel.js"
    ]
    
    for js_file in js_files:
        file_path = extension_dir / js_file
        if file_path.exists():
            try:
                with open(file_path, "r") as f:
                    content = f.read()
                
                # Basic syntax checks
                if "chrome.runtime" in content:
                    print(f"   ✅ {js_file} - Chrome API usage detected")
                elif "addEventListener" in content:
                    print(f"   ✅ {js_file} - Event listeners detected")
                else:
                    print(f"   ⚠️ {js_file} - No Chrome API or events detected")
                
                # Check for common errors
                if "sendResponse" in content and "return true" not in content:
                    print(f"   ⚠️ {js_file} - Missing 'return true' for async messages")
                
            except Exception as e:
                print(f"   ❌ Error reading {js_file}: {e}")
        else:
            print(f"   ❌ Missing: {js_file}")
    
    # Test HTML files
    print("\n📄 Testing HTML files...")
    html_files = [
        "popup/popup.html",
        "devtools/devtools.html",
        "devtools/panel.html"
    ]
    
    for html_file in html_files:
        file_path = extension_dir / html_file
        if file_path.exists():
            try:
                with open(file_path, "r") as f:
                    content = f.read()
                
                if "<!DOCTYPE html>" in content:
                    print(f"   ✅ {html_file} - Valid HTML structure")
                else:
                    print(f"   ⚠️ {html_file} - Missing DOCTYPE")
                
            except Exception as e:
                print(f"   ❌ Error reading {html_file}: {e}")
        else:
            print(f"   ❌ Missing: {html_file}")
    
    # Test icon files
    print("\n🎨 Testing icon files...")
    icon_sizes = [16, 32, 48, 128]
    for size in icon_sizes:
        icon_path = extension_dir / f"icons/icon{size}.png"
        if icon_path.exists():
            file_size = icon_path.stat().st_size
            if file_size > 0:
                print(f"   ✅ icon{size}.png ({file_size} bytes)")
            else:
                print(f"   ❌ icon{size}.png is empty")
        else:
            print(f"   ❌ Missing: icon{size}.png")
    
    print("\n✅ Extension file tests completed!")
    return True


def test_background_script_logic():
    """Test background script logic."""
    print("\n🔄 Testing Background Script Logic")
    print("="*40)
    
    try:
        with open("chrome_extension/background.js", "r") as f:
            content = f.read()
        
        # Check for proper message handling
        if "chrome.runtime.onMessage.addListener" in content:
            print("   ✅ Message listener registered")
        else:
            print("   ❌ No message listener found")
            return False
        
        if "return true" in content:
            print("   ✅ Async message handling enabled")
        else:
            print("   ⚠️ May have issues with async messages")
        
        # Check for initialization
        if "initializeExtension" in content:
            print("   ✅ Extension initialization function found")
        else:
            print("   ⚠️ No initialization function found")
        
        # Check for error handling
        if "try {" in content and "catch" in content:
            print("   ✅ Error handling implemented")
        else:
            print("   ⚠️ Limited error handling")
        
        # Check for storage usage
        if "chrome.storage.local" in content:
            print("   ✅ Local storage usage detected")
        else:
            print("   ❌ No storage usage found")
        
        print("   ✅ Background script logic tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing background script: {e}")
        return False


def test_popup_script_logic():
    """Test popup script logic."""
    print("\n🖼️ Testing Popup Script Logic")
    print("="*40)
    
    try:
        with open("chrome_extension/popup/popup.js", "r") as f:
            content = f.read()
        
        # Check for DOM ready handling
        if "DOMContentLoaded" in content:
            print("   ✅ DOM ready event handling")
        else:
            print("   ⚠️ No DOM ready handling found")
        
        # Check for message sending
        if "chrome.runtime.sendMessage" in content:
            print("   ✅ Message sending to background script")
        else:
            print("   ❌ No message sending found")
        
        # Check for retry logic
        if "sendMessageWithRetry" in content:
            print("   ✅ Message retry logic implemented")
        else:
            print("   ⚠️ No retry logic found")
        
        # Check for error handling
        if "showError" in content and "showSuccess" in content:
            print("   ✅ User feedback functions implemented")
        else:
            print("   ⚠️ Limited user feedback")
        
        print("   ✅ Popup script logic tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing popup script: {e}")
        return False


def generate_installation_instructions():
    """Generate installation instructions."""
    print("\n📋 Installation Instructions")
    print("="*40)
    
    instructions = """
🚀 How to Install the Cipher-Spy Chrome Extension:

1. Open Chrome and navigate to: chrome://extensions/

2. Enable "Developer mode" (toggle in top-right corner)

3. Click "Load unpacked" button

4. Navigate to and select the 'chrome_extension' folder

5. The extension should appear in your extensions list

6. Pin the extension to your toolbar for easy access

🔧 Optional: Start the backend server
   python -m src.main

✅ Test the extension:
   - Click the extension icon
   - Try starting/stopping monitoring
   - Browse a website to capture traffic
   - Check the DevTools panel (F12 → Cipher-Spy tab)

🆘 Troubleshooting:
   - If extension won't load: Check console for errors
   - If no traffic captured: Check permissions and filters
   - If backend connection fails: Verify server is running
"""
    
    print(instructions)


def main():
    """Main test function."""
    print("🔍 Cipher-Spy Chrome Extension Test Suite")
    print("="*50)
    
    success = True
    
    # Run tests
    success &= test_extension_files()
    success &= test_background_script_logic()
    success &= test_popup_script_logic()
    
    if success:
        print("\n🎉 All tests passed! Extension is ready for installation.")
        generate_installation_instructions()
        return 0
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
