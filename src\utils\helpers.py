"""
Helper utilities for Cipher-Spy.

Provides common utility functions used across the application.
"""

import uuid
import re
from datetime import datetime
from urllib.parse import urlparse
from typing import Optional


def generate_uuid() -> str:
    """
    Generate a UUID string.
    
    Returns:
        str: UUID string
    """
    return str(uuid.uuid4())


def format_timestamp(dt: Optional[datetime] = None) -> str:
    """
    Format timestamp for display.
    
    Args:
        dt: Datetime object (defaults to now)
        
    Returns:
        str: Formatted timestamp
    """
    if dt is None:
        dt = datetime.utcnow()
    return dt.strftime('%Y-%m-%d %H:%M:%S UTC')


def parse_user_agent(user_agent: str) -> dict:
    """
    Parse user agent string.
    
    Args:
        user_agent: User agent string
        
    Returns:
        dict: Parsed user agent info
    """
    # Basic user agent parsing
    info = {
        'browser': 'unknown',
        'version': 'unknown',
        'os': 'unknown'
    }
    
    if 'Chrome' in user_agent:
        info['browser'] = 'Chrome'
    elif 'Firefox' in user_agent:
        info['browser'] = 'Firefox'
    elif 'Safari' in user_agent:
        info['browser'] = 'Safari'
    
    if 'Windows' in user_agent:
        info['os'] = 'Windows'
    elif 'Mac' in user_agent:
        info['os'] = 'macOS'
    elif 'Linux' in user_agent:
        info['os'] = 'Linux'
    
    return info


def extract_domain(url: str) -> Optional[str]:
    """
    Extract domain from URL.
    
    Args:
        url: URL string
        
    Returns:
        Optional[str]: Domain or None if invalid
    """
    try:
        parsed = urlparse(url)
        return parsed.netloc
    except Exception:
        return None


def is_valid_ip(ip: str) -> bool:
    """
    Check if string is a valid IP address.
    
    Args:
        ip: IP address string
        
    Returns:
        bool: True if valid IP
    """
    # Basic IPv4 regex
    pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if re.match(pattern, ip):
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    return False
