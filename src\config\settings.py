"""
Application settings and configuration management.

Uses Pydantic Settings for type-safe configuration with environment variable support.
Provides validation and default values for all configuration options.
"""

from functools import lru_cache
from typing import List, Optional
from pathlib import Path

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden via environment variables.
    Boolean values accept: true/false, 1/0, yes/no, on/off.
    """
    
    # Database Configuration
    database_url: str = Field(
        default="postgresql://cipher_user:cipher_pass@localhost:5432/cipher_spy",
        description="PostgreSQL database connection URL"
    )
    neo4j_uri: str = Field(
        default="bolt://localhost:7687",
        description="Neo4j database connection URI"
    )
    neo4j_user: str = Field(default="neo4j", description="Neo4j username")
    neo4j_password: str = Field(default="cipher_neo4j", description="Neo4j password")
    
    # LLM Configuration
    openrouter_api_key: Optional[str] = Field(
        default=None,
        description="OpenRouter API key for premium LLM access"
    )
    default_llm_model: str = Field(
        default="openai/gpt-4-turbo-preview",
        description="Default LLM model to use"
    )
    fallback_llm_model: str = Field(
        default="openai/gpt-3.5-turbo",
        description="Fallback LLM model if default fails"
    )
    local_llm_enabled: bool = Field(
        default=False,
        description="Enable local LLM support"
    )
    local_llm_model_path: Optional[str] = Field(
        default=None,
        description="Path to local LLM model file"
    )
    
    # Application Settings
    environment: str = Field(default="development", description="Application environment")
    log_level: str = Field(default="INFO", description="Logging level")
    debug: bool = Field(default=False, description="Enable debug mode")
    secret_key: str = Field(
        default="your-secret-key-here",
        description="Secret key for cryptographic operations"
    )
    api_v1_str: str = Field(default="/api/v1", description="API version prefix")
    
    # Crawling Configuration
    max_crawl_depth: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum crawling depth"
    )
    max_pages_per_domain: int = Field(
        default=1000,
        ge=1,
        le=10000,
        description="Maximum pages to crawl per domain"
    )
    crawl_delay_ms: int = Field(
        default=1000,
        ge=0,
        le=10000,
        description="Delay between requests in milliseconds"
    )
    respect_robots_txt: bool = Field(
        default=True,
        description="Respect robots.txt directives"
    )
    user_agent: str = Field(
        default="CipherSpy/1.0 (Security Research Tool)",
        description="User agent string for requests"
    )
    
    # Security Settings
    safe_mode: bool = Field(
        default=True,
        description="Enable safe mode (no destructive actions)"
    )
    require_approval_for_exploits: bool = Field(
        default=True,
        description="Require human approval before executing exploits"
    )
    max_exploit_attempts: int = Field(
        default=3,
        ge=1,
        le=10,
        description="Maximum exploit attempts per vulnerability"
    )
    exploit_timeout_seconds: int = Field(
        default=300,
        ge=30,
        le=3600,
        description="Timeout for exploit execution"
    )
    
    # Fingerprinting Configuration
    wappalyzer_timeout: int = Field(
        default=30,
        ge=5,
        le=120,
        description="Wappalyzer scan timeout in seconds"
    )
    wafw00f_timeout: int = Field(
        default=60,
        ge=10,
        le=300,
        description="WafW00f scan timeout in seconds"
    )
    enable_aggressive_fingerprinting: bool = Field(
        default=False,
        description="Enable aggressive fingerprinting techniques"
    )
    
    # Knowledge Base Settings
    exploitdb_update_interval_hours: int = Field(
        default=24,
        ge=1,
        le=168,
        description="Exploit-DB update interval in hours"
    )
    vector_similarity_threshold: float = Field(
        default=0.7,
        ge=0.1,
        le=1.0,
        description="Vector similarity threshold for exploit matching"
    )
    max_exploit_recommendations: int = Field(
        default=10,
        ge=1,
        le=50,
        description="Maximum exploit recommendations to return"
    )
    
    # Storage Settings
    data_directory: Path = Field(
        default=Path("/app/data"),
        description="Data storage directory"
    )
    logs_directory: Path = Field(
        default=Path("/app/logs"),
        description="Logs storage directory"
    )
    screenshots_directory: Path = Field(
        default=Path("/app/data/screenshots"),
        description="Screenshots storage directory"
    )
    reports_directory: Path = Field(
        default=Path("/app/data/reports"),
        description="Reports storage directory"
    )
    
    # Performance Settings
    max_concurrent_crawlers: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum concurrent crawler instances"
    )
    database_pool_size: int = Field(
        default=20,
        ge=5,
        le=100,
        description="Database connection pool size"
    )
    neo4j_pool_size: int = Field(
        default=10,
        ge=1,
        le=50,
        description="Neo4j connection pool size"
    )
    vector_batch_size: int = Field(
        default=100,
        ge=10,
        le=1000,
        description="Vector processing batch size"
    )
    
    # Monitoring & Health
    enable_metrics: bool = Field(
        default=True,
        description="Enable Prometheus metrics"
    )
    metrics_port: int = Field(
        default=9090,
        ge=1024,
        le=65535,
        description="Metrics server port"
    )
    health_check_interval: int = Field(
        default=30,
        ge=5,
        le=300,
        description="Health check interval in seconds"
    )
    
    # Development Settings
    reload_on_change: bool = Field(
        default=False,
        description="Reload application on code changes"
    )
    enable_cors: bool = Field(
        default=True,
        description="Enable CORS middleware"
    )
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins"
    )
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level is one of the standard levels."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    @validator("environment")
    def validate_environment(cls, v):
        """Validate environment is one of the expected values."""
        valid_envs = ["development", "testing", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f"Environment must be one of: {valid_envs}")
        return v.lower()
    
    def create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.data_directory,
            self.logs_directory,
            self.screenshots_directory,
            self.reports_directory
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached settings instance.
    
    Uses LRU cache to ensure settings are loaded only once.
    
    Returns:
        Settings: Application settings instance
    """
    settings = Settings()
    settings.create_directories()
    return settings
