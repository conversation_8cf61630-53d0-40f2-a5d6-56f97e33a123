# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Agent Orchestration
langchain==0.0.350
langgraph==0.0.20
langchain-openai==0.0.5
langchain-community==0.0.10

# Web Crawling & Automation
playwright==1.40.0
requests==2.31.0
httpx==0.25.2
beautifulsoup4==4.12.2
lxml==4.9.3

# Fingerprinting Tools
python-Wappalyzer==0.3.1
wafw00f==2.2.0
python-nmap==0.7.1

# Database & Storage
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Knowledge Graph & Vector Search
neo4j==5.15.0
# graphiti-core==0.3.0  # Optional - for advanced knowledge graph features
chromadb==0.4.18
sentence-transformers==2.2.2
numpy==1.24.4
faiss-cpu==1.7.4

# LLM Integration
openai==1.6.1
anthropic==0.7.8
tiktoken==0.5.2

# Security & Utilities
cryptography==41.0.8
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Logging & Monitoring
structlog==23.2.0
rich==13.7.0
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Data Processing
pandas==2.1.4
jinja2==3.1.2
python-dotenv==1.0.0
click==8.1.7
typer==0.9.0

# Network & Security Analysis
scapy==2.5.0
dnspython==2.4.2
python-whois==0.8.0
aiohttp==3.9.1

# Report Generation
reportlab==4.0.7
markdown==3.5.1
weasyprint==60.2

# Additional dependencies for crawler
urllib3==2.1.0
certifi==2023.11.17
