#!/usr/bin/env python3
"""
Chrome Extension API Routes for Cipher-Spy

Provides API endpoints for the Chrome extension to communicate with
the Cipher-Spy backend for real-time traffic analysis and processing.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import asyncio

from ...api_discovery.discovery_engine import Discovery<PERSON>ng<PERSON>
from ...api_discovery.target_config import TargetConfig
from ...api_discovery.universal_analyzer import UniversalAPIAnalyzer
from ...models.extension_models import (
    ExtensionRequest,
    ExtensionSession,
    AnalysisRequest,
    SessionAnalysisRequest
)
from ...services.traffic_processor import TrafficProcessor
from ...services.extension_bridge import ExtensionBridge
from ...utils.traffic_analyzer import TrafficAnalyzer

router = APIRouter(tags=["extension"])

# Global services
traffic_processor = TrafficProcessor()
extension_bridge = ExtensionBridge()
traffic_analyzer = TrafficAnalyzer()
discovery_engine = DiscoveryEngine()

# Active sessions storage
active_sessions: Dict[str, ExtensionSession] = {}


@router.post("/analyze-request")
async def analyze_request(request: AnalysisRequest):
    """
    Analyze a single network request from the Chrome extension.

    Args:
        request: Request analysis data from extension

    Returns:
        Analysis results for the request
    """
    try:
        session_id = request.session_id
        request_data = request.request

        # Get or create session
        if session_id not in active_sessions:
            active_sessions[session_id] = ExtensionSession(
                session_id=session_id,
                start_time=datetime.now(),
                requests=[],
                analysis_results={}
            )

        session = active_sessions[session_id]
        session.requests.append(request_data)

        # Analyze the request
        analysis = await traffic_analyzer.analyze_single_request(request_data)

        # Store analysis
        session.analysis_results[request_data.id] = analysis

        return JSONResponse({
            "success": True,
            "request_id": request_data.id,
            "analysis": analysis,
            "session_stats": {
                "total_requests": len(session.requests),
                "session_duration": (datetime.now() - session.start_time).total_seconds()
            }
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.post("/analyze-session")
async def analyze_session(request: SessionAnalysisRequest, background_tasks: BackgroundTasks):
    """
    Perform comprehensive analysis of an entire browsing session.

    Args:
        request: Session analysis request with all captured requests
        background_tasks: FastAPI background tasks

    Returns:
        Session analysis results
    """
    try:
        session_id = request.session_id
        requests = request.requests

        # Update session data
        if session_id not in active_sessions:
            active_sessions[session_id] = ExtensionSession(
                session_id=session_id,
                start_time=datetime.now(),
                requests=[],
                analysis_results={}
            )

        session = active_sessions[session_id]
        session.requests = requests

        # Perform comprehensive analysis
        analysis_results = await traffic_analyzer.analyze_session(requests)

        # Extract target domains for API discovery
        domains = traffic_analyzer.extract_target_domains(requests)

        # Start background API discovery for discovered domains
        for domain in domains:
            background_tasks.add_task(
                perform_background_discovery,
                domain,
                session_id,
                requests
            )

        # Store session analysis
        session.analysis_results['session_analysis'] = analysis_results
        session.last_analysis = datetime.now()

        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "analysis": analysis_results,
            "discovered_domains": domains,
            "background_discovery_started": len(domains) > 0,
            "session_stats": {
                "total_requests": len(requests),
                "api_requests": len([r for r in requests if traffic_analyzer.is_api_request(r)]),
                "unique_domains": len(domains),
                "session_duration": (datetime.now() - session.start_time).total_seconds()
            }
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Session analysis failed: {str(e)}")


@router.post("/sessions/{session_id}/requests")
async def add_request_to_session(session_id: str, request: Dict[str, Any]):
    """
    Add a captured request to a session.

    Args:
        session_id: Extension session ID
        request: Request data to add

    Returns:
        Success confirmation
    """
    try:
        if session_id not in active_sessions:
            # Auto-create session if it doesn't exist
            active_sessions[session_id] = ExtensionSession(
                session_id=session_id,
                start_time=datetime.now(),
                requests=[],
                analysis_results={}
            )

        session = active_sessions[session_id]
        session.requests.append(request)

        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "request_count": len(session.requests),
            "message": "Request added successfully"
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add request: {str(e)}")


@router.get("/sessions/{session_id}/summary")
async def get_session_summary(session_id: str):
    """
    Get a summary of session data.

    Args:
        session_id: Extension session ID

    Returns:
        Session summary
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = active_sessions[session_id]

    # Calculate summary statistics
    api_requests = [req for req in session.requests if traffic_analyzer.is_api_request(req)]
    unique_domains = set()

    for req in session.requests:
        url = req.get('url') if isinstance(req, dict) else getattr(req, 'url', '')
        if url:
            unique_domains.add(traffic_analyzer.extract_domain(url))

    return JSONResponse({
        "session_id": session_id,
        "start_time": session.start_time.isoformat(),
        "duration": (datetime.now() - session.start_time).total_seconds(),
        "total_requests": len(session.requests),
        "api_requests": len(api_requests),
        "unique_domains": len(unique_domains),
        "domains": list(unique_domains),
        "has_analysis": bool(session.analysis_results),
        "metadata": getattr(session, 'metadata', {})
    })


@router.get("/session/{session_id}")
async def get_session(session_id: str):
    """
    Get session information and analysis results.

    Args:
        session_id: Extension session ID

    Returns:
        Session data and analysis results
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = active_sessions[session_id]

    return JSONResponse({
        "session_id": session_id,
        "start_time": session.start_time.isoformat(),
        "last_analysis": session.last_analysis.isoformat() if session.last_analysis else None,
        "request_count": len(session.requests),
        "analysis_count": len(session.analysis_results),
        "session_duration": (datetime.now() - session.start_time).total_seconds(),
        "has_analysis": 'session_analysis' in session.analysis_results
    })


@router.get("/session/{session_id}/analysis")
async def get_session_analysis(session_id: str):
    """
    Get detailed analysis results for a session.

    Args:
        session_id: Extension session ID

    Returns:
        Detailed analysis results
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = active_sessions[session_id]

    if 'session_analysis' not in session.analysis_results:
        raise HTTPException(status_code=404, detail="No session analysis available")

    return JSONResponse({
        "session_id": session_id,
        "analysis": session.analysis_results['session_analysis'],
        "individual_analyses": {
            req_id: analysis for req_id, analysis in session.analysis_results.items()
            if req_id != 'session_analysis'
        }
    })


@router.post("/session/{session_id}/discover")
async def trigger_discovery(session_id: str, background_tasks: BackgroundTasks):
    """
    Trigger API discovery for a specific session.

    Args:
        session_id: Extension session ID
        background_tasks: FastAPI background tasks

    Returns:
        Discovery trigger confirmation
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = active_sessions[session_id]

    # Extract domains from session requests
    domains = traffic_analyzer.extract_target_domains(session.requests)

    if not domains:
        raise HTTPException(status_code=400, detail="No discoverable domains found in session")

    # Start discovery for each domain
    discovery_tasks = []
    for domain in domains:
        task_id = f"{session_id}_{domain}_{datetime.now().timestamp()}"
        background_tasks.add_task(
            perform_background_discovery,
            domain,
            session_id,
            session.requests,
            task_id
        )
        discovery_tasks.append(task_id)

    return JSONResponse({
        "success": True,
        "session_id": session_id,
        "domains": domains,
        "discovery_tasks": discovery_tasks,
        "message": f"Started API discovery for {len(domains)} domains"
    })


@router.post("/sessions")
async def create_session(request: Dict[str, Any]):
    """
    Create a new extension session.

    Args:
        request: Session creation request with session_id and metadata

    Returns:
        Created session information
    """
    try:
        session_id = request.get("session_id")
        metadata = request.get("metadata", {})

        if not session_id:
            raise HTTPException(status_code=400, detail="session_id is required")

        if session_id in active_sessions:
            # Return existing session
            session = active_sessions[session_id]
            return JSONResponse({
                "success": True,
                "session_id": session_id,
                "message": "Session already exists",
                "start_time": session.start_time.isoformat(),
                "request_count": len(session.requests)
            })

        # Create new session
        session = ExtensionSession(
            session_id=session_id,
            start_time=datetime.now(),
            requests=[],
            analysis_results={},
            metadata=metadata
        )

        active_sessions[session_id] = session

        return JSONResponse({
            "success": True,
            "session_id": session_id,
            "message": "Session created successfully",
            "start_time": session.start_time.isoformat(),
            "metadata": metadata
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Session creation failed: {str(e)}")


@router.get("/sessions")
async def list_sessions():
    """
    List all active extension sessions.

    Returns:
        List of active sessions with basic info
    """
    sessions_info = []

    for session_id, session in active_sessions.items():
        sessions_info.append({
            "session_id": session_id,
            "start_time": session.start_time.isoformat(),
            "request_count": len(session.requests),
            "duration": (datetime.now() - session.start_time).total_seconds(),
            "has_analysis": 'session_analysis' in session.analysis_results,
            "last_analysis": session.last_analysis.isoformat() if session.last_analysis else None
        })

    return JSONResponse({
        "active_sessions": len(sessions_info),
        "sessions": sessions_info
    })


@router.delete("/session/{session_id}")
async def delete_session(session_id: str):
    """
    Delete a session and its data.

    Args:
        session_id: Extension session ID

    Returns:
        Deletion confirmation
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    del active_sessions[session_id]

    return JSONResponse({
        "success": True,
        "message": f"Session {session_id} deleted"
    })


@router.post("/export-session/{session_id}")
async def export_session(session_id: str):
    """
    Export session data in various formats.

    Args:
        session_id: Extension session ID

    Returns:
        Exportable session data
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = active_sessions[session_id]

    # Create comprehensive export
    export_data = {
        "session_metadata": {
            "session_id": session_id,
            "start_time": session.start_time.isoformat(),
            "export_time": datetime.now().isoformat(),
            "duration": (datetime.now() - session.start_time).total_seconds(),
            "request_count": len(session.requests)
        },
        "requests": [req.dict() if hasattr(req, 'dict') else req for req in session.requests],
        "analysis_results": session.analysis_results,
        "discovered_apis": await extract_discovered_apis(session.requests),
        "summary": await generate_session_summary(session)
    }

    return JSONResponse(export_data)


@router.get("/health")
async def health_check():
    """
    Health check endpoint for the extension API.

    Returns:
        Service health status
    """
    return JSONResponse({
        "status": "healthy",
        "service": "cipher-spy-extension-api",
        "timestamp": datetime.now().isoformat(),
        "active_sessions": len(active_sessions),
        "version": "1.0.0"
    })


# Background tasks
async def perform_background_discovery(
    domain: str,
    session_id: str,
    requests: List[Any],
    task_id: Optional[str] = None
):
    """
    Perform API discovery in the background for a domain.

    Args:
        domain: Target domain for discovery
        session_id: Associated session ID
        requests: Session requests for context
        task_id: Optional task identifier
    """
    try:
        print(f"🔍 Starting background API discovery for {domain} (session: {session_id})")

        # Create target configuration from session data
        target_config = await create_target_config_from_session(domain, requests)

        # Perform discovery
        result = await discovery_engine.discover_target(
            target_config=target_config,
            analyzer_type='universal',
            generate_docs=True,
            generate_clients=False  # Skip client generation for background tasks
        )

        # Store discovery results in session
        if session_id in active_sessions:
            session = active_sessions[session_id]
            if 'discoveries' not in session.analysis_results:
                session.analysis_results['discoveries'] = {}

            session.analysis_results['discoveries'][domain] = {
                "discovery_result": result.dict() if hasattr(result, 'dict') else result,
                "discovery_time": datetime.now().isoformat(),
                "task_id": task_id
            }

        print(f"✅ Background discovery completed for {domain}")

    except Exception as e:
        print(f"❌ Background discovery failed for {domain}: {e}")

        # Store error in session
        if session_id in active_sessions:
            session = active_sessions[session_id]
            if 'discovery_errors' not in session.analysis_results:
                session.analysis_results['discovery_errors'] = {}

            session.analysis_results['discovery_errors'][domain] = {
                "error": str(e),
                "error_time": datetime.now().isoformat(),
                "task_id": task_id
            }


async def create_target_config_from_session(domain: str, requests: List[Any]) -> TargetConfig:
    """
    Create a target configuration based on session request data.

    Args:
        domain: Target domain
        requests: Session requests

    Returns:
        TargetConfig for the domain
    """
    # Extract domain-specific requests
    domain_requests = [
        req for req in requests
        if domain in (req.get('url', '') if isinstance(req, dict) else getattr(req, 'url', ''))
    ]

    # Extract API endpoints from requests
    api_endpoints = []
    for req in domain_requests:
        url = req.get('url') if isinstance(req, dict) else getattr(req, 'url', '')
        if traffic_analyzer.is_api_request(req):
            api_endpoints.append(url)

    # Create configuration
    config = TargetConfig.create_generic_config(domain, f"Extension Discovery for {domain}")

    # Enhance with session-specific data
    if api_endpoints:
        config.api_path_patterns.extend([
            pattern for pattern in set(api_endpoints)
            if pattern not in config.api_path_patterns
        ])

    return config


async def extract_discovered_apis(requests: List[Any]) -> Dict[str, Any]:
    """
    Extract discovered API information from requests.

    Args:
        requests: Session requests

    Returns:
        Discovered API information
    """
    apis = {
        "endpoints": [],
        "domains": set(),
        "methods": set(),
        "patterns": []
    }

    for req in requests:
        if traffic_analyzer.is_api_request(req):
            url = req.get('url') if isinstance(req, dict) else getattr(req, 'url', '')
            method = req.get('method') if isinstance(req, dict) else getattr(req, 'method', 'GET')

            apis["endpoints"].append(url)
            apis["domains"].add(traffic_analyzer.extract_domain(url))
            apis["methods"].add(method)

    # Convert sets to lists for JSON serialization
    apis["domains"] = list(apis["domains"])
    apis["methods"] = list(apis["methods"])

    return apis


async def generate_session_summary(session: ExtensionSession) -> Dict[str, Any]:
    """
    Generate a summary of the session.

    Args:
        session: Extension session

    Returns:
        Session summary
    """
    summary = {
        "duration": (datetime.now() - session.start_time).total_seconds(),
        "total_requests": len(session.requests),
        "api_requests": len([r for r in session.requests if traffic_analyzer.is_api_request(r)]),
        "unique_domains": len(set(
            traffic_analyzer.extract_domain(
                r.get('url') if isinstance(r, dict) else getattr(r, 'url', '')
            ) for r in session.requests
        )),
        "analysis_completed": 'session_analysis' in session.analysis_results,
        "discoveries_completed": 'discoveries' in session.analysis_results,
        "error_count": len(session.analysis_results.get('discovery_errors', {}))
    }

    return summary
