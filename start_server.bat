@echo off
echo 🔍 Cipher-Spy Backend Server
echo ==============================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python found

REM Try to start the server
echo 🚀 Starting Cipher-Spy Server...
echo.

python start_server.py

if errorlevel 1 (
    echo.
    echo ❌ Server failed to start
    echo 💡 Try: python -m src.main --mode server
    pause
    exit /b 1
)

echo.
echo ✅ Server stopped normally
pause
