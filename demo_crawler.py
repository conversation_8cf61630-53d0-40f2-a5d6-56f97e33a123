#!/usr/bin/env python3
"""
Demonstration script for Cipher-Spy Autonomous Web Reconnaissance Agent.

This script showcases the crawler's capabilities on pump.fun and provides
a comprehensive example of autonomous API discovery and reverse engineering.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.state import ScanState, TargetInfo
from src.agents.crawler import CrawlerAgent
from src.utils.logging import setup_logging


class CrawlerDemo:
    """Demonstration class for the Cipher-Spy crawler."""

    def __init__(self):
        self.results_dir = Path("demo_results")
        self.results_dir.mkdir(exist_ok=True)

    async def run_pump_fun_demo(self):
        """Run a comprehensive demo on pump.fun."""
        print("🎯 Cipher-Spy Autonomous Web Reconnaissance Demo")
        print("="*60)
        print("Target: https://pump.fun")
        print("Objective: Discover and document all accessible API endpoints")
        print("="*60)

        # Setup logging
        setup_logging(level="INFO", environment="development")

        # Create target configuration
        target = TargetInfo(
            url="https://pump.fun",
            domain="pump.fun",
            scope=["pump.fun", "*.pump.fun"]
        )

        # Create scan state with demo configuration
        state = ScanState(
            target=target,
            config={
                "max_crawl_depth": 2,  # Limited for demo
                "max_pages_per_domain": 20,  # Limited for demo
                "crawl_delay_ms": 3000,  # Be very respectful
                "respect_robots_txt": True,
                "safe_mode": True,
                "headless": True
            }
        )

        print(f"🆔 Scan ID: {state.scan_id}")
        print(f"⚙️  Configuration: Safe mode enabled, respectful crawling")

        try:
            # Initialize crawler agent
            crawler = CrawlerAgent(
                max_crawl_depth=2,
                max_pages_per_domain=20,
                crawl_delay_ms=3000,
                headless=True
            )

            print("\n🚀 Starting autonomous reconnaissance...")

            # Execute crawler
            start_time = datetime.now()
            final_state = await crawler.execute(state)
            end_time = datetime.now()

            duration = (end_time - start_time).total_seconds()

            # Generate comprehensive report
            await self._generate_demo_report(final_state, duration)

            print(f"\n✅ Demo completed in {duration:.2f} seconds")
            print(f"📁 Results saved to: {self.results_dir}")

        except ImportError as e:
            if "graphiti" in str(e).lower():
                print(f"\n⚠️  Graphiti not installed - running in basic mode")
                print("To enable advanced knowledge graph features:")
                print("  python setup_graphiti.py")
                print("\nContinuing with crawler-only demo...")
                # Continue without Graphiti
                return await self._run_basic_demo()
            else:
                print(f"\n💥 Demo failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        except Exception as e:
            print(f"\n💥 Demo failed: {e}")
            import traceback
            traceback.print_exc()
            return False

        return True

    async def _run_basic_demo(self):
        """Run basic demo without Graphiti dependencies."""
        print("🎯 Running Basic Crawler Demo (No Graphiti)")

        try:
            # Import only what we need
            from src.core.state import ScanState, TargetInfo
            from src.agents.crawler import CrawlerAgent
            from datetime import datetime
            from urllib.parse import urlparse

            # Create target
            target = TargetInfo(
                url="https://pump.fun",
                domain="pump.fun",
                scope=["pump.fun", "*.pump.fun"]
            )

            # Create scan state
            state = ScanState(
                target=target,
                config={
                    "max_crawl_depth": 2,
                    "max_pages_per_domain": 10,  # Smaller for basic demo
                    "crawl_delay_ms": 3000,
                    "safe_mode": True,
                    "headless": True
                }
            )

            print(f"🆔 Scan ID: {state.scan_id}")

            # Initialize crawler directly
            crawler = CrawlerAgent(
                max_crawl_depth=2,
                max_pages_per_domain=10,
                crawl_delay_ms=3000,
                headless=True
            )

            print("🚀 Starting basic crawler...")
            start_time = datetime.now()
            final_state = await crawler.execute(state)
            end_time = datetime.now()

            duration = (end_time - start_time).total_seconds()

            # Generate basic report
            await self._generate_demo_report(final_state, duration)

            print(f"\n✅ Basic demo completed in {duration:.2f} seconds")
            return True

        except Exception as e:
            print(f"💥 Basic demo failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def _generate_demo_report(self, state: ScanState, duration: float):
        """Generate a comprehensive demo report."""
        print("\n📊 RECONNAISSANCE RESULTS")
        print("="*60)

        # Basic statistics
        pages_count = len(state.pages)
        endpoints_count = len(state.endpoints)

        print(f"📄 Pages Discovered: {pages_count}")
        print(f"🔗 API Endpoints Found: {endpoints_count}")
        print(f"⏱️  Scan Duration: {duration:.2f} seconds")

        # Page analysis
        if state.pages:
            print(f"\n📄 PAGE INVENTORY ({len(state.pages)} pages)")
            print("-" * 40)

            for i, page in enumerate(state.pages, 1):
                print(f"{i:2d}. {page.url}")
                if page.title:
                    print(f"    📝 Title: {page.title}")
                if page.status_code:
                    print(f"    📊 Status: {page.status_code}")
                if page.screenshot_path:
                    print(f"    📸 Screenshot: {page.screenshot_path}")
                print()

        # API endpoint analysis
        if state.endpoints:
            print(f"\n🔗 API ENDPOINT DISCOVERY ({len(state.endpoints)} endpoints)")
            print("-" * 50)

            # Group endpoints by method
            methods = {}
            for endpoint in state.endpoints:
                method = endpoint.method
                if method not in methods:
                    methods[method] = []
                methods[method].append(endpoint)

            for method, endpoints in methods.items():
                print(f"\n{method} Endpoints ({len(endpoints)}):")
                for endpoint in endpoints:
                    print(f"  • {endpoint.url}")
                    if endpoint.response_status:
                        print(f"    Status: {endpoint.response_status}")
                    if endpoint.response_content_type:
                        print(f"    Content-Type: {endpoint.response_content_type}")
                    if endpoint.parameters:
                        print(f"    Parameters: {len(endpoint.parameters)} detected")

        # Agent performance
        print(f"\n🤖 AGENT PERFORMANCE")
        print("-" * 30)

        for agent_id, agent in state.agents.items():
            status_emoji = "✅" if agent.status == "completed" else "❌" if agent.status == "failed" else "⏳"
            print(f"{status_emoji} {agent_id}: {agent.status} ({agent.progress:.1%})")

            if agent.current_task:
                print(f"   Task: {agent.current_task}")
            if agent.error_message:
                print(f"   Error: {agent.error_message}")

        # Save detailed JSON report
        await self._save_json_report(state, duration)

        # Generate API documentation
        await self._generate_api_docs(state.endpoints)

        # Generate cURL commands
        await self._generate_curl_commands(state.endpoints)

    async def _save_json_report(self, state: ScanState, duration: float):
        """Save detailed JSON report."""
        report = {
            "scan_metadata": {
                "scan_id": state.scan_id,
                "target_url": state.target.url,
                "target_domain": state.target.domain,
                "scan_duration_seconds": duration,
                "timestamp": datetime.now().isoformat(),
                "status": state.status
            },
            "discovery_summary": {
                "pages_discovered": len(state.pages),
                "endpoints_discovered": len(state.endpoints),
                "technologies_detected": len(state.technologies),
                "vulnerabilities_found": len(state.vulnerabilities)
            },
            "pages": [
                {
                    "url": page.url,
                    "title": page.title,
                    "status_code": page.status_code,
                    "content_type": page.content_type,
                    "screenshot_path": page.screenshot_path,
                    "discovered_at": page.discovered_at.isoformat() if page.discovered_at else None
                }
                for page in state.pages
            ],
            "endpoints": [
                {
                    "url": endpoint.url,
                    "method": endpoint.method,
                    "parameters": endpoint.parameters,
                    "headers": endpoint.headers,
                    "response_status": endpoint.response_status,
                    "response_content_type": endpoint.response_content_type,
                    "response_sample": endpoint.response_sample[:500] if endpoint.response_sample else None,
                    "discovered_from": endpoint.discovered_from,
                    "discovered_at": endpoint.discovered_at.isoformat() if endpoint.discovered_at else None
                }
                for endpoint in state.endpoints
            ],
            "agent_performance": {
                agent_id: {
                    "status": agent.status,
                    "progress": agent.progress,
                    "current_task": agent.current_task,
                    "error_message": agent.error_message
                }
                for agent_id, agent in state.agents.items()
            }
        }

        report_file = self.results_dir / f"scan_report_{state.scan_id}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        print(f"💾 Detailed report saved: {report_file}")

    async def _generate_api_docs(self, endpoints):
        """Generate API documentation."""
        if not endpoints:
            return

        docs = []
        docs.append("# API Endpoint Documentation")
        docs.append(f"Generated by Cipher-Spy on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        docs.append("")

        # Group by method
        methods = {}
        for endpoint in endpoints:
            method = endpoint.method
            if method not in methods:
                methods[method] = []
            methods[method].append(endpoint)

        for method, method_endpoints in methods.items():
            docs.append(f"## {method} Endpoints")
            docs.append("")

            for endpoint in method_endpoints:
                docs.append(f"### {method} {endpoint.url}")
                docs.append("")

                if endpoint.response_status:
                    docs.append(f"**Status Code:** {endpoint.response_status}")

                if endpoint.response_content_type:
                    docs.append(f"**Content-Type:** {endpoint.response_content_type}")

                if endpoint.parameters:
                    docs.append("**Parameters:**")
                    for param, value in endpoint.parameters.items():
                        docs.append(f"- `{param}`: {value}")

                if endpoint.response_sample:
                    docs.append("**Sample Response:**")
                    docs.append("```json")
                    docs.append(endpoint.response_sample[:500])
                    docs.append("```")

                docs.append("")

        docs_file = self.results_dir / "api_documentation.md"
        with open(docs_file, 'w') as f:
            f.write('\n'.join(docs))

        print(f"📚 API documentation saved: {docs_file}")

    async def _generate_curl_commands(self, endpoints):
        """Generate cURL commands for testing."""
        if not endpoints:
            return

        commands = []
        commands.append("#!/bin/bash")
        commands.append("# cURL commands for discovered API endpoints")
        commands.append(f"# Generated by Cipher-Spy on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        commands.append("")

        for endpoint in endpoints:
            commands.append(f"# {endpoint.method} {endpoint.url}")

            curl_cmd = f"curl -X {endpoint.method}"

            # Add headers
            if endpoint.headers:
                for header, value in endpoint.headers.items():
                    if header.lower() not in ['host', 'content-length']:
                        curl_cmd += f" -H '{header}: {value}'"

            # Add URL
            curl_cmd += f" '{endpoint.url}'"

            commands.append(curl_cmd)
            commands.append("")

        curl_file = self.results_dir / "api_test_commands.sh"
        with open(curl_file, 'w') as f:
            f.write('\n'.join(commands))

        # Make executable
        curl_file.chmod(0o755)

        print(f"🔧 cURL commands saved: {curl_file}")


async def main():
    """Main demo function."""
    demo = CrawlerDemo()

    print("🎬 Starting Cipher-Spy Crawler Demonstration")
    print("This demo will showcase autonomous web reconnaissance capabilities")
    print("on pump.fun with respectful, safe crawling practices.")
    print()

    input("Press Enter to start the demonstration...")

    success = await demo.run_pump_fun_demo()

    if success:
        print("\n🎉 Demonstration completed successfully!")
        print("\nKey achievements:")
        print("✅ Autonomous page navigation and interaction")
        print("✅ Complete API endpoint discovery and documentation")
        print("✅ Safe, respectful crawling with scope enforcement")
        print("✅ Comprehensive reporting and analysis")
        print("\nCheck the demo_results/ directory for detailed output.")
    else:
        print("\n❌ Demonstration failed!")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
