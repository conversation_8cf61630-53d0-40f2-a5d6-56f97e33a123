#!/usr/bin/env python3
"""
Quick run script for Cipher-Spy.

Provides easy access to common operations without complex command line arguments.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))


def print_banner():
    """Print the Cipher-Spy banner."""
    print("""
🔬 Cipher-Spy: AI-Driven Red Team Swarm
🕷️  Autonomous Web Reconnaissance Agent
═══════════════════════════════════════════════════════════════
    """)


def print_menu():
    """Print the main menu."""
    print("Choose an option:")
    print("1. 🎬 Run Demo on pump.fun")
    print("2. 🕷️  Crawl Custom Target")
    print("3. 🚀 Start API Server")
    print("4. 🧪 Run Test Suite")
    print("5. ⚙️  Setup Playwright")
    print("6. 🧠 Setup Graphiti (Knowledge Graph)")
    print("7. ❓ Help & Documentation")
    print("0. 🚪 Exit")
    print()


async def run_demo():
    """Run the pump.fun demo."""
    print("🎬 Starting Demo on pump.fun...")
    print("This will demonstrate autonomous web reconnaissance capabilities.")
    print()

    try:
        from demo_crawler import CrawlerDemo
        demo = CrawlerDemo()
        success = await demo.run_pump_fun_demo()

        if success:
            print("\n🎉 Demo completed! Check demo_results/ for output.")
        else:
            print("\n❌ Demo failed!")

    except Exception as e:
        print(f"💥 Demo error: {e}")


async def crawl_custom_target():
    """Crawl a custom target."""
    print("🕷️  Custom Target Crawler")
    print("Enter the target URL to crawl:")

    target = input("Target URL: ").strip()
    if not target:
        print("❌ No target provided!")
        return

    if not target.startswith(('http://', 'https://')):
        target = f"https://{target}"

    print(f"🎯 Target: {target}")
    print("⚙️  Using safe default configuration...")

    try:
        from src.core.state import ScanState, TargetInfo
        from src.core.workflow import create_workflow
        from urllib.parse import urlparse
        from datetime import datetime

        # Parse target
        parsed = urlparse(target)
        domain = parsed.netloc

        # Create target info
        target_info = TargetInfo(
            url=target,
            domain=domain,
            scope=[domain, f"*.{domain}"]
        )

        # Create scan state with safe defaults
        state = ScanState(
            target=target_info,
            config={
                "max_crawl_depth": 2,
                "max_pages_per_domain": 20,
                "crawl_delay_ms": 2000,
                "safe_mode": True,
                "headless": True
            }
        )

        print(f"🆔 Scan ID: {state.scan_id}")
        print("🚀 Starting crawl...")

        # Create and run workflow
        workflow = create_workflow()
        start_time = datetime.now()
        final_state = await workflow.execute(state)
        end_time = datetime.now()

        duration = (end_time - start_time).total_seconds()

        # Print results
        print(f"\n📊 RESULTS")
        print(f"✅ Completed in {duration:.2f} seconds")
        print(f"📄 Pages: {len(final_state.pages)}")
        print(f"🔗 Endpoints: {len(final_state.endpoints)}")
        print(f"📊 Status: {final_state.status}")

        await workflow.cleanup()

    except Exception as e:
        print(f"💥 Crawl failed: {e}")


async def start_server():
    """Start the API server."""
    print("🚀 Starting Cipher-Spy API Server...")
    print("Server will be available at: http://localhost:8000")
    print("API docs will be at: http://localhost:8000/docs")
    print("Press Ctrl+C to stop")
    print()

    try:
        import uvicorn
        from src.main import create_app

        app = create_app()
        uvicorn.run(app, host="0.0.0.0", port=8000)

    except Exception as e:
        print(f"💥 Server failed: {e}")


async def run_tests():
    """Run the test suite."""
    print("🧪 Running Cipher-Spy Test Suite...")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_crawler.py"],
                              capture_output=True, text=True)

        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)

        if result.returncode == 0:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")

    except Exception as e:
        print(f"💥 Test error: {e}")


def setup_playwright():
    """Setup Playwright browsers."""
    print("⚙️  Setting up Playwright browsers...")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "setup_playwright.py"])

        if result.returncode == 0:
            print("✅ Playwright setup complete!")
        else:
            print("❌ Playwright setup failed!")

    except Exception as e:
        print(f"💥 Setup error: {e}")


def setup_graphiti():
    """Setup Graphiti knowledge graph."""
    print("🧠 Setting up Graphiti knowledge graph...")
    print("This will install advanced knowledge graph capabilities for:")
    print("• Exploit correlation and recommendation")
    print("• Vulnerability relationship mapping")
    print("• Technology stack analysis")
    print()

    try:
        import subprocess
        result = subprocess.run([sys.executable, "setup_graphiti.py"])

        if result.returncode == 0:
            print("✅ Graphiti setup complete!")
        else:
            print("❌ Graphiti setup failed!")

    except Exception as e:
        print(f"💥 Setup error: {e}")


def show_help():
    """Show help and documentation."""
    print("📚 Cipher-Spy Help & Documentation")
    print("="*50)
    print()
    print("📖 Available Documentation:")
    print("  • QUICK_START.md - Get started in minutes")
    print("  • CRAWLER_README.md - Complete technical docs")
    print("  • README.md - Project overview")
    print()
    print("🔧 Command Line Interface:")
    print("  python cipher-spy.py --help")
    print()
    print("🎯 Quick Examples:")
    print("  # Demo on pump.fun")
    print("  python cipher-spy.py demo")
    print()
    print("  # Crawl custom target")
    print("  python cipher-spy.py crawl https://example.com")
    print()
    print("  # Start API server")
    print("  python cipher-spy.py server")
    print()
    print("🐛 Troubleshooting:")
    print("  1. Install dependencies: pip install -r requirements.txt")
    print("  2. Setup Playwright: python setup_playwright.py")
    print("  3. Check logs for detailed error information")
    print()


async def main():
    """Main interactive menu."""
    print_banner()

    while True:
        print_menu()

        try:
            choice = input("Enter your choice (0-7): ").strip()
            print()

            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                await run_demo()
            elif choice == "2":
                await crawl_custom_target()
            elif choice == "3":
                await start_server()
            elif choice == "4":
                await run_tests()
            elif choice == "5":
                setup_playwright()
            elif choice == "6":
                setup_graphiti()
            elif choice == "7":
                show_help()
            else:
                print("❌ Invalid choice! Please enter 0-7.")

            print("\n" + "="*50)
            input("Press Enter to continue...")
            print()

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"💥 Error: {e}")
            print("Please try again.")


if __name__ == "__main__":
    asyncio.run(main())
