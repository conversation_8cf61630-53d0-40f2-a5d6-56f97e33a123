#!/usr/bin/env python3
"""
Advanced API Analyzer for Discovered pump.fun Scanner Endpoints

Analyzes and tests the high-value API endpoints discovered during reconnaissance.
"""

import asyncio
import json
import requests
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from dotenv import load_dotenv
import os

load_dotenv()

@dataclass
class APIEndpointAnalysis:
    """Analysis results for a discovered API endpoint."""
    url: str
    method: str
    endpoint_type: str
    business_value: str
    test_results: Dict[str, Any]
    response_schema: Optional[Dict[str, Any]]
    rate_limits: Optional[Dict[str, Any]]
    authentication_required: bool
    parameters_discovered: List[str]
    timestamp: str

class AdvancedAPIAnalyzer:
    """
    Analyzes and tests the high-value API endpoints discovered during reconnaissance.
    """

    def __init__(self):
        self.results_dir = Path("advanced_api_analysis")
        self.results_dir.mkdir(exist_ok=True)

        # High-value endpoints discovered from reconnaissance
        self.high_value_endpoints = [
            {
                "url": "https://advanced-api-v2.pump.fun/coins/list",
                "method": "GET",
                "type": "advanced_coin_listing",
                "business_value": "HIGH - Core advanced coin discovery API"
            },
            {
                "url": "https://advanced-api-v2.pump.fun/coins/graduated",
                "method": "GET",
                "type": "graduated_coins",
                "business_value": "HIGH - Graduated coins tracking"
            },
            {
                "url": "https://frontend-api-v3.pump.fun/bookmarks",
                "method": "GET",
                "type": "user_bookmarks",
                "business_value": "MEDIUM - User preference tracking"
            },
            {
                "url": "https://frontend-api-v3.pump.fun/replies/",
                "method": "GET",
                "type": "social_interactions",
                "business_value": "MEDIUM - Social engagement data"
            },
            {
                "url": "https://pump.fun/api/flags",
                "method": "GET",
                "type": "feature_flags",
                "business_value": "HIGH - Feature configuration and capabilities"
            },
            {
                "url": "https://pump-fe.helius-rpc.com/",
                "method": "POST",
                "type": "blockchain_rpc",
                "business_value": "HIGH - Direct blockchain interaction"
            }
        ]

        self.analysis_results: List[APIEndpointAnalysis] = []
        self.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')

    async def analyze_discovered_apis(self) -> Dict[str, Any]:
        """Perform comprehensive analysis of discovered APIs."""
        print("🔬 Advanced API Analysis - Discovered pump.fun Scanner Endpoints")
        print("="*70)

        try:
            # Phase 1: Test each high-value endpoint
            print("📡 Phase 1: Testing High-Value Endpoints")
            print("-" * 40)

            for endpoint in self.high_value_endpoints:
                print(f"   🎯 Testing {endpoint['type']}: {endpoint['url']}")
                analysis = await self._analyze_endpoint(endpoint)
                self.analysis_results.append(analysis)

                # Rate limiting
                await asyncio.sleep(2)

            # Phase 2: Parameter discovery
            print("\n🔍 Phase 2: Parameter Discovery & Testing")
            print("-" * 40)
            await self._discover_parameters()

            # Phase 3: Generate comprehensive documentation
            print("\n📚 Phase 3: Generate API Documentation")
            print("-" * 40)
            await self._generate_api_documentation()

            # Phase 4: Create integration examples
            print("\n🛠️  Phase 4: Create Integration Examples")
            print("-" * 40)
            await self._create_integration_examples()

            # Generate final report
            final_report = await self._generate_analysis_report()

            print(f"\n✅ Advanced API analysis completed!")
            print(f"📁 Results saved to: {self.results_dir}")

            return final_report

        except Exception as e:
            print(f"\n💥 Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}

    async def _analyze_endpoint(self, endpoint: Dict[str, Any]) -> APIEndpointAnalysis:
        """Analyze a specific API endpoint."""

        test_results = {}
        response_schema = None
        rate_limits = None
        auth_required = False
        parameters_discovered = []

        try:
            # Test basic endpoint access
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Origin': 'https://pump.fun',
                'Referer': 'https://pump.fun/advanced/coin?scan=true'
            }

            if endpoint['method'].upper() == 'GET':
                # Test GET endpoint
                response = requests.get(
                    endpoint['url'],
                    headers=headers,
                    timeout=15
                )

                test_results['basic_access'] = {
                    'status_code': response.status_code,
                    'success': response.status_code in [200, 201, 202],
                    'response_size': len(response.content),
                    'content_type': response.headers.get('content-type', 'unknown')
                }

                # Try to parse JSON response
                if response.status_code == 200:
                    try:
                        data = response.json()
                        response_schema = self._infer_schema(data)
                        test_results['data_structure'] = {
                            'type': type(data).__name__,
                            'size': len(data) if isinstance(data, (list, dict)) else 1,
                            'sample_keys': list(data.keys())[:5] if isinstance(data, dict) else []
                        }

                        print(f"      ✅ Success: {response.status_code}, Data: {type(data).__name__}")

                    except json.JSONDecodeError:
                        test_results['data_structure'] = {'type': 'non_json', 'content': response.text[:200]}
                        print(f"      ⚠️  Non-JSON response: {response.status_code}")
                else:
                    print(f"      ❌ Failed: {response.status_code}")
                    auth_required = response.status_code in [401, 403]

            elif endpoint['method'].upper() == 'POST':
                # Test POST endpoint with minimal data
                test_data = self._generate_test_payload(endpoint)

                response = requests.post(
                    endpoint['url'],
                    json=test_data,
                    headers={**headers, 'Content-Type': 'application/json'},
                    timeout=15
                )

                test_results['basic_access'] = {
                    'status_code': response.status_code,
                    'success': response.status_code in [200, 201, 202],
                    'response_size': len(response.content),
                    'content_type': response.headers.get('content-type', 'unknown')
                }

                print(f"      {'✅' if response.status_code < 400 else '❌'} POST: {response.status_code}")
                auth_required = response.status_code in [401, 403]

            # Check for rate limiting headers
            rate_limit_headers = ['x-ratelimit-limit', 'x-ratelimit-remaining', 'retry-after']
            rate_limits = {
                header: response.headers.get(header)
                for header in rate_limit_headers
                if response.headers.get(header)
            }

        except requests.exceptions.RequestException as e:
            test_results['error'] = str(e)
            print(f"      ❌ Request failed: {e}")

        return APIEndpointAnalysis(
            url=endpoint['url'],
            method=endpoint['method'],
            endpoint_type=endpoint['type'],
            business_value=endpoint['business_value'],
            test_results=test_results,
            response_schema=response_schema,
            rate_limits=rate_limits,
            authentication_required=auth_required,
            parameters_discovered=parameters_discovered,
            timestamp=datetime.now().isoformat()
        )

    def _infer_schema(self, data: Any, max_depth: int = 3) -> Dict[str, Any]:
        """Infer JSON schema from response data."""
        if max_depth <= 0:
            return {'type': 'unknown'}

        if data is None:
            return {'type': 'null'}
        elif isinstance(data, bool):
            return {'type': 'boolean'}
        elif isinstance(data, int):
            return {'type': 'integer'}
        elif isinstance(data, float):
            return {'type': 'number'}
        elif isinstance(data, str):
            return {'type': 'string', 'example': data[:50]}
        elif isinstance(data, list):
            if len(data) == 0:
                return {'type': 'array', 'items': {'type': 'unknown'}}
            else:
                return {
                    'type': 'array',
                    'length': len(data),
                    'items': self._infer_schema(data[0], max_depth - 1)
                }
        elif isinstance(data, dict):
            properties = {}
            for key, value in list(data.items())[:10]:
                properties[key] = self._infer_schema(value, max_depth - 1)

            return {
                'type': 'object',
                'properties': properties,
                'total_keys': len(data)
            }
        else:
            return {'type': 'unknown'}

    def _generate_test_payload(self, endpoint: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test payload for POST endpoints."""
        endpoint_type = endpoint.get('type', '')

        if 'rpc' in endpoint_type:
            return {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getAccountInfo",
                "params": ["********************************"]
            }
        else:
            return {}

    async def _discover_parameters(self):
        """Discover parameters for successful endpoints."""
        print("   🔍 Testing parameter combinations...")

        successful_endpoints = [
            analysis for analysis in self.analysis_results
            if analysis.test_results.get('basic_access', {}).get('success', False)
        ]

        for analysis in successful_endpoints:
            if analysis.endpoint_type in ['advanced_coin_listing', 'graduated_coins']:
                # Test common query parameters
                test_params = [
                    {'sortBy': 'creationTime'},
                    {'sortBy': 'marketCap'},
                    {'sortBy': 'volume'},
                    {'limit': 50},
                    {'offset': 0},
                    {'sortBy': 'creationTime', 'limit': 20}
                ]

                for params in test_params:
                    try:
                        response = requests.get(
                            analysis.url,
                            params=params,
                            headers={
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                'Accept': 'application/json',
                                'Origin': 'https://pump.fun',
                                'Referer': 'https://pump.fun/advanced/coin?scan=true'
                            },
                            timeout=10
                        )

                        if response.status_code == 200:
                            analysis.parameters_discovered.extend(params.keys())
                            print(f"      ✅ Parameters work for {analysis.endpoint_type}: {params}")

                        await asyncio.sleep(1)  # Rate limiting

                    except Exception as e:
                        continue

        print(f"   📊 Parameter discovery completed for {len(successful_endpoints)} endpoints")

    async def _generate_api_documentation(self):
        """Generate comprehensive API documentation."""
        if not self.openrouter_api_key:
            print("   ⚠️  No OpenRouter API key found, skipping AI documentation")
            return

        print("   🤖 Generating AI-powered API documentation...")

        # Prepare comprehensive API data
        api_data = []
        for analysis in self.analysis_results:
            if analysis.test_results.get('basic_access', {}).get('success', False):
                api_data.append({
                    'url': analysis.url,
                    'method': analysis.method,
                    'type': analysis.endpoint_type,
                    'business_value': analysis.business_value,
                    'response_schema': analysis.response_schema,
                    'parameters': analysis.parameters_discovered,
                    'authentication_required': analysis.authentication_required
                })

        if not api_data:
            print("   ⚠️  No successful APIs to document")
            return

        # Generate documentation using AI
        prompt = f"""You are a technical documentation expert specializing in API reverse engineering. Create comprehensive documentation for the discovered pump.fun advanced scanner APIs.

DISCOVERED APIS: {len(api_data)}

API DETAILS:
{json.dumps(api_data, indent=2, default=str)[:6000]}

Create professional documentation with these sections:

# Pump.fun Advanced Scanner API Documentation

## Overview
[Explain the advanced scanner API ecosystem and its capabilities]

## Authentication & Access
[Document authentication requirements and access patterns]

## Core Endpoints

### Advanced Coin Listing API
[Detailed documentation for coin listing endpoints]

### Graduated Coins API
[Documentation for graduated coins tracking]

### Social & User APIs
[Documentation for bookmarks, replies, and user interaction APIs]

### Feature Flags API
[Documentation for feature configuration API]

### Blockchain RPC Integration
[Documentation for direct blockchain access]

## Parameters & Filtering
[Comprehensive parameter documentation with examples]

## Response Schemas
[Detailed response structure documentation]

## Integration Examples

### Python Client Implementation
[Complete Python code for API integration]

### Advanced Filtering Examples
[Examples of complex filtering and querying]

### Real-time Data Streaming
[How to implement real-time data updates]

## Rate Limiting & Best Practices
[Performance optimization and rate limiting guidance]

## Error Handling
[Comprehensive error handling strategies]

## Business Use Cases
[Real-world applications and business value]

Focus on practical implementation for developers building coin scanning and analysis tools. Include working code examples and integration patterns.

Format as professional markdown documentation.
"""

        try:
            response = requests.post(
                'https://openrouter.ai/api/v1/chat/completions',
                headers={
                    'Authorization': f'Bearer {self.openrouter_api_key}',
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://cipher-spy.com',
                    'X-Title': 'Cipher-Spy Advanced API Documentation'
                },
                json={
                    'model': 'anthropic/claude-3.5-sonnet',
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 8000,
                    'temperature': 0.1
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                doc_content = result['choices'][0]['message']['content']

                # Save documentation
                doc_file = self.results_dir / "advanced_scanner_api_documentation.md"
                with open(doc_file, 'w', encoding='utf-8') as f:
                    f.write(doc_content)

                print(f"   ✅ API documentation generated: {doc_file}")
            else:
                print(f"   ❌ Documentation generation failed: {response.status_code}")

        except Exception as e:
            print(f"   ❌ Documentation generation error: {e}")

    async def _create_integration_examples(self):
        """Create practical integration examples."""
        print("   🛠️  Creating integration examples...")

        # Create Python client example
        client_code = self._generate_python_client()
        client_file = self.results_dir / "pump_scanner_client.py"
        with open(client_file, 'w', encoding='utf-8') as f:
            f.write(client_code)

        # Create usage examples
        examples_code = self._generate_usage_examples()
        examples_file = self.results_dir / "usage_examples.py"
        with open(examples_file, 'w', encoding='utf-8') as f:
            f.write(examples_code)

        print(f"   ✅ Integration examples created")
        print(f"      📄 Python Client: {client_file}")
        print(f"      📄 Usage Examples: {examples_file}")

    def _generate_python_client(self) -> str:
        """Generate Python client code for the discovered APIs."""

        successful_apis = [
            analysis for analysis in self.analysis_results
            if analysis.test_results.get('basic_access', {}).get('success', False)
        ]

        return f'''#!/usr/bin/env python3
"""
Pump.fun Advanced Scanner API Client

Generated by Cipher-Spy Advanced API Analyzer
Provides access to discovered advanced scanner APIs.
"""

import requests
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class ScannerConfig:
    """Configuration for the scanner client."""
    base_url: str = "https://advanced-api-v2.pump.fun"
    frontend_url: str = "https://frontend-api-v3.pump.fun"
    rate_limit_delay: float = 1.0
    timeout: int = 15


class PumpScannerClient:
    """
    Advanced client for pump.fun scanner APIs.

    Discovered APIs: {len(successful_apis)}
    """

    def __init__(self, config: Optional[ScannerConfig] = None):
        self.config = config or ScannerConfig()
        self.session = requests.Session()
        self.session.headers.update({{
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Origin': 'https://pump.fun',
            'Referer': 'https://pump.fun/advanced/coin?scan=true'
        }})

    def get_advanced_coins(self, sort_by: str = 'creationTime', limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """
        Get advanced coin listings with filtering and sorting.

        Args:
            sort_by: Sort criteria (creationTime, marketCap, volume)
            limit: Number of results to return
            offset: Pagination offset

        Returns:
            Dict containing coin data
        """
        params = {{
            'sortBy': sort_by,
            'limit': limit,
            'offset': offset
        }}

        response = self.session.get(
            f"{{self.config.base_url}}/coins/list",
            params=params,
            timeout=self.config.timeout
        )

        response.raise_for_status()
        time.sleep(self.config.rate_limit_delay)

        return response.json()

    def get_graduated_coins(self, sort_by: str = 'creationTime', limit: int = 50) -> Dict[str, Any]:
        """
        Get graduated coins data.

        Args:
            sort_by: Sort criteria
            limit: Number of results

        Returns:
            Dict containing graduated coins data
        """
        params = {{
            'sortBy': sort_by,
            'limit': limit
        }}

        response = self.session.get(
            f"{{self.config.base_url}}/coins/graduated",
            params=params,
            timeout=self.config.timeout
        )

        response.raise_for_status()
        time.sleep(self.config.rate_limit_delay)

        return response.json()

    def get_feature_flags(self) -> Dict[str, Any]:
        """
        Get current feature flags and configuration.

        Returns:
            Dict containing feature flags
        """
        response = self.session.get(
            "https://pump.fun/api/flags",
            timeout=self.config.timeout
        )

        response.raise_for_status()
        time.sleep(self.config.rate_limit_delay)

        return response.json()

    def get_social_replies(self, limit: int = 1000, offset: int = 0, reverse_order: bool = True) -> Dict[str, Any]:
        """
        Get social replies and interactions.

        Args:
            limit: Number of replies to fetch
            offset: Pagination offset
            reverse_order: Whether to reverse order

        Returns:
            Dict containing social interaction data
        """
        params = {{
            'limit': limit,
            'offset': offset,
            'reverseOrder': str(reverse_order).lower()
        }}

        response = self.session.get(
            f"{{self.config.frontend_url}}/replies/",
            params=params,
            timeout=self.config.timeout
        )

        response.raise_for_status()
        time.sleep(self.config.rate_limit_delay)

        return response.json()

    def scan_new_coins(self, max_age_hours: int = 24, min_market_cap: int = 1000) -> List[Dict[str, Any]]:
        """
        Scan for new coins matching criteria.

        Args:
            max_age_hours: Maximum age in hours
            min_market_cap: Minimum market cap filter

        Returns:
            List of coins matching criteria
        """
        coins = self.get_advanced_coins(sort_by='creationTime', limit=100)

        # Filter based on criteria (implement filtering logic based on response structure)
        # This would need to be customized based on actual response format

        return coins

    def monitor_graduated_coins(self, callback=None) -> None:
        """
        Monitor for newly graduated coins.

        Args:
            callback: Function to call when new graduated coins are found
        """
        last_seen = set()

        while True:
            try:
                graduated = self.get_graduated_coins(limit=20)

                # Extract coin identifiers (customize based on response structure)
                current_coins = set()  # Implement based on actual response

                new_coins = current_coins - last_seen
                if new_coins and callback:
                    callback(new_coins)

                last_seen = current_coins
                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                print(f"Monitoring error: {{e}}")
                time.sleep(60)  # Wait longer on error


# Example usage
if __name__ == "__main__":
    client = PumpScannerClient()

    # Get latest coins
    latest_coins = client.get_advanced_coins(sort_by='creationTime', limit=10)
    print(f"Latest coins: {{len(latest_coins)}}")

    # Get graduated coins
    graduated = client.get_graduated_coins(limit=10)
    print(f"Graduated coins: {{len(graduated)}}")

    # Get feature flags
    flags = client.get_feature_flags()
    print(f"Feature flags: {{flags}}")
'''

    def _generate_usage_examples(self) -> str:
        """Generate usage examples for the API client."""
        from api_analyzer_methods import generate_usage_examples
        return generate_usage_examples()

    async def _generate_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive analysis report."""
        print("   📋 Generating analysis report...")

        from api_analyzer_methods import (
            generate_key_discoveries,
            generate_integration_recommendations,
            assess_business_impact,
            generate_markdown_summary
        )

        # Compile statistics
        successful_apis = [
            analysis for analysis in self.analysis_results
            if analysis.test_results.get('basic_access', {}).get('success', False)
        ]

        failed_apis = [
            analysis for analysis in self.analysis_results
            if not analysis.test_results.get('basic_access', {}).get('success', False)
        ]

        high_value_apis = [
            analysis for analysis in successful_apis
            if 'HIGH' in analysis.business_value
        ]

        # Generate report
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_endpoints_analyzed': len(self.analysis_results),
            'successful_endpoints': len(successful_apis),
            'failed_endpoints': len(failed_apis),
            'high_value_endpoints': len(high_value_apis),
            'success_rate': len(successful_apis) / len(self.analysis_results) * 100 if self.analysis_results else 0,
            'endpoint_analysis': [asdict(analysis) for analysis in self.analysis_results],
            'key_discoveries': generate_key_discoveries(successful_apis),
            'integration_recommendations': generate_integration_recommendations(successful_apis),
            'business_impact': assess_business_impact(high_value_apis)
        }

        # Save report
        report_file = self.results_dir / "advanced_api_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, default=str)

        # Generate markdown summary
        markdown_summary = generate_markdown_summary(report)
        summary_file = self.results_dir / "API_ANALYSIS_SUMMARY.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(markdown_summary)

        print(f"   ✅ Analysis report generated")
        print(f"   📄 JSON Report: {report_file}")
        print(f"   📝 Summary: {summary_file}")

        return report


async def main():
    """Main execution function for advanced API analysis."""
    print("🚀 Starting Advanced API Analysis...")

    analyzer = AdvancedAPIAnalyzer()
    results = await analyzer.analyze_discovered_apis()

    if results.get('error'):
        print(f"\n💥 Analysis failed: {results['error']}")
        return False

    print("\n🎉 Advanced API Analysis Completed Successfully!")
    print("\n📋 SUMMARY:")
    print(f"   🎯 Endpoints Analyzed: {results.get('total_endpoints_analyzed', 0)}")
    print(f"   ✅ Successful: {results.get('successful_endpoints', 0)}")
    print(f"   📈 Success Rate: {results.get('success_rate', 0):.1f}%")
    print(f"   💎 High-Value APIs: {results.get('high_value_endpoints', 0)}")
    print(f"   📁 Results Directory: advanced_api_analysis")

    return True


if __name__ == "__main__":
    asyncio.run(main())
