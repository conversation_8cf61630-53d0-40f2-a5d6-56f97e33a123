#!/usr/bin/env python3
"""
Database setup script for Cipher-Spy.

Initializes PostgreSQL and Neo4j databases, creates tables,
and sets up initial data including exploit knowledge base.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config.settings import get_settings
from src.config.database import DatabaseManager
from src.models.database import Base
from src.utils.logging import setup_logging


async def setup_postgres():
    """Setup PostgreSQL database and tables."""
    print("Setting up PostgreSQL database...")
    
    db_manager = DatabaseManager()
    
    try:
        # Initialize PostgreSQL connection
        await db_manager.init_postgres()
        print("✓ PostgreSQL connection established")
        
        # Create all tables
        async with db_manager.postgres_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✓ Database tables created")
        
        # TODO: Insert initial data if needed
        # await insert_initial_data(db_manager)
        
    except Exception as e:
        print(f"✗ PostgreSQL setup failed: {e}")
        raise
    finally:
        await db_manager.close_postgres()


async def setup_neo4j():
    """Setup Neo4j database and constraints."""
    print("Setting up Neo4j database...")
    
    db_manager = DatabaseManager()
    
    try:
        # Initialize Neo4j connection
        await db_manager.init_neo4j()
        print("✓ Neo4j connection established")
        
        # Create constraints and indexes
        async with db_manager.get_neo4j_session() as session:
            # Create constraints for unique nodes
            constraints = [
                "CREATE CONSTRAINT exploit_id IF NOT EXISTS FOR (e:Exploit) REQUIRE e.id IS UNIQUE",
                "CREATE CONSTRAINT cve_id IF NOT EXISTS FOR (c:CVE) REQUIRE c.id IS UNIQUE",
                "CREATE CONSTRAINT technology_name IF NOT EXISTS FOR (t:Technology) REQUIRE t.name IS UNIQUE",
                "CREATE CONSTRAINT vulnerability_id IF NOT EXISTS FOR (v:Vulnerability) REQUIRE v.id IS UNIQUE"
            ]
            
            for constraint in constraints:
                try:
                    await session.run(constraint)
                    print(f"✓ Created constraint: {constraint.split()[2]}")
                except Exception as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠ Constraint creation warning: {e}")
            
            # Create indexes for performance
            indexes = [
                "CREATE INDEX exploit_platform IF NOT EXISTS FOR (e:Exploit) ON (e.platform)",
                "CREATE INDEX exploit_type IF NOT EXISTS FOR (e:Exploit) ON (e.type)",
                "CREATE INDEX cve_severity IF NOT EXISTS FOR (c:CVE) ON (c.severity)",
                "CREATE INDEX technology_category IF NOT EXISTS FOR (t:Technology) ON (t.category)"
            ]
            
            for index in indexes:
                try:
                    await session.run(index)
                    print(f"✓ Created index: {index.split()[2]}")
                except Exception as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠ Index creation warning: {e}")
        
        print("✓ Neo4j constraints and indexes created")
        
    except Exception as e:
        print(f"✗ Neo4j setup failed: {e}")
        raise
    finally:
        await db_manager.close_neo4j()


async def verify_setup():
    """Verify database setup is working correctly."""
    print("Verifying database setup...")
    
    db_manager = DatabaseManager()
    
    try:
        # Test PostgreSQL
        await db_manager.init_postgres()
        async with db_manager.postgres_engine.begin() as conn:
            result = await conn.execute("SELECT 1 as test")
            assert result.scalar() == 1
        print("✓ PostgreSQL verification passed")
        
        # Test Neo4j
        await db_manager.init_neo4j()
        async with db_manager.get_neo4j_session() as session:
            result = await session.run("RETURN 1 as test")
            record = await result.single()
            assert record["test"] == 1
        print("✓ Neo4j verification passed")
        
        # Test health check
        health = await db_manager.health_check()
        assert health["postgres"]["status"] == "healthy"
        assert health["neo4j"]["status"] == "healthy"
        print("✓ Health check passed")
        
    except Exception as e:
        print(f"✗ Verification failed: {e}")
        raise
    finally:
        await db_manager.close_postgres()
        await db_manager.close_neo4j()


async def main():
    """Main setup function."""
    print("🚀 Starting Cipher-Spy database setup...")
    
    # Setup logging
    setup_logging(level="INFO", environment="development")
    
    try:
        # Setup databases
        await setup_postgres()
        await setup_neo4j()
        
        # Verify setup
        await verify_setup()
        
        print("🎉 Database setup completed successfully!")
        print("\nNext steps:")
        print("1. Load exploit database: python scripts/load_exploitdb.py")
        print("2. Start the application: docker-compose up")
        
    except Exception as e:
        print(f"💥 Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
