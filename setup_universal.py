#!/usr/bin/env python3
"""
Cipher-Spy Universal Setup Script

Sets up the enhanced Cipher-Spy Universal API Discovery Framework
with all necessary dependencies and configurations.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def print_banner():
    """Print setup banner."""
    print("="*80)
    print("🔍 Cipher-Spy Universal API Discovery Framework Setup")
    print("="*80)
    print("Setting up the enhanced universal framework...")
    print()


def check_python_version():
    """Check Python version compatibility."""
    print("📋 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        sys.exit(1)


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directory structure...")
    
    directories = [
        "universal_api_discovery",
        "chrome_extension_data",
        "api_discovery_results",
        "src/api_discovery/configs",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Created: {directory}")


def setup_environment():
    """Setup environment configuration."""
    print("\n⚙️ Setting up environment configuration...")
    
    env_content = """# Cipher-Spy Universal API Discovery Configuration

# Backend Configuration
CIPHER_SPY_HOST=localhost
CIPHER_SPY_PORT=8000
CIPHER_SPY_DEBUG=true

# API Discovery Settings
MAX_CONCURRENT_DISCOVERIES=3
DEFAULT_RATE_LIMIT_DELAY=1.0
MAX_PARAMETER_TESTS=1000

# Chrome Extension Integration
EXTENSION_API_ENABLED=true
EXTENSION_CORS_ORIGINS=["chrome-extension://*", "http://localhost:*"]

# OpenRouter API (Optional - for AI-powered documentation)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Analysis Settings
GENERATE_DOCUMENTATION=true
GENERATE_CLIENT_CODE=true
DEEP_ANALYSIS_ENABLED=true

# Security Settings
RESPECT_ROBOTS_TXT=true
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_SECOND=2

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/cipher_spy_universal.log
"""
    
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, "w") as f:
            f.write(env_content)
        print("✅ Created .env configuration file")
    else:
        print("✅ .env file already exists")


def create_chrome_extension_icons():
    """Create placeholder icons for Chrome extension."""
    print("\n🎨 Setting up Chrome extension icons...")
    
    icons_dir = Path("chrome_extension/icons")
    icons_dir.mkdir(exist_ok=True)
    
    # Create simple placeholder icons (you would replace these with actual icons)
    icon_sizes = [16, 32, 48, 128]
    
    for size in icon_sizes:
        icon_file = icons_dir / f"icon{size}.png"
        if not icon_file.exists():
            # Create a simple placeholder (in a real setup, you'd use actual icon files)
            print(f"   📝 Placeholder needed: {icon_file}")
    
    print("✅ Chrome extension icon structure ready")


def setup_sample_configs():
    """Create sample target configurations."""
    print("\n📋 Creating sample configurations...")
    
    configs_dir = Path("src/api_discovery/configs")
    
    # Sample pump.fun configuration
    pump_config = {
        "name": "pump_fun_sample",
        "domain": "pump.fun",
        "description": "Sample configuration for pump.fun analysis",
        "base_urls": ["https://pump.fun", "https://advanced-api-v2.pump.fun"],
        "known_endpoints": [
            {
                "name": "coin_list",
                "url": "https://advanced-api-v2.pump.fun/coins/list",
                "method": "GET",
                "business_value": "Cryptocurrency listing discovery"
            }
        ],
        "parameter_categories": {
            "pagination": ["limit", "offset", "page"],
            "sorting": ["sortBy", "orderBy", "direction"],
            "filtering": ["search", "minMarketCap", "maxMarketCap"]
        }
    }
    
    with open(configs_dir / "pump_fun_sample.json", "w") as f:
        json.dump(pump_config, f, indent=2)
    
    # Sample generic configuration
    generic_config = {
        "name": "generic_api_sample",
        "domain": "example.com",
        "description": "Sample configuration for generic API discovery",
        "parameter_categories": {
            "pagination": ["page", "limit", "offset"],
            "sorting": ["sort", "order"],
            "filtering": ["search", "filter", "q"]
        }
    }
    
    with open(configs_dir / "generic_sample.json", "w") as f:
        json.dump(generic_config, f, indent=2)
    
    print("✅ Sample configurations created")


def create_demo_script():
    """Create a demo script for testing the system."""
    print("\n🎯 Creating demo script...")
    
    demo_content = '''#!/usr/bin/env python3
"""
Cipher-Spy Universal Demo Script

Demonstrates the capabilities of the Universal API Discovery Framework.
"""

import asyncio
from src.api_discovery.discovery_engine import DiscoveryEngine
from src.api_discovery.target_config import TargetConfig


async def demo_universal_discovery():
    """Demonstrate universal API discovery."""
    print("🚀 Cipher-Spy Universal API Discovery Demo")
    print("="*50)
    
    # Initialize discovery engine
    engine = DiscoveryEngine()
    
    # Demo 1: Generic website analysis
    print("\\n📊 Demo 1: Generic API Discovery")
    print("-" * 30)
    
    try:
        config = TargetConfig.create_generic_config("httpbin.org", "HTTPBin API Test")
        result = await engine.discover_target(config, analyzer_type='universal')
        
        print(f"✅ Discovered {len(result.discovered_endpoints)} endpoints")
        print(f"🧪 Tested {len(result.parameter_tests)} parameters")
        print(f"⏱️ Analysis completed in {result.total_duration:.2f} seconds")
        
    except Exception as e:
        print(f"❌ Demo 1 failed: {e}")
    
    # Demo 2: Pump.fun specialized analysis (if available)
    print("\\n🪙 Demo 2: Pump.fun Specialized Analysis")
    print("-" * 40)
    
    try:
        config = TargetConfig.create_pump_fun_config()
        result = await engine.discover_target(config, analyzer_type='pump_fun')
        
        print(f"✅ Discovered {len(result.discovered_endpoints)} pump.fun endpoints")
        print(f"💰 Cryptocurrency-specific analysis completed")
        
    except Exception as e:
        print(f"❌ Demo 2 failed: {e}")
    
    print("\\n🎉 Demo completed! Check the results directory for detailed output.")


if __name__ == "__main__":
    asyncio.run(demo_universal_discovery())
'''
    
    with open("demo_universal.py", "w") as f:
        f.write(demo_content)
    
    # Make executable
    os.chmod("demo_universal.py", 0o755)
    print("✅ Demo script created: demo_universal.py")


def print_next_steps():
    """Print next steps for the user."""
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("="*50)
    
    print("1. 🔧 Configure your environment:")
    print("   - Edit .env file with your OpenRouter API key (optional)")
    print("   - Adjust rate limits and analysis settings")
    
    print("\\n2. 🚀 Try the universal analyzer:")
    print("   python universal_api_analyzer.py --target httpbin.org")
    
    print("\\n3. 🌐 Install Chrome extension:")
    print("   - Open Chrome and go to chrome://extensions/")
    print("   - Enable 'Developer mode'")
    print("   - Click 'Load unpacked' and select chrome_extension/ folder")
    
    print("\\n4. 🎯 Run the demo:")
    print("   python demo_universal.py")
    
    print("\\n5. 🔍 Start the backend server:")
    print("   python -m src.main")
    
    print("\\n📚 Documentation:")
    print("   - README_UNIVERSAL.md - Complete framework documentation")
    print("   - src/api_discovery/ - Framework source code")
    print("   - chrome_extension/ - Browser extension code")
    
    print("\\n🆘 Need help?")
    print("   - Check the documentation in docs/")
    print("   - Run with --help for command options")
    print("   - Report issues on GitHub")


def main():
    """Main setup function."""
    print_banner()
    
    try:
        check_python_version()
        install_dependencies()
        create_directories()
        setup_environment()
        create_chrome_extension_icons()
        setup_sample_configs()
        create_demo_script()
        print_next_steps()
        
    except KeyboardInterrupt:
        print("\\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\\n❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
'''
