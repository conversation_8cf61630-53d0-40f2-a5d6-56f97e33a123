#!/usr/bin/env python3
"""
Universal API Analyzer for Cipher-Spy

A concrete implementation of BaseAPIAnalyzer that provides universal
API discovery capabilities for any website. This analyzer uses generic
strategies and can be configured for any target domain.
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from urllib.parse import urljoin, urlparse

from .base_analyzer import BaseAPIAnalyzer, APIEndpointConfig
from .target_config import TargetConfig, APIType


class UniversalAPIAnalyzer(BaseAPIAnalyzer):
    """
    Universal API analyzer that can discover and analyze APIs for any website.
    
    This analyzer implements generic discovery strategies that work across
    different website architectures and API patterns.
    """

    def __init__(self, target_config: TargetConfig, **kwargs):
        """Initialize the universal API analyzer."""
        super().__init__(target_config, **kwargs)
        
        # Universal parameter test sets
        self.universal_parameter_sets = {
            'pagination': [
                'page', 'limit', 'offset', 'size', 'count', 'perPage', 'pageSize',
                'skip', 'start', 'from', 'cursor', 'next', 'prev', 'after', 'before'
            ],
            'sorting': [
                'sort', 'sortBy', 'orderBy', 'order', 'direction', 'desc', 'asc',
                'sortDirection', 'sortOrder', 'reverse'
            ],
            'filtering': [
                'filter', 'search', 'query', 'q', 'keyword', 'term', 'name',
                'type', 'category', 'status', 'state', 'tag', 'tags'
            ],
            'format': [
                'format', 'output', 'type', 'accept', 'contentType',
                'json', 'xml', 'csv', 'html'
            ],
            'time_based': [
                'date', 'time', 'timestamp', 'since', 'until', 'from', 'to',
                'startDate', 'endDate', 'createdAfter', 'createdBefore',
                'updatedAfter', 'updatedBefore'
            ],
            'numeric_filters': [
                'min', 'max', 'minValue', 'maxValue', 'gt', 'lt', 'gte', 'lte',
                'range', 'between'
            ],
            'boolean_flags': [
                'active', 'enabled', 'disabled', 'public', 'private', 'visible',
                'hidden', 'featured', 'published', 'draft'
            ],
            'advanced': [
                'include', 'exclude', 'fields', 'select', 'expand', 'embed',
                'populate', 'join', 'with', 'relations'
            ]
        }
        
        # Universal test values
        self.universal_test_values = {
            'numeric': [1, 5, 10, 25, 50, 100, 500, 1000],
            'string': ['test', 'example', 'sample', 'demo', 'api'],
            'boolean': [True, False, 'true', 'false', '1', '0'],
            'sort_directions': ['asc', 'desc', 'ascending', 'descending'],
            'formats': ['json', 'xml', 'csv', 'html'],
            'time_formats': ['2024-01-01', '2024-12-31', '1h', '24h', '7d', '30d']
        }

    async def discover_endpoints(self) -> List[APIEndpointConfig]:
        """
        Discover API endpoints using universal strategies.
        
        Returns:
            List[APIEndpointConfig]: Discovered API endpoints
        """
        print("🔍 Discovering endpoints using universal strategies...")
        
        discovered_endpoints = []
        
        # Start with known endpoints from configuration
        discovered_endpoints.extend(self.target_config.known_endpoints)
        print(f"   📋 Added {len(self.target_config.known_endpoints)} known endpoints")
        
        # Strategy 1: Pattern-based URL discovery
        pattern_endpoints = await self._discover_by_url_patterns()
        discovered_endpoints.extend(pattern_endpoints)
        print(f"   🎯 Discovered {len(pattern_endpoints)} endpoints via URL patterns")
        
        # Strategy 2: Navigation-based discovery
        nav_endpoints = await self._discover_by_navigation()
        discovered_endpoints.extend(nav_endpoints)
        print(f"   🧭 Discovered {len(nav_endpoints)} endpoints via navigation")
        
        # Strategy 3: Common API path probing
        probe_endpoints = await self._discover_by_probing()
        discovered_endpoints.extend(probe_endpoints)
        print(f"   🔬 Discovered {len(probe_endpoints)} endpoints via probing")
        
        # Remove duplicates
        unique_endpoints = self._deduplicate_endpoints(discovered_endpoints)
        print(f"   ✅ Total unique endpoints: {len(unique_endpoints)}")
        
        return unique_endpoints

    async def _discover_by_url_patterns(self) -> List[APIEndpointConfig]:
        """Discover endpoints by analyzing URL patterns."""
        endpoints = []
        
        # Common API URL patterns
        api_patterns = [
            '/api/v1/', '/api/v2/', '/api/v3/',
            '/api/', '/rest/', '/graphql',
            '/endpoints/', '/services/',
            '/data/', '/feed/', '/json/'
        ]
        
        for base_url in self.target_config.base_urls:
            for pattern in api_patterns:
                test_url = urljoin(base_url, pattern)
                
                try:
                    response = self.session.get(test_url, timeout=10)
                    if response.status_code == 200:
                        # Try to determine if this is an API endpoint
                        content_type = response.headers.get('content-type', '').lower()
                        if 'json' in content_type or 'api' in test_url.lower():
                            endpoint = APIEndpointConfig(
                                name=f"discovered_{pattern.strip('/').replace('/', '_')}",
                                url=test_url,
                                method="GET",
                                api_type=self._detect_api_type(response),
                                description=f"Discovered via pattern: {pattern}"
                            )
                            endpoints.append(endpoint)
                
                except Exception:
                    continue
                
                # Rate limiting
                await asyncio.sleep(self.rate_limit_delay)
        
        return endpoints

    async def _discover_by_navigation(self) -> List[APIEndpointConfig]:
        """Discover endpoints by navigating the website."""
        endpoints = []
        
        # This would integrate with the existing Playwright crawler
        # For now, we'll implement a simplified version
        
        for start_url in self.target_config.navigation_config.start_urls:
            try:
                response = self.session.get(start_url, timeout=15)
                if response.status_code == 200:
                    # Look for API-related links in the page
                    api_links = self._extract_api_links(response.text, start_url)
                    
                    for link in api_links:
                        endpoint = APIEndpointConfig(
                            name=f"nav_discovered_{len(endpoints)}",
                            url=link,
                            method="GET",
                            description=f"Discovered via navigation from {start_url}"
                        )
                        endpoints.append(endpoint)
                
            except Exception as e:
                print(f"      ⚠️ Navigation discovery error for {start_url}: {e}")
                continue
            
            await asyncio.sleep(self.rate_limit_delay)
        
        return endpoints

    async def _discover_by_probing(self) -> List[APIEndpointConfig]:
        """Discover endpoints by probing common API paths."""
        endpoints = []
        
        # Common API endpoint names
        common_endpoints = [
            'users', 'user', 'profile', 'account',
            'data', 'items', 'list', 'search',
            'status', 'health', 'info', 'version',
            'config', 'settings', 'options',
            'feed', 'posts', 'content', 'media'
        ]
        
        for base_url in self.target_config.base_urls:
            # Try different API base paths
            api_bases = ['/api/', '/api/v1/', '/rest/', '/']
            
            for api_base in api_bases:
                for endpoint_name in common_endpoints:
                    test_url = urljoin(base_url, f"{api_base}{endpoint_name}")
                    
                    try:
                        response = self.session.get(test_url, timeout=10)
                        if response.status_code == 200:
                            content_type = response.headers.get('content-type', '').lower()
                            if 'json' in content_type:
                                endpoint = APIEndpointConfig(
                                    name=f"probed_{endpoint_name}",
                                    url=test_url,
                                    method="GET",
                                    description=f"Discovered via probing: {endpoint_name}"
                                )
                                endpoints.append(endpoint)
                    
                    except Exception:
                        continue
                    
                    # Rate limiting
                    await asyncio.sleep(self.rate_limit_delay)
        
        return endpoints

    def _extract_api_links(self, html_content: str, base_url: str) -> List[str]:
        """Extract potential API links from HTML content."""
        api_links = []
        
        # Look for links that might be API endpoints
        link_patterns = [
            r'href=["\']([^"\']*(?:api|json|rest|graphql)[^"\']*)["\']',
            r'src=["\']([^"\']*\.json[^"\']*)["\']',
            r'url:["\']([^"\']*(?:api|json)[^"\']*)["\']'
        ]
        
        for pattern in link_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                full_url = urljoin(base_url, match)
                if self._is_valid_api_url(full_url):
                    api_links.append(full_url)
        
        return list(set(api_links))  # Remove duplicates

    def _is_valid_api_url(self, url: str) -> bool:
        """Check if a URL looks like a valid API endpoint."""
        parsed = urlparse(url)
        
        # Check if domain is allowed
        if parsed.netloc not in self.target_config.allowed_domains:
            return False
        
        # Check for API indicators
        api_indicators = ['api', 'json', 'rest', 'graphql', 'endpoint']
        url_lower = url.lower()
        
        return any(indicator in url_lower for indicator in api_indicators)

    def _detect_api_type(self, response) -> APIType:
        """Detect the type of API based on response characteristics."""
        content_type = response.headers.get('content-type', '').lower()
        
        if 'json' in content_type:
            try:
                data = response.json()
                if isinstance(data, dict) and 'data' in data and 'query' in str(data):
                    return APIType.GRAPHQL
                else:
                    return APIType.REST
            except:
                return APIType.REST
        
        return APIType.UNKNOWN

    def _deduplicate_endpoints(self, endpoints: List[APIEndpointConfig]) -> List[APIEndpointConfig]:
        """Remove duplicate endpoints based on URL."""
        seen_urls = set()
        unique_endpoints = []
        
        for endpoint in endpoints:
            if endpoint.url not in seen_urls:
                seen_urls.add(endpoint.url)
                unique_endpoints.append(endpoint)
        
        return unique_endpoints

    async def extract_business_logic(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract business logic from API responses (universal implementation).
        
        Args:
            endpoint_data: Raw endpoint response data
            
        Returns:
            Dict[str, Any]: Extracted business intelligence
        """
        business_logic = {
            'data_structure': {},
            'business_entities': [],
            'key_metrics': [],
            'relationships': [],
            'patterns': {}
        }
        
        # Analyze data structure
        if 'fields' in endpoint_data:
            business_logic['data_structure'] = self._analyze_business_structure(endpoint_data['fields'])
        
        # Identify business entities
        business_logic['business_entities'] = self._identify_business_entities(endpoint_data)
        
        # Extract key metrics
        business_logic['key_metrics'] = self._extract_key_metrics(endpoint_data)
        
        return business_logic

    def _analyze_business_structure(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the business structure from field data."""
        structure = {
            'entity_fields': [],
            'metric_fields': [],
            'metadata_fields': [],
            'relationship_fields': []
        }
        
        for field_path, field_info in fields.items():
            field_lower = field_path.lower()
            
            # Categorize fields
            if any(keyword in field_lower for keyword in ['id', 'uuid', 'key']):
                structure['entity_fields'].append(field_path)
            elif any(keyword in field_lower for keyword in ['count', 'total', 'sum', 'avg', 'rate']):
                structure['metric_fields'].append(field_path)
            elif any(keyword in field_lower for keyword in ['created', 'updated', 'timestamp', 'date']):
                structure['metadata_fields'].append(field_path)
            elif any(keyword in field_lower for keyword in ['ref', 'link', 'url', 'href']):
                structure['relationship_fields'].append(field_path)
        
        return structure

    def _identify_business_entities(self, endpoint_data: Dict[str, Any]) -> List[str]:
        """Identify business entities from the endpoint data."""
        entities = []
        
        # Look for common business entity patterns
        entity_patterns = [
            'user', 'customer', 'client', 'account', 'profile',
            'product', 'item', 'service', 'order', 'transaction',
            'post', 'article', 'content', 'media', 'file',
            'category', 'tag', 'group', 'team', 'organization'
        ]
        
        endpoint_name = endpoint_data.get('endpoint_name', '').lower()
        
        for pattern in entity_patterns:
            if pattern in endpoint_name:
                entities.append(pattern)
        
        return entities

    def _extract_key_metrics(self, endpoint_data: Dict[str, Any]) -> List[str]:
        """Extract key business metrics from the endpoint data."""
        metrics = []
        
        if 'fields' in endpoint_data:
            for field_path in endpoint_data['fields'].keys():
                field_lower = field_path.lower()
                
                # Look for metric indicators
                if any(keyword in field_lower for keyword in [
                    'count', 'total', 'sum', 'average', 'rate', 'percentage',
                    'score', 'rating', 'value', 'amount', 'price', 'cost'
                ]):
                    metrics.append(field_path)
        
        return metrics

    def get_parameter_test_sets(self) -> Dict[str, List[str]]:
        """Get universal parameter test sets."""
        # Merge universal sets with target-specific sets
        test_sets = self.universal_parameter_sets.copy()
        
        if self.target_config.parameter_config.parameter_categories:
            for category, params in self.target_config.parameter_config.parameter_categories.items():
                if category in test_sets:
                    test_sets[category].extend(params)
                else:
                    test_sets[category] = params
        
        return test_sets

    def get_test_values(self) -> Dict[str, List[Any]]:
        """Get universal test values."""
        # Merge universal values with target-specific values
        test_values = self.universal_test_values.copy()
        
        if self.target_config.parameter_config.test_values:
            for category, values in self.target_config.parameter_config.test_values.items():
                if category in test_values:
                    test_values[category].extend(values)
                else:
                    test_values[category] = values
        
        return test_values
