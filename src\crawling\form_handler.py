"""
Form interaction handler for Cipher-Spy.

Intelligently fills and submits forms during web crawling to discover
authenticated areas and trigger API calls.
"""

import asyncio
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass

from playwright.async_api import Page, Locator
from playwright.async_api import TimeoutError as PlaywrightTimeoutError

from ..utils.logging import get_logger


@dataclass
class FormInfo:
    """Information about a discovered form."""
    action: str
    method: str
    fields: List[Dict[str, str]]
    submit_buttons: List[str]
    discovered_at: datetime
    page_url: str


class FormHandler:
    """
    Intelligent form interaction handler.
    
    Features:
    - Automatic form detection and analysis
    - Safe form filling with test data
    - Authentication form handling
    - Form submission with result tracking
    """
    
    def __init__(self, credentials: Optional[Dict[str, str]] = None):
        """
        Initialize form handler.
        
        Args:
            credentials: Optional credentials for authentication forms
        """
        self.credentials = credentials or {}
        self.logger = get_logger(__name__)
        
        # Discovered forms
        self.discovered_forms: List[FormInfo] = []
        
        # Test data for form filling
        self.test_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'TestPassword123!',
            'name': 'Test User',
            'first_name': 'Test',
            'last_name': 'User',
            'phone': '555-0123',
            'address': '123 Test St',
            'city': 'Test City',
            'zip': '12345',
            'search': 'test query',
            'comment': 'This is a test comment',
            'message': 'This is a test message'
        }
        
        # Field type patterns
        self.field_patterns = {
            'email': [r'email', r'e-mail', r'mail'],
            'password': [r'password', r'pass', r'pwd'],
            'username': [r'username', r'user', r'login'],
            'name': [r'name', r'fullname'],
            'first_name': [r'first.*name', r'fname', r'given.*name'],
            'last_name': [r'last.*name', r'lname', r'family.*name', r'surname'],
            'phone': [r'phone', r'tel', r'mobile'],
            'address': [r'address', r'street'],
            'city': [r'city', r'town'],
            'zip': [r'zip', r'postal', r'postcode'],
            'search': [r'search', r'query', r'q'],
            'comment': [r'comment', r'note'],
            'message': [r'message', r'msg', r'text']
        }
        
        self.logger.info("Form handler initialized")
    
    async def handle_form(self, form: Locator, page: Page) -> Optional[FormInfo]:
        """
        Handle a single form element.
        
        Args:
            form: Playwright locator for the form
            page: Current page
            
        Returns:
            FormInfo if form was processed successfully
        """
        try:
            # Extract form information
            form_info = await self._analyze_form(form, page)
            
            if not form_info:
                return None
            
            self.logger.info(f"Processing form: {form_info.action} ({form_info.method})")
            
            # Fill form fields
            filled_fields = await self._fill_form(form, form_info, page)
            
            if filled_fields:
                # Submit form if safe to do so
                await self._submit_form(form, form_info, page)
            
            # Store discovered form
            self.discovered_forms.append(form_info)
            
            return form_info
            
        except Exception as e:
            self.logger.error(f"Error handling form: {e}")
            return None
    
    async def _analyze_form(self, form: Locator, page: Page) -> Optional[FormInfo]:
        """Analyze form structure and extract metadata."""
        try:
            # Get form attributes
            action = await form.get_attribute('action') or page.url
            method = (await form.get_attribute('method') or 'GET').upper()
            
            # Find all input fields
            fields = []
            inputs = await form.locator('input, textarea, select').all()
            
            for input_elem in inputs:
                try:
                    field_type = await input_elem.get_attribute('type') or 'text'
                    field_name = await input_elem.get_attribute('name') or ''
                    field_id = await input_elem.get_attribute('id') or ''
                    placeholder = await input_elem.get_attribute('placeholder') or ''
                    required = await input_elem.get_attribute('required') is not None
                    
                    # Skip hidden and submit fields for analysis
                    if field_type not in ['hidden', 'submit', 'button']:
                        fields.append({
                            'type': field_type,
                            'name': field_name,
                            'id': field_id,
                            'placeholder': placeholder,
                            'required': required
                        })
                        
                except Exception as e:
                    self.logger.debug(f"Error analyzing input field: {e}")
            
            # Find submit buttons
            submit_buttons = []
            buttons = await form.locator('button, input[type="submit"], input[type="button"]').all()
            
            for button in buttons:
                try:
                    button_text = await button.text_content() or ''
                    button_value = await button.get_attribute('value') or ''
                    submit_buttons.append(button_text or button_value)
                except:
                    pass
            
            return FormInfo(
                action=action,
                method=method,
                fields=fields,
                submit_buttons=submit_buttons,
                discovered_at=datetime.utcnow(),
                page_url=page.url
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing form: {e}")
            return None
    
    async def _fill_form(self, form: Locator, form_info: FormInfo, page: Page) -> bool:
        """Fill form fields with appropriate test data."""
        try:
            filled_any = False
            
            for field in form_info.fields:
                try:
                    field_name = field['name']
                    field_type = field['type']
                    field_id = field['id']
                    
                    # Skip certain field types
                    if field_type in ['hidden', 'submit', 'button', 'file']:
                        continue
                    
                    # Determine appropriate test value
                    test_value = self._get_test_value(field)
                    
                    if not test_value:
                        continue
                    
                    # Find the input element
                    input_selector = None
                    if field_name:
                        input_selector = f'[name="{field_name}"]'
                    elif field_id:
                        input_selector = f'#{field_id}'
                    
                    if input_selector:
                        input_elem = form.locator(input_selector).first
                        
                        # Check if element exists and is visible
                        if await input_elem.count() > 0 and await input_elem.is_visible():
                            # Fill based on field type
                            if field_type == 'checkbox':
                                if not await input_elem.is_checked():
                                    await input_elem.check()
                                    filled_any = True
                            elif field_type == 'radio':
                                await input_elem.check()
                                filled_any = True
                            elif field_type == 'select':
                                # Select first non-empty option
                                options = await input_elem.locator('option').all()
                                if len(options) > 1:
                                    await input_elem.select_option(index=1)
                                    filled_any = True
                            else:
                                # Text input
                                await input_elem.clear()
                                await input_elem.fill(test_value)
                                filled_any = True
                            
                            self.logger.debug(f"Filled field {field_name} with {test_value}")
                            
                            # Small delay between field fills
                            await asyncio.sleep(0.2)
                
                except Exception as e:
                    self.logger.debug(f"Error filling field {field.get('name', 'unknown')}: {e}")
            
            return filled_any
            
        except Exception as e:
            self.logger.error(f"Error filling form: {e}")
            return False
    
    def _get_test_value(self, field: Dict[str, str]) -> Optional[str]:
        """Get appropriate test value for a form field."""
        try:
            field_name = field.get('name', '').lower()
            field_id = field.get('id', '').lower()
            placeholder = field.get('placeholder', '').lower()
            field_type = field.get('type', '').lower()
            
            # Combine all identifiers for pattern matching
            field_text = f"{field_name} {field_id} {placeholder}".lower()
            
            # Check for authentication fields first
            if self.credentials:
                if any(pattern in field_text for pattern in ['email', 'username', 'login']):
                    return self.credentials.get('username') or self.credentials.get('email')
                elif 'password' in field_text:
                    return self.credentials.get('password')
            
            # Match against test data patterns
            for data_key, patterns in self.field_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, field_text, re.IGNORECASE):
                        return self.test_data.get(data_key)
            
            # Default values based on field type
            if field_type == 'email':
                return self.test_data['email']
            elif field_type == 'password':
                return self.test_data['password']
            elif field_type == 'tel':
                return self.test_data['phone']
            elif field_type == 'url':
                return 'https://example.com'
            elif field_type == 'number':
                return '123'
            elif field_type == 'date':
                return '2024-01-01'
            elif field_type in ['text', 'search']:
                return 'test'
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting test value: {e}")
            return None
    
    async def _submit_form(self, form: Locator, form_info: FormInfo, page: Page) -> bool:
        """Submit the form if it's safe to do so."""
        try:
            # Check if this looks like a safe form to submit
            if not self._is_safe_to_submit(form_info):
                self.logger.debug(f"Skipping form submission for safety: {form_info.action}")
                return False
            
            # Find submit button
            submit_button = form.locator('button[type="submit"], input[type="submit"]').first
            
            if await submit_button.count() == 0:
                # Try generic button
                submit_button = form.locator('button').first
            
            if await submit_button.count() > 0:
                self.logger.info(f"Submitting form: {form_info.action}")
                
                # Click submit button
                await submit_button.click()
                
                # Wait for potential navigation or response
                await asyncio.sleep(2)
                
                return True
            else:
                self.logger.debug("No submit button found")
                return False
                
        except Exception as e:
            self.logger.error(f"Error submitting form: {e}")
            return False
    
    def _is_safe_to_submit(self, form_info: FormInfo) -> bool:
        """Determine if a form is safe to submit."""
        try:
            # Don't submit forms that might cause destructive actions
            dangerous_patterns = [
                'delete', 'remove', 'cancel', 'unsubscribe',
                'purchase', 'buy', 'order', 'payment',
                'transfer', 'send', 'post', 'publish'
            ]
            
            form_text = f"{form_info.action} {' '.join(form_info.submit_buttons)}".lower()
            
            for pattern in dangerous_patterns:
                if pattern in form_text:
                    return False
            
            # Only submit GET forms or forms with test-safe fields
            if form_info.method == 'GET':
                return True
            
            # Check if form has only safe field types
            safe_field_types = ['text', 'email', 'search', 'tel', 'url']
            for field in form_info.fields:
                if field['type'] not in safe_field_types and field['type'] != 'hidden':
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking form safety: {e}")
            return False
    
    def get_discovered_forms(self) -> List[FormInfo]:
        """Get all discovered forms."""
        return self.discovered_forms.copy()
    
    def get_form_statistics(self) -> Dict[str, Any]:
        """Get form discovery statistics."""
        return {
            "total_forms": len(self.discovered_forms),
            "methods_used": list(set(form.method for form in self.discovered_forms)),
            "unique_actions": len(set(form.action for form in self.discovered_forms)),
            "forms_with_auth_fields": len([
                form for form in self.discovered_forms
                if any('password' in field['name'].lower() for field in form.fields)
            ])
        }
