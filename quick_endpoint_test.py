#!/usr/bin/env python3
"""
Quick Endpoint Test

Simple test to verify endpoint counting is working correctly.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor
from src.utils.logging import setup_logging


async def quick_endpoint_test():
    """Quick test of endpoint counting."""
    print("🧪 Quick Endpoint Test")
    print("="*30)
    
    # Setup logging
    setup_logging(level="INFO", environment="development")
    
    # Initialize components
    interceptor = NetworkInterceptor()
    crawler = PlaywrightCrawler(headless=True, delay_ms=1000)
    
    try:
        # Start crawler
        await crawler.start()
        page = crawler.page
        
        # Setup network interception
        await interceptor.setup_page(page)
        
        print("🌐 Navigating to pump.fun (with shorter timeout)...")
        
        # Navigate with shorter timeout
        try:
            await page.goto("https://pump.fun", wait_until="domcontentloaded", timeout=15000)
        except:
            print("⚠️  Navigation timed out, but that's OK - we still captured requests")
        
        # Wait a bit for any remaining requests
        await asyncio.sleep(3)
        
        # Get results
        endpoints = interceptor.get_discovered_endpoints()
        stats = interceptor.get_statistics()
        
        print(f"\n📊 RESULTS:")
        print(f"Total Requests: {stats['total_requests']}")
        print(f"Total Responses: {stats['total_responses']}")
        print(f"API Endpoints Discovered: {len(endpoints)}")
        
        if len(endpoints) > 0:
            print(f"\n🔗 FIRST 10 ENDPOINTS:")
            for i, endpoint in enumerate(endpoints[:10]):
                method = endpoint.get('method', 'GET')
                url = endpoint.get('url', 'Unknown')
                print(f"  {i+1}. {method} {url[:80]}{'...' if len(url) > 80 else ''}")
            
            if len(endpoints) > 10:
                print(f"  ... and {len(endpoints) - 10} more")
            
            print(f"\n✅ SUCCESS: {len(endpoints)} endpoints discovered!")
            return True
        else:
            print("\n❌ No endpoints discovered")
            return False
        
    except Exception as e:
        print(f"💥 Error: {e}")
        return False
    
    finally:
        try:
            await crawler.stop()
        except:
            pass


async def main():
    """Main entry point."""
    success = await quick_endpoint_test()
    
    if success:
        print("\n✅ Endpoint test PASSED!")
        return 0
    else:
        print("\n❌ Endpoint test FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
