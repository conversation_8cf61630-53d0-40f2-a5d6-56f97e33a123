"""
Traffic Analyzer Utility

Provides utilities for analyzing network traffic patterns, detecting APIs,
and extracting useful information from captured requests.
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Set
from urllib.parse import urlparse, parse_qs
from collections import defaultdict, Counter


logger = logging.getLogger(__name__)


class TrafficAnalyzer:
    """
    Analyzes network traffic to identify patterns, APIs, and security issues.
    """
    
    def __init__(self):
        """Initialize the traffic analyzer."""
        self.api_patterns = [
            r'/api/',
            r'/v\d+/',
            r'\.json',
            r'/graphql',
            r'/rest/',
            r'/oauth/',
            r'/auth/',
            r'/login',
            r'/token'
        ]
        
        self.technology_patterns = {
            'React': [r'react', r'_react', r'ReactDOM'],
            'Vue': [r'vue\.js', r'vuejs', r'__vue__'],
            'Angular': [r'angular', r'ng-', r'@angular'],
            'jQuery': [r'jquery', r'\$\.'],
            'GraphQL': [r'graphql', r'__schema'],
            'REST API': [r'/api/', r'/rest/'],
            'WebSocket': [r'ws://', r'wss://'],
            'OAuth': [r'/oauth/', r'access_token'],
            'JWT': [r'Bearer\s+[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+']
        }
        
        self.security_headers = [
            'content-security-policy',
            'x-frame-options',
            'x-content-type-options',
            'strict-transport-security',
            'x-xss-protection',
            'referrer-policy'
        ]
    
    def is_api_request(self, request: Any) -> bool:
        """
        Determine if a request appears to be an API call.
        
        Args:
            request: Request object or dictionary
            
        Returns:
            True if appears to be API request
        """
        try:
            url = self._get_url(request)
            if not url:
                return False
            
            url_lower = url.lower()
            
            # Check URL patterns
            for pattern in self.api_patterns:
                if re.search(pattern, url_lower):
                    return True
            
            # Check content type
            headers = self._get_headers(request)
            content_type = headers.get('content-type', '').lower()
            if 'application/json' in content_type or 'application/xml' in content_type:
                return True
            
            # Check for AJAX requests
            if headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if API request: {e}")
            return False
    
    def extract_domain(self, url: str) -> str:
        """
        Extract domain from URL.
        
        Args:
            url: URL string
            
        Returns:
            Domain name
        """
        try:
            parsed = urlparse(url)
            return parsed.netloc
        except:
            return "unknown"
    
    def analyze_request_patterns(self, requests: List[Any]) -> Dict[str, Any]:
        """
        Analyze patterns in a collection of requests.
        
        Args:
            requests: List of request objects
            
        Returns:
            Pattern analysis results
        """
        patterns = {
            "url_patterns": [],
            "parameter_patterns": [],
            "method_distribution": {},
            "status_distribution": {},
            "content_types": {},
            "domains": set(),
            "api_endpoints": [],
            "technologies": set()
        }
        
        try:
            method_counter = Counter()
            status_counter = Counter()
            content_type_counter = Counter()
            
            for request in requests:
                url = self._get_url(request)
                method = self._get_method(request)
                
                if url:
                    patterns["domains"].add(self.extract_domain(url))
                    
                    if self.is_api_request(request):
                        patterns["api_endpoints"].append(url)
                
                if method:
                    method_counter[method] += 1
                
                # Analyze response if available
                response = self._get_response(request)
                if response:
                    status = response.get('statusCode') or response.get('status')
                    if status:
                        status_counter[status] += 1
                    
                    headers = response.get('headers', {})
                    content_type = headers.get('content-type', '')
                    if content_type:
                        content_type_counter[content_type] += 1
                
                # Detect technologies
                technologies = self.detect_technologies(request)
                patterns["technologies"].update(technologies)
            
            # Convert counters to dictionaries
            patterns["method_distribution"] = dict(method_counter)
            patterns["status_distribution"] = dict(status_counter)
            patterns["content_types"] = dict(content_type_counter)
            patterns["domains"] = list(patterns["domains"])
            patterns["technologies"] = list(patterns["technologies"])
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing request patterns: {e}")
            return patterns
    
    def detect_technologies(self, request: Any) -> Set[str]:
        """
        Detect technologies used based on request/response data.
        
        Args:
            request: Request object
            
        Returns:
            Set of detected technologies
        """
        technologies = set()
        
        try:
            url = self._get_url(request)
            headers = self._get_headers(request)
            response = self._get_response(request)
            
            # Check URL patterns
            if url:
                for tech, patterns in self.technology_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, url, re.IGNORECASE):
                            technologies.add(tech)
            
            # Check headers
            for header_name, header_value in headers.items():
                for tech, patterns in self.technology_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, f"{header_name}: {header_value}", re.IGNORECASE):
                            technologies.add(tech)
            
            # Check response headers
            if response and 'headers' in response:
                response_headers = response['headers']
                server = response_headers.get('server', '')
                if server:
                    if 'nginx' in server.lower():
                        technologies.add('Nginx')
                    elif 'apache' in server.lower():
                        technologies.add('Apache')
                    elif 'cloudflare' in server.lower():
                        technologies.add('Cloudflare')
            
            return technologies
            
        except Exception as e:
            logger.error(f"Error detecting technologies: {e}")
            return technologies
    
    def analyze_security_headers(self, requests: List[Any]) -> Dict[str, Any]:
        """
        Analyze security headers in responses.
        
        Args:
            requests: List of request objects
            
        Returns:
            Security analysis results
        """
        security_analysis = {
            "headers_found": {},
            "missing_headers": [],
            "security_score": 0,
            "recommendations": []
        }
        
        try:
            headers_found = set()
            
            for request in requests:
                response = self._get_response(request)
                if response and 'headers' in response:
                    response_headers = response['headers']
                    
                    for header in self.security_headers:
                        if header in response_headers:
                            headers_found.add(header)
                            security_analysis["headers_found"][header] = response_headers[header]
            
            # Determine missing headers
            security_analysis["missing_headers"] = [
                header for header in self.security_headers 
                if header not in headers_found
            ]
            
            # Calculate security score
            security_analysis["security_score"] = len(headers_found) / len(self.security_headers) * 100
            
            # Generate recommendations
            if 'content-security-policy' not in headers_found:
                security_analysis["recommendations"].append("Implement Content Security Policy (CSP)")
            
            if 'strict-transport-security' not in headers_found:
                security_analysis["recommendations"].append("Enable HTTP Strict Transport Security (HSTS)")
            
            if 'x-frame-options' not in headers_found:
                security_analysis["recommendations"].append("Add X-Frame-Options header to prevent clickjacking")
            
            return security_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing security headers: {e}")
            return security_analysis
    
    def extract_parameters(self, requests: List[Any]) -> Dict[str, Any]:
        """
        Extract and analyze parameters from requests.
        
        Args:
            requests: List of request objects
            
        Returns:
            Parameter analysis results
        """
        parameters = {
            "query_params": set(),
            "form_params": set(),
            "json_params": set(),
            "common_patterns": [],
            "sensitive_params": []
        }
        
        sensitive_keywords = [
            'password', 'token', 'key', 'secret', 'auth', 'session',
            'api_key', 'access_token', 'refresh_token', 'csrf'
        ]
        
        try:
            for request in requests:
                url = self._get_url(request)
                
                # Extract query parameters
                if url:
                    parsed = urlparse(url)
                    query_params = parse_qs(parsed.query)
                    parameters["query_params"].update(query_params.keys())
                
                # Extract form parameters
                body = self._get_body(request)
                if body:
                    try:
                        # Try to parse as JSON
                        json_data = json.loads(body)
                        if isinstance(json_data, dict):
                            parameters["json_params"].update(json_data.keys())
                    except:
                        # Try to parse as form data
                        if 'application/x-www-form-urlencoded' in self._get_content_type(request):
                            form_params = parse_qs(body)
                            parameters["form_params"].update(form_params.keys())
            
            # Convert sets to lists
            parameters["query_params"] = list(parameters["query_params"])
            parameters["form_params"] = list(parameters["form_params"])
            parameters["json_params"] = list(parameters["json_params"])
            
            # Find sensitive parameters
            all_params = set(parameters["query_params"] + parameters["form_params"] + parameters["json_params"])
            for param in all_params:
                for keyword in sensitive_keywords:
                    if keyword in param.lower():
                        parameters["sensitive_params"].append(param)
                        break
            
            return parameters
            
        except Exception as e:
            logger.error(f"Error extracting parameters: {e}")
            return parameters
    
    def _get_url(self, request: Any) -> str:
        """Get URL from request object."""
        if isinstance(request, dict):
            return request.get('url', '')
        return getattr(request, 'url', '')
    
    def _get_method(self, request: Any) -> str:
        """Get HTTP method from request object."""
        if isinstance(request, dict):
            return request.get('method', 'GET')
        return getattr(request, 'method', 'GET')
    
    def _get_headers(self, request: Any) -> Dict[str, str]:
        """Get headers from request object."""
        if isinstance(request, dict):
            return request.get('headers', {})
        return getattr(request, 'headers', {})
    
    def _get_response(self, request: Any) -> Optional[Dict[str, Any]]:
        """Get response from request object."""
        if isinstance(request, dict):
            return request.get('response')
        return getattr(request, 'response', None)
    
    def _get_body(self, request: Any) -> Optional[str]:
        """Get request body."""
        if isinstance(request, dict):
            return request.get('body') or request.get('postData')
        return getattr(request, 'body', None) or getattr(request, 'postData', None)
    
    def _get_content_type(self, request: Any) -> str:
        """Get content type from request headers."""
        headers = self._get_headers(request)
        return headers.get('content-type', '').lower()


# Global instance
traffic_analyzer = TrafficAnalyzer()
