#!/usr/bin/env python3
"""
Cipher-Spy Command Line Interface

Main entry point for the Cipher-Spy autonomous web reconnaissance system.
Provides multiple modes of operation including API server, standalone crawler,
and interactive demo.
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path
from urllib.parse import urlparse

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.logging import setup_logging
from src.config.settings import get_settings


def create_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Cipher-Spy: AI-Driven Red Team Swarm for Autonomous Web Reconnaissance",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run API server
  python cipher-spy.py server

  # Crawl a specific target
  python cipher-spy.py crawl https://pump.fun

  # Run interactive demo
  python cipher-spy.py demo

  # Crawl with custom configuration
  python cipher-spy.py crawl https://example.com --config config.json --depth 3

  # Run in safe mode with verbose output
  python cipher-spy.py crawl https://target.com --safe-mode --verbose
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Server command
    server_parser = subparsers.add_parser('server', help='Run API server')
    server_parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    server_parser.add_argument('--port', type=int, default=8000, help='Port to bind to')
    server_parser.add_argument('--reload', action='store_true', help='Enable auto-reload')
    
    # Crawl command
    crawl_parser = subparsers.add_parser('crawl', help='Run standalone crawler')
    crawl_parser.add_argument('target', help='Target URL to crawl')
    crawl_parser.add_argument('--config', help='Configuration file path')
    crawl_parser.add_argument('--depth', type=int, default=5, help='Maximum crawl depth')
    crawl_parser.add_argument('--pages', type=int, default=100, help='Maximum pages to crawl')
    crawl_parser.add_argument('--delay', type=int, default=1000, help='Delay between requests (ms)')
    crawl_parser.add_argument('--safe-mode', action='store_true', default=True, help='Enable safe mode')
    crawl_parser.add_argument('--no-safe-mode', action='store_false', dest='safe_mode', help='Disable safe mode')
    crawl_parser.add_argument('--headless', action='store_true', default=True, help='Run browser in headless mode')
    crawl_parser.add_argument('--show-browser', action='store_false', dest='headless', help='Show browser window')
    crawl_parser.add_argument('--output', help='Output directory for results')
    crawl_parser.add_argument('--credentials', help='JSON file with authentication credentials')
    
    # Demo command
    demo_parser = subparsers.add_parser('demo', help='Run interactive demo')
    demo_parser.add_argument('--target', default='https://pump.fun', help='Demo target URL')
    
    # Global options
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--log-file', help='Log file path')
    
    return parser


async def run_server(args):
    """Run the API server."""
    import uvicorn
    from src.main import create_app, setup_signal_handlers
    
    print("🚀 Starting Cipher-Spy API Server")
    print(f"🌐 Server will be available at: http://{args.host}:{args.port}")
    print(f"📚 API documentation: http://{args.host}:{args.port}/docs")
    
    setup_signal_handlers()
    app = create_app()
    
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )


async def run_crawler(args):
    """Run the standalone crawler."""
    from src.core.state import ScanState, TargetInfo
    from src.core.workflow import create_workflow
    from datetime import datetime
    
    print("🕷️  Starting Cipher-Spy Autonomous Crawler")
    print(f"🎯 Target: {args.target}")
    
    # Parse target URL
    parsed = urlparse(args.target)
    if not parsed.scheme:
        args.target = f"https://{args.target}"
        parsed = urlparse(args.target)
    
    domain = parsed.netloc
    
    # Load configuration
    config = {}
    if args.config:
        with open(args.config, 'r') as f:
            config = json.load(f)
    
    # Override with command line arguments
    config.update({
        "max_crawl_depth": args.depth,
        "max_pages_per_domain": args.pages,
        "crawl_delay_ms": args.delay,
        "safe_mode": args.safe_mode,
        "headless": args.headless
    })
    
    # Load credentials if provided
    credentials = None
    if args.credentials:
        with open(args.credentials, 'r') as f:
            credentials = json.load(f)
    
    # Create target
    target = TargetInfo(
        url=args.target,
        domain=domain,
        scope=[domain, f"*.{domain}"],
        credentials=credentials
    )
    
    # Create scan state
    state = ScanState(target=target, config=config)
    
    print(f"🆔 Scan ID: {state.scan_id}")
    print(f"⚙️  Configuration:")
    print(f"   Max Depth: {args.depth}")
    print(f"   Max Pages: {args.pages}")
    print(f"   Delay: {args.delay}ms")
    print(f"   Safe Mode: {'✅' if args.safe_mode else '❌'}")
    print(f"   Headless: {'✅' if args.headless else '❌'}")
    
    try:
        # Create and run workflow
        workflow_config = {"crawler": config}
        workflow = create_workflow(workflow_config)
        
        start_time = datetime.now()
        final_state = await workflow.execute(state)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        # Print results
        print(f"\n📊 CRAWLING RESULTS")
        print(f"{'='*50}")
        print(f"✅ Scan completed in {duration:.2f} seconds")
        print(f"📄 Pages discovered: {len(final_state.pages)}")
        print(f"🔗 Endpoints discovered: {len(final_state.endpoints)}")
        print(f"📊 Final status: {final_state.status}")
        
        if final_state.pages:
            print(f"\n📄 Sample Pages:")
            for i, page in enumerate(final_state.pages[:5], 1):
                print(f"  {i}. {page.url}")
                if page.title:
                    print(f"     📝 {page.title}")
        
        if final_state.endpoints:
            print(f"\n🔗 Sample Endpoints:")
            for i, endpoint in enumerate(final_state.endpoints[:5], 1):
                print(f"  {i}. {endpoint.method} {endpoint.url}")
                if endpoint.response_status:
                    print(f"     📊 Status: {endpoint.response_status}")
        
        # Save results if output directory specified
        if args.output:
            await save_results(final_state, args.output, duration)
        
        # Cleanup
        await workflow.cleanup()
        
        print(f"\n🎉 Crawling completed successfully!")
        
    except Exception as e:
        print(f"\n💥 Crawling failed: {e}")
        import traceback
        if args.debug:
            traceback.print_exc()
        return 1
    
    return 0


async def run_demo(args):
    """Run the interactive demo."""
    print("🎬 Starting Cipher-Spy Interactive Demo")
    print(f"🎯 Target: {args.target}")
    
    # Import and run demo
    try:
        from demo_crawler import CrawlerDemo
        
        demo = CrawlerDemo()
        success = await demo.run_pump_fun_demo()
        
        if success:
            print("\n🎉 Demo completed successfully!")
            return 0
        else:
            print("\n❌ Demo failed!")
            return 1
            
    except ImportError:
        print("❌ Demo module not found. Please ensure demo_crawler.py is available.")
        return 1
    except Exception as e:
        print(f"💥 Demo failed: {e}")
        return 1


async def save_results(state, output_dir, duration):
    """Save crawling results to output directory."""
    import json
    from datetime import datetime
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save JSON report
    report = {
        "scan_metadata": {
            "scan_id": state.scan_id,
            "target_url": state.target.url,
            "duration_seconds": duration,
            "timestamp": datetime.now().isoformat(),
            "status": state.status
        },
        "summary": {
            "pages_discovered": len(state.pages),
            "endpoints_discovered": len(state.endpoints)
        },
        "pages": [
            {
                "url": page.url,
                "title": page.title,
                "status_code": page.status_code
            }
            for page in state.pages
        ],
        "endpoints": [
            {
                "url": endpoint.url,
                "method": endpoint.method,
                "response_status": endpoint.response_status
            }
            for endpoint in state.endpoints
        ]
    }
    
    report_file = output_path / f"scan_report_{state.scan_id}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"💾 Results saved to: {report_file}")


def main():
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Setup logging
    log_level = "DEBUG" if args.debug else "INFO" if args.verbose else "WARNING"
    setup_logging(
        level=log_level,
        environment="development",
        log_file=Path(args.log_file) if args.log_file else None
    )
    
    # Print banner
    print("🔬 Cipher-Spy: AI-Driven Red Team Swarm")
    print("🕷️  Autonomous Web Reconnaissance Agent")
    print("="*50)
    
    # Route to appropriate command
    if args.command == 'server':
        asyncio.run(run_server(args))
    elif args.command == 'crawl':
        exit_code = asyncio.run(run_crawler(args))
        sys.exit(exit_code)
    elif args.command == 'demo':
        exit_code = asyncio.run(run_demo(args))
        sys.exit(exit_code)
    else:
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
