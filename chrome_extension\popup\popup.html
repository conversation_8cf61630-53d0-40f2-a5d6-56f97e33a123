<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Cipher-Spy Network Monitor</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        <span class="icon">CS</span>
        <span class="title">Cipher-Spy</span>
      </div>
      <div class="status" id="status">
        <span class="status-indicator" id="statusIndicator"></span>
        <span class="status-text" id="statusText">Stopped</span>
      </div>
    </div>

    <!-- Main Controls -->
    <div class="controls">
      <button id="toggleMonitoring" class="btn btn-primary">Start Monitoring</button>
      <button id="clearRequests" class="btn btn-secondary">Clear Data</button>
    </div>

    <!-- Statistics -->
    <div class="stats">
      <div class="stat-item">
        <div class="stat-number" id="requestCount">0</div>
        <div class="stat-label">Requests</div>
      </div>
      <div class="stat-item">
        <div class="stat-number" id="apiCount">0</div>
        <div class="stat-label">APIs</div>
      </div>
      <div class="stat-item">
        <div class="stat-number" id="errorCount">0</div>
        <div class="stat-label">Errors</div>
      </div>
    </div>

    <!-- Session Info -->
    <div class="session-info">
      <div class="session-item">
        <span class="label">Session:</span>
        <span class="value" id="sessionId">-</span>
      </div>
      <div class="session-item">
        <span class="label">Duration:</span>
        <span class="value" id="sessionDuration">-</span>
      </div>
    </div>

    <!-- Recent Requests -->
    <div class="section">
      <div class="section-header">
        <h3>Recent Requests</h3>
        <button id="refreshRequests" class="btn-icon" title="Refresh">🔄</button>
      </div>
      <div class="request-list" id="requestList">
        <div class="empty-state">No requests captured yet</div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="section">
      <div class="section-header">
        <h3>Quick Actions</h3>
      </div>
      <div class="actions">
        <button id="exportData" class="btn btn-outline">Export Data</button>
        <button id="analyzeSession" class="btn btn-outline">Analyze Session</button>
        <button id="openDevtools" class="btn btn-outline">Open DevTools</button>
      </div>
    </div>

    <!-- Settings Toggle -->
    <div class="settings-toggle">
      <button id="settingsToggle" class="btn-text">Settings</button>
    </div>

    <!-- Settings Panel (Hidden by default) -->
    <div class="settings-panel" id="settingsPanel" style="display: none;">
      <div class="setting-group">
        <label class="setting-label">
          <input type="checkbox" id="captureHeaders"> Capture Headers
        </label>
        <label class="setting-label">
          <input type="checkbox" id="capturePayloads"> Capture Payloads
        </label>
        <label class="setting-label">
          <input type="checkbox" id="autoAnalysis"> Auto Analysis
        </label>
      </div>

      <div class="setting-group">
        <label class="setting-label">Backend URL:</label>
        <input type="text" id="backendUrl" class="setting-input" placeholder="http://localhost:8000">
      </div>

      <div class="setting-group">
        <label class="setting-label">Filter Patterns (comma-separated):</label>
        <textarea id="filterPatterns" class="setting-textarea" placeholder="/api/, .json, /graphql"></textarea>
      </div>

      <div class="setting-group">
        <label class="setting-label">Exclude Patterns (comma-separated):</label>
        <textarea id="excludePatterns" class="setting-textarea" placeholder=".css, .js, .png"></textarea>
      </div>

      <div class="setting-actions">
        <button id="saveSettings" class="btn btn-primary">Save Settings</button>
        <button id="resetSettings" class="btn btn-secondary">Reset</button>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <span class="version">v1.0.0</span>
      <a href="#" id="helpLink" class="help-link">Help</a>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
