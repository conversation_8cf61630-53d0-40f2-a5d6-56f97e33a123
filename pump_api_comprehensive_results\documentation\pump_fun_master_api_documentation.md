# Pump.fun API Documentation

## Executive Summary
Pump.fun provides a set of public APIs that enable developers to access cryptocurrency market data, trending coins, personalized recommendations, and system status information. This documentation covers four primary endpoints that serve different aspects of the pump.fun platform.

## Quick Start Guide

### Base URLs
- Primary API: `https://frontend-api-v3.pump.fun`
- Secondary API: `https://pump.fun/api`

### Making Your First Request
```bash
curl https://frontend-api-v3.pump.fun/coins?offset=0&limit=50&sort=market_cap&order=DESC&includeNsfw=false
```

## API Categories

### 1. Market Data APIs
#### Trending Coins
```http
GET /coins
Base URL: https://frontend-api-v3.pump.fun
```
Parameters:
- `offset` (integer): Starting position
- `limit` (integer): Number of results (max 50)
- `sort` (string): Sort field (e.g., market_cap)
- `order` (string): Sort order (ASC/DESC)
- `includeNsfw` (boolean): Include NSFW content

### 2. Personalization APIs
#### For You Recommendations
```http
GET /coins/for-you
Base URL: https://frontend-api-v3.pump.fun
```
Parameters:
- `offset` (integer): Starting position
- `limit` (integer): Number of results (max 48)
- `includeNsfw` (boolean): Include NSFW content

### 3. System Status APIs
#### Feature Flags
```http
GET /api/flags
Base URL: https://pump.fun
```

#### System Runners
```http
GET /api/runners
Base URL: https://pump.fun
```

## Authentication
Currently, no authentication is required for the documented endpoints. However, implementing rate limiting in your applications is recommended.

## Rate Limiting
While official rate limits are not documented, we recommend:
- Implementing exponential backoff
- Caching responses where appropriate
- Limiting requests to no more than 1 request per second

## Error Handling
Common HTTP status codes:
- 200: Successful request
- 400: Bad request
- 429: Too many requests
- 500: Server error

## Integration Examples

### JavaScript/Node.js
```javascript
async function getTrendingCoins() {
  const response = await fetch(
    'https://frontend-api-v3.pump.fun/coins?offset=0&limit=50&sort=market_cap&order=DESC&includeNsfw=false'
  );
  return await response.json();
}
```

### Python
```python
import requests

def get_trending_coins():
    url = "https://frontend-api-v3.pump.fun/coins"
    params = {
        "offset": 0,
        "limit": 50,
        "sort": "market_cap",
        "order": "DESC",
        "includeNsfw": False
    }
    response = requests.get(url, params=params)
    return response.json()
```

## Business Use Cases

1. Market Analysis
   - Track trending cryptocurrencies
   - Monitor market movements
   - Analyze popularity patterns

2. Portfolio Management
   - Get personalized coin recommendations
   - Track system status
   - Monitor market trends

3. Data Analytics
   - Historical trend analysis
   - Market sentiment analysis
   - Performance tracking

## Technical Architecture

### Response Times
- Trending Coins: ~513ms
- For You Recommendations: ~163ms
- Feature Flags: ~229ms
- System Runners: ~335ms

### Response Sizes
- Trending Coins: ~63.8KB
- For You Recommendations: ~54.2KB
- Feature Flags: ~0.6KB
- System Runners: ~12.1KB

## Developer Resources

### Best Practices
1. Implement caching for frequently accessed data
2. Use error handling and retry logic
3. Monitor response times and sizes
4. Implement rate limiting in your applications

### Tools
- Postman Collection (coming soon)
- API Health Monitor (coming soon)
- Sample Code Repository (coming soon)

### Support
For technical support or questions, please refer to pump.fun's official channels.

## Changelog
- Initial documentation release covering 4 primary endpoints
- Documented response times and sizes
- Added integration examples

---

**Note**: This documentation is based on reverse-engineered endpoints and may not represent the complete API offering of pump.fun. Always refer to official documentation when available.