#!/usr/bin/env python3
"""
Test script for the Cipher-Spy crawler agent.

Demonstrates autonomous web reconnaissance and API discovery
capabilities on the pump.fun target.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.state import ScanState, TargetInfo
from src.core.workflow import create_workflow
from src.utils.logging import setup_logging
from src.config.settings import get_settings


async def test_crawler_on_pump_fun():
    """Test the crawler agent on pump.fun."""
    print("🚀 Starting Cipher-Spy Crawler Test on pump.fun")
    
    # Setup logging
    setup_logging(level="INFO", environment="development")
    
    # Create target info
    target = TargetInfo(
        url="https://pump.fun",
        domain="pump.fun",
        scope=["pump.fun", "*.pump.fun"]
    )
    
    # Create initial scan state
    state = ScanState(
        target=target,
        config={
            "max_crawl_depth": 3,
            "max_pages_per_domain": 50,
            "crawl_delay_ms": 2000,  # Be respectful
            "respect_robots_txt": True,
            "safe_mode": True
        }
    )
    
    print(f"🎯 Target: {target.url}")
    print(f"📊 Scan ID: {state.scan_id}")
    
    try:
        # Create and configure workflow
        workflow_config = {
            "crawler": {
                "max_crawl_depth": 3,
                "max_pages_per_domain": 50,
                "crawl_delay_ms": 2000,
                "headless": True  # Run in headless mode for testing
            }
        }
        
        workflow = create_workflow(workflow_config)
        
        print("🔄 Starting workflow execution...")
        
        # Execute the workflow
        final_state = await workflow.execute(state)
        
        # Print results
        print("\n" + "="*60)
        print("📈 CRAWLING RESULTS")
        print("="*60)
        
        print(f"Status: {final_state.status}")
        print(f"Pages discovered: {len(final_state.pages)}")
        print(f"Endpoints discovered: {len(final_state.endpoints)}")
        
        if final_state.pages:
            print("\n📄 Discovered Pages:")
            for i, page in enumerate(final_state.pages[:10], 1):  # Show first 10
                print(f"  {i}. {page.url}")
                if page.title:
                    print(f"     Title: {page.title}")
                if page.screenshot_path:
                    print(f"     Screenshot: {page.screenshot_path}")
        
        if final_state.endpoints:
            print("\n🔗 Discovered API Endpoints:")
            for i, endpoint in enumerate(final_state.endpoints[:10], 1):  # Show first 10
                print(f"  {i}. {endpoint.method} {endpoint.url}")
                if endpoint.response_status:
                    print(f"     Status: {endpoint.response_status}")
                if endpoint.response_content_type:
                    print(f"     Content-Type: {endpoint.response_content_type}")
        
        # Agent statistics
        print("\n🤖 Agent Performance:")
        for agent_id, agent in final_state.agents.items():
            print(f"  {agent_id}: {agent.status} ({agent.progress:.1%})")
            if agent.current_task:
                print(f"    Task: {agent.current_task}")
            if agent.error_message:
                print(f"    Error: {agent.error_message}")
        
        # Workflow summary
        summary = final_state.get_summary()
        print(f"\n📊 Summary:")
        print(f"  Total pages: {summary['pages_discovered']}")
        print(f"  Total endpoints: {summary['endpoints_discovered']}")
        print(f"  Technologies detected: {summary['technologies_detected']}")
        print(f"  Vulnerabilities found: {summary['vulnerabilities_found']}")
        
        if final_state.error_message:
            print(f"\n❌ Error: {final_state.error_message}")
        
        print("\n✅ Test completed successfully!")
        
        # Cleanup
        await workflow.cleanup()
        
    except Exception as e:
        print(f"\n💥 Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def test_crawler_components():
    """Test individual crawler components."""
    print("\n🧪 Testing Crawler Components")
    
    from src.crawling.playwright_crawler import PlaywrightCrawler
    from src.crawling.network_interceptor import NetworkInterceptor
    from src.crawling.form_handler import FormHandler
    from src.crawling.scope_manager import ScopeManager
    
    try:
        # Test scope manager
        print("Testing ScopeManager...")
        scope_manager = ScopeManager(
            base_url="https://pump.fun",
            allowed_domains=["pump.fun", "*.pump.fun"],
            respect_robots=True
        )
        
        test_urls = [
            "https://pump.fun/about",
            "https://api.pump.fun/tokens",
            "https://evil.com/malicious",
            "https://sub.pump.fun/data"
        ]
        
        for url in test_urls:
            in_scope = scope_manager.is_in_scope(url)
            print(f"  {url}: {'✅' if in_scope else '❌'}")
        
        # Test network interceptor
        print("\nTesting NetworkInterceptor...")
        network_interceptor = NetworkInterceptor()
        stats = network_interceptor.get_statistics()
        print(f"  Initial stats: {stats}")
        
        # Test form handler
        print("\nTesting FormHandler...")
        form_handler = FormHandler()
        form_stats = form_handler.get_form_statistics()
        print(f"  Initial form stats: {form_stats}")
        
        print("✅ Component tests completed!")
        
    except Exception as e:
        print(f"💥 Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def main():
    """Main test function."""
    print("🔬 Cipher-Spy Crawler Test Suite")
    print("="*50)
    
    # Test components first
    component_success = await test_crawler_components()
    
    if component_success:
        # Test full crawler on pump.fun
        crawler_success = await test_crawler_on_pump_fun()
        
        if crawler_success:
            print("\n🎉 All tests passed!")
            return 0
        else:
            print("\n❌ Crawler test failed!")
            return 1
    else:
        print("\n❌ Component tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
