#!/usr/bin/env python3
"""
Test Cipher-Spy Server

Simple script to test that the Cipher-Spy backend server is running
and responding correctly.
"""

import requests
import json
import time


def test_server():
    """Test the Cipher-Spy server endpoints."""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Cipher-Spy Server")
    print("="*30)
    
    # Test 1: Health check
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Health check passed")
            print(f"   📊 Status: {data.get('status')}")
            print(f"   🏷️ Environment: {data.get('environment')}")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
        return False
    
    # Test 2: Root endpoint
    print("\n2. Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Root endpoint working")
            print(f"   📛 Name: {data.get('name')}")
            print(f"   📝 Description: {data.get('description')}")
        else:
            print(f"   ❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Root endpoint error: {e}")
    
    # Test 3: Extension API endpoints
    print("\n3. Testing extension API endpoints...")
    try:
        # Test extension health
        response = requests.get(f"{base_url}/api/v1/extension/health", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ Extension health endpoint working")
        else:
            print(f"   ⚠️ Extension health: {response.status_code}")
        
        # Test sessions list
        response = requests.get(f"{base_url}/api/v1/extension/sessions", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Sessions endpoint working")
            print(f"   📊 Active sessions: {data.get('active_sessions', 0)}")
        else:
            print(f"   ⚠️ Sessions endpoint: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Extension API error: {e}")
    
    # Test 4: CORS headers
    print("\n4. Testing CORS configuration...")
    try:
        response = requests.options(f"{base_url}/health", timeout=5)
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        if any(cors_headers.values()):
            print(f"   ✅ CORS headers present")
            for header, value in cors_headers.items():
                if value:
                    print(f"   🔗 {header}: {value}")
        else:
            print(f"   ⚠️ No CORS headers found")
            
    except Exception as e:
        print(f"   ❌ CORS test error: {e}")
    
    print("\n🎉 Server tests completed!")
    print("\n📋 Next Steps:")
    print("1. ✅ Server is running on http://localhost:8000")
    print("2. 🌐 Install Chrome extension from chrome_extension/ folder")
    print("3. 🔗 Extension will connect to this backend automatically")
    print("4. 📊 Check /docs for API documentation")
    
    return True


def test_extension_workflow():
    """Test a basic extension workflow."""
    base_url = "http://localhost:8000/api/v1/extension"
    
    print("\n🔄 Testing Extension Workflow")
    print("="*35)
    
    try:
        # Create a test session
        session_data = {
            "session_id": f"test_session_{int(time.time())}",
            "metadata": {"test": True, "browser": "Chrome"}
        }
        
        print("1. Creating test session...")
        response = requests.post(f"{base_url}/sessions", json=session_data, timeout=5)
        if response.status_code == 200:
            session_id = session_data["session_id"]
            print(f"   ✅ Session created: {session_id}")
            
            # Add a test request
            print("2. Adding test request...")
            request_data = {
                "id": "test_request_1",
                "url": "https://api.example.com/test",
                "method": "GET",
                "headers": {"Content-Type": "application/json"},
                "timestamp": int(time.time() * 1000)
            }
            
            response = requests.post(
                f"{base_url}/sessions/{session_id}/requests", 
                json=request_data, 
                timeout=5
            )
            if response.status_code == 200:
                print(f"   ✅ Request added successfully")
                
                # Get session summary
                print("3. Getting session summary...")
                response = requests.get(f"{base_url}/sessions/{session_id}/summary", timeout=5)
                if response.status_code == 200:
                    summary = response.json()
                    print(f"   ✅ Session summary retrieved")
                    print(f"   📊 Total requests: {summary.get('total_requests', 0)}")
                    print(f"   🌐 Unique domains: {summary.get('unique_domains', 0)}")
                else:
                    print(f"   ⚠️ Summary failed: {response.status_code}")
            else:
                print(f"   ❌ Add request failed: {response.status_code}")
        else:
            print(f"   ❌ Session creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Workflow test error: {e}")


if __name__ == "__main__":
    try:
        success = test_server()
        if success:
            test_extension_workflow()
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed: {e}")
