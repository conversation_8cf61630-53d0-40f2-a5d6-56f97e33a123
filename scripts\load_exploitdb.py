#!/usr/bin/env python3
"""
Exploit-DB data loading script for Cipher-Spy.

Downloads and processes Exploit-DB data, creating knowledge graph
nodes and relationships for vulnerability and exploit information.
"""

import asyncio
import csv
import json
import logging
import sys
from pathlib import Path
from typing import Dict, List, Optional
import subprocess
import re

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.config.settings import get_settings
from src.config.database import DatabaseManager
from src.utils.logging import setup_logging


class ExploitDBLoader:
    """Loads and processes Exploit-DB data into the knowledge graph."""
    
    def __init__(self):
        self.settings = get_settings()
        self.db_manager = DatabaseManager()
        self.exploitdb_path = self.settings.data_directory / "exploitdb"
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            "exploits_processed": 0,
            "cves_processed": 0,
            "technologies_processed": 0,
            "relationships_created": 0,
            "errors": 0
        }
    
    async def download_exploitdb(self) -> bool:
        """Download or update Exploit-DB repository."""
        print("📥 Downloading Exploit-DB repository...")
        
        try:
            if self.exploitdb_path.exists():
                # Update existing repository
                print("Updating existing Exploit-DB repository...")
                result = subprocess.run(
                    ["git", "pull"],
                    cwd=self.exploitdb_path,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
            else:
                # Clone repository
                print("Cloning Exploit-DB repository...")
                self.exploitdb_path.parent.mkdir(parents=True, exist_ok=True)
                result = subprocess.run(
                    [
                        "git", "clone", 
                        "https://github.com/offensive-security/exploitdb.git",
                        str(self.exploitdb_path)
                    ],
                    capture_output=True,
                    text=True,
                    timeout=600
                )
            
            if result.returncode == 0:
                print("✓ Exploit-DB repository ready")
                return True
            else:
                print(f"✗ Git operation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ Git operation timed out")
            return False
        except Exception as e:
            print(f"✗ Download failed: {e}")
            return False
    
    async def load_exploits(self) -> None:
        """Load exploit data from CSV files."""
        print("📊 Loading exploit data...")
        
        # Files to process
        csv_files = [
            "files_exploits.csv",
            "files_shellcodes.csv", 
            "files_papers.csv"
        ]
        
        async with self.db_manager.get_neo4j_session() as session:
            for csv_file in csv_files:
                file_path = self.exploitdb_path / csv_file
                if not file_path.exists():
                    print(f"⚠ File not found: {csv_file}")
                    continue
                
                print(f"Processing {csv_file}...")
                await self._process_csv_file(session, file_path, csv_file)
    
    async def _process_csv_file(self, session, file_path: Path, file_type: str) -> None:
        """Process a single CSV file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                reader = csv.DictReader(f)
                batch_size = 100
                batch = []
                
                for row in reader:
                    try:
                        # Process row based on file type
                        if "exploits" in file_type:
                            node_data = self._process_exploit_row(row)
                        elif "shellcodes" in file_type:
                            node_data = self._process_shellcode_row(row)
                        elif "papers" in file_type:
                            node_data = self._process_paper_row(row)
                        else:
                            continue
                        
                        if node_data:
                            batch.append(node_data)
                        
                        # Process batch
                        if len(batch) >= batch_size:
                            await self._create_nodes_batch(session, batch)
                            batch = []
                            
                    except Exception as e:
                        self.logger.error(f"Error processing row: {e}")
                        self.stats["errors"] += 1
                
                # Process remaining batch
                if batch:
                    await self._create_nodes_batch(session, batch)
                    
        except Exception as e:
            print(f"✗ Error processing {file_path}: {e}")
    
    def _process_exploit_row(self, row: Dict) -> Optional[Dict]:
        """Process an exploit row from CSV."""
        try:
            # Extract basic information
            edb_id = row.get('id', '').strip()
            description = row.get('description', '').strip()
            date = row.get('date', '').strip()
            author = row.get('author', '').strip()
            platform = row.get('platform', '').strip()
            exploit_type = row.get('type', '').strip()
            port = row.get('port', '').strip()
            
            if not edb_id or not description:
                return None
            
            # Extract CVE IDs from description
            cve_ids = self._extract_cve_ids(description)
            
            # Extract technology information
            technologies = self._extract_technologies(description, platform)
            
            node_data = {
                'type': 'exploit',
                'id': f"EDB-{edb_id}",
                'edb_id': edb_id,
                'description': description,
                'date': date,
                'author': author,
                'platform': platform,
                'exploit_type': exploit_type,
                'port': port,
                'cve_ids': cve_ids,
                'technologies': technologies
            }
            
            self.stats["exploits_processed"] += 1
            return node_data
            
        except Exception as e:
            self.logger.error(f"Error processing exploit row: {e}")
            return None
    
    def _process_shellcode_row(self, row: Dict) -> Optional[Dict]:
        """Process a shellcode row from CSV."""
        try:
            edb_id = row.get('id', '').strip()
            description = row.get('description', '').strip()
            date = row.get('date', '').strip()
            author = row.get('author', '').strip()
            platform = row.get('platform', '').strip()
            
            if not edb_id or not description:
                return None
            
            node_data = {
                'type': 'shellcode',
                'id': f"EDB-{edb_id}",
                'edb_id': edb_id,
                'description': description,
                'date': date,
                'author': author,
                'platform': platform
            }
            
            return node_data
            
        except Exception as e:
            self.logger.error(f"Error processing shellcode row: {e}")
            return None
    
    def _process_paper_row(self, row: Dict) -> Optional[Dict]:
        """Process a paper row from CSV."""
        try:
            edb_id = row.get('id', '').strip()
            description = row.get('description', '').strip()
            date = row.get('date', '').strip()
            author = row.get('author', '').strip()
            
            if not edb_id or not description:
                return None
            
            node_data = {
                'type': 'paper',
                'id': f"EDB-{edb_id}",
                'edb_id': edb_id,
                'description': description,
                'date': date,
                'author': author
            }
            
            return node_data
            
        except Exception as e:
            self.logger.error(f"Error processing paper row: {e}")
            return None
    
    def _extract_cve_ids(self, text: str) -> List[str]:
        """Extract CVE IDs from text."""
        cve_pattern = r'CVE-\d{4}-\d{4,7}'
        return list(set(re.findall(cve_pattern, text, re.IGNORECASE)))
    
    def _extract_technologies(self, description: str, platform: str) -> List[str]:
        """Extract technology names from description and platform."""
        technologies = []
        
        # Common technology patterns
        tech_patterns = [
            r'\b(WordPress|Joomla|Drupal|Magento)\b',
            r'\b(Apache|Nginx|IIS|Tomcat)\b',
            r'\b(MySQL|PostgreSQL|Oracle|MSSQL)\b',
            r'\b(PHP|Python|Java|ASP\.NET|Node\.js)\b',
            r'\b(Windows|Linux|Unix|macOS)\b'
        ]
        
        text = f"{description} {platform}".lower()
        
        for pattern in tech_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            technologies.extend(matches)
        
        return list(set(technologies))
    
    async def _create_nodes_batch(self, session, batch: List[Dict]) -> None:
        """Create a batch of nodes in Neo4j."""
        try:
            # Create exploit/shellcode/paper nodes
            for item in batch:
                await self._create_exploit_node(session, item)
                
                # Create CVE relationships
                for cve_id in item.get('cve_ids', []):
                    await self._create_cve_relationship(session, item['id'], cve_id)
                
                # Create technology relationships
                for tech in item.get('technologies', []):
                    await self._create_technology_relationship(session, item['id'], tech)
            
        except Exception as e:
            self.logger.error(f"Error creating nodes batch: {e}")
            self.stats["errors"] += 1
    
    async def _create_exploit_node(self, session, item: Dict) -> None:
        """Create an exploit node."""
        query = """
        MERGE (e:Exploit {id: $id})
        SET e.edb_id = $edb_id,
            e.description = $description,
            e.date = $date,
            e.author = $author,
            e.platform = $platform,
            e.type = $exploit_type,
            e.port = $port,
            e.updated_at = datetime()
        """
        
        await session.run(query, **item)
    
    async def _create_cve_relationship(self, session, exploit_id: str, cve_id: str) -> None:
        """Create relationship between exploit and CVE."""
        query = """
        MERGE (c:CVE {id: $cve_id})
        WITH c
        MATCH (e:Exploit {id: $exploit_id})
        MERGE (e)-[:EXPLOITS]->(c)
        """
        
        await session.run(query, exploit_id=exploit_id, cve_id=cve_id)
        self.stats["relationships_created"] += 1
    
    async def _create_technology_relationship(self, session, exploit_id: str, tech_name: str) -> None:
        """Create relationship between exploit and technology."""
        query = """
        MERGE (t:Technology {name: $tech_name})
        WITH t
        MATCH (e:Exploit {id: $exploit_id})
        MERGE (e)-[:TARGETS]->(t)
        """
        
        await session.run(query, exploit_id=exploit_id, tech_name=tech_name)
        self.stats["relationships_created"] += 1
    
    async def create_indexes(self) -> None:
        """Create additional indexes for performance."""
        print("🔍 Creating search indexes...")
        
        async with self.db_manager.get_neo4j_session() as session:
            # Full-text search indexes
            indexes = [
                "CREATE FULLTEXT INDEX exploit_search IF NOT EXISTS FOR (e:Exploit) ON EACH [e.description]",
                "CREATE FULLTEXT INDEX cve_search IF NOT EXISTS FOR (c:CVE) ON EACH [c.description]"
            ]
            
            for index in indexes:
                try:
                    await session.run(index)
                    print(f"✓ Created index: {index.split()[3]}")
                except Exception as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠ Index creation warning: {e}")
    
    async def print_statistics(self) -> None:
        """Print loading statistics."""
        print("\n📈 Loading Statistics:")
        print(f"  Exploits processed: {self.stats['exploits_processed']}")
        print(f"  CVEs processed: {self.stats['cves_processed']}")
        print(f"  Technologies processed: {self.stats['technologies_processed']}")
        print(f"  Relationships created: {self.stats['relationships_created']}")
        print(f"  Errors encountered: {self.stats['errors']}")


async def main():
    """Main loading function."""
    print("🚀 Starting Exploit-DB data loading...")
    
    # Setup logging
    setup_logging(level="INFO", environment="development")
    
    loader = ExploitDBLoader()
    
    try:
        # Initialize database connection
        await loader.db_manager.init_neo4j()
        
        # Download/update Exploit-DB
        if not await loader.download_exploitdb():
            print("💥 Failed to download Exploit-DB")
            sys.exit(1)
        
        # Load exploit data
        await loader.load_exploits()
        
        # Create search indexes
        await loader.create_indexes()
        
        # Print statistics
        await loader.print_statistics()
        
        print("🎉 Exploit-DB loading completed successfully!")
        
    except Exception as e:
        print(f"💥 Loading failed: {e}")
        sys.exit(1)
    finally:
        await loader.db_manager.close_neo4j()


if __name__ == "__main__":
    asyncio.run(main())
