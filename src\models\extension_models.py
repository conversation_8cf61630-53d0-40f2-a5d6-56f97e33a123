#!/usr/bin/env python3
"""
Data Models for Chrome Extension Integration

Defines Pydantic models for data exchange between the Chrome extension
and the Cipher-Spy backend API.
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum


class RequestMethod(str, Enum):
    """HTTP request methods."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class RequestType(str, Enum):
    """Types of web requests."""
    MAIN_FRAME = "main_frame"
    SUB_FRAME = "sub_frame"
    STYLESHEET = "stylesheet"
    SCRIPT = "script"
    IMAGE = "image"
    FONT = "font"
    OBJECT = "object"
    XMLHTTPREQUEST = "xmlhttprequest"
    PING = "ping"
    CSP_REPORT = "csp_report"
    MEDIA = "media"
    WEBSOCKET = "websocket"
    OTHER = "other"


class ResponseHeaders(BaseModel):
    """HTTP response headers."""
    name: str
    value: str


class RequestBody(BaseModel):
    """HTTP request body data."""
    error: Optional[str] = None
    formData: Optional[Dict[str, List[str]]] = None
    raw: Optional[List[Dict[str, Any]]] = None


class ResponseInfo(BaseModel):
    """HTTP response information."""
    statusCode: int
    statusLine: Optional[str] = None
    responseHeaders: Optional[List[ResponseHeaders]] = None
    timestamp: int


class ErrorInfo(BaseModel):
    """Request error information."""
    error: str
    timestamp: int


class ExtensionRequest(BaseModel):
    """Network request captured by the Chrome extension."""
    id: str = Field(..., description="Unique request identifier")
    sessionId: str = Field(..., description="Extension session identifier")
    timestamp: int = Field(..., description="Request timestamp (milliseconds)")
    url: str = Field(..., description="Request URL")
    method: RequestMethod = Field(..., description="HTTP method")
    type: RequestType = Field(..., description="Request type")
    tabId: int = Field(..., description="Chrome tab ID")
    frameId: int = Field(..., description="Frame ID")
    requestBody: Optional[RequestBody] = Field(None, description="Request body data")
    initiator: Optional[str] = Field(None, description="Request initiator")
    response: Optional[ResponseInfo] = Field(None, description="Response information")
    error: Optional[ErrorInfo] = Field(None, description="Error information")
    analysis: Optional[Dict[str, Any]] = Field(None, description="Analysis results")


class CapturedRequest(BaseModel):
    """Simplified captured request for internal processing."""
    request_id: str = Field(..., description="Unique request identifier")
    url: str = Field(..., description="Request URL")
    method: str = Field(..., description="HTTP method")
    headers: Dict[str, Any] = Field(default_factory=dict, description="Request headers")
    body: Optional[str] = Field(None, description="Request body")
    timestamp: datetime = Field(..., description="Request timestamp")
    response_data: Dict[str, Any] = Field(default_factory=dict, description="Response data")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class ExtensionSession(BaseModel):
    """Chrome extension monitoring session."""
    session_id: str = Field(..., description="Unique session identifier")
    start_time: datetime = Field(..., description="Session start time")
    last_activity: datetime = Field(default_factory=datetime.now, description="Last activity time")
    last_analysis: Optional[datetime] = Field(None, description="Last analysis time")
    requests: List[CapturedRequest] = Field(default_factory=list, description="Captured requests")
    analysis_results: Dict[str, Any] = Field(default_factory=dict, description="Analysis results")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Session metadata")


class AnalysisRequest(BaseModel):
    """Request for analyzing a single network request."""
    session_id: str = Field(..., description="Session identifier")
    request: ExtensionRequest = Field(..., description="Request to analyze")


class SessionAnalysisRequest(BaseModel):
    """Request for analyzing an entire session."""
    session_id: str = Field(..., description="Session identifier")
    requests: List[ExtensionRequest] = Field(..., description="All session requests")


class APIEndpointInfo(BaseModel):
    """Information about a discovered API endpoint."""
    url: str = Field(..., description="Endpoint URL")
    method: RequestMethod = Field(..., description="HTTP method")
    request_count: int = Field(..., description="Number of requests to this endpoint")
    response_codes: List[int] = Field(..., description="Observed response codes")
    parameters: List[str] = Field(default_factory=list, description="Discovered parameters")
    headers: Dict[str, str] = Field(default_factory=dict, description="Common headers")
    content_type: Optional[str] = Field(None, description="Response content type")
    is_api: bool = Field(..., description="Whether this appears to be an API endpoint")


class DomainAnalysis(BaseModel):
    """Analysis results for a specific domain."""
    domain: str = Field(..., description="Domain name")
    request_count: int = Field(..., description="Total requests to domain")
    api_endpoints: List[APIEndpointInfo] = Field(..., description="Discovered API endpoints")
    technologies: List[str] = Field(default_factory=list, description="Detected technologies")
    patterns: List[str] = Field(default_factory=list, description="Observed patterns")
    security_headers: Dict[str, str] = Field(default_factory=dict, description="Security headers")


class SessionAnalysisResult(BaseModel):
    """Comprehensive session analysis results."""
    session_id: str = Field(..., description="Session identifier")
    analysis_time: datetime = Field(..., description="Analysis timestamp")
    total_requests: int = Field(..., description="Total number of requests")
    api_requests: int = Field(..., description="Number of API requests")
    unique_domains: int = Field(..., description="Number of unique domains")
    domains: List[DomainAnalysis] = Field(..., description="Per-domain analysis")
    technologies: Dict[str, int] = Field(..., description="Technology usage counts")
    security_analysis: Dict[str, Any] = Field(..., description="Security analysis results")
    performance_metrics: Dict[str, Any] = Field(..., description="Performance metrics")
    recommendations: List[str] = Field(..., description="Analysis recommendations")


class TrafficPattern(BaseModel):
    """Detected traffic pattern."""
    pattern_type: str = Field(..., description="Type of pattern")
    description: str = Field(..., description="Pattern description")
    frequency: int = Field(..., description="Pattern frequency")
    examples: List[str] = Field(..., description="Example URLs")
    confidence: float = Field(..., description="Confidence score (0-1)")


class SecurityFinding(BaseModel):
    """Security-related finding."""
    severity: str = Field(..., description="Severity level")
    category: str = Field(..., description="Security category")
    description: str = Field(..., description="Finding description")
    affected_urls: List[str] = Field(..., description="Affected URLs")
    recommendation: str = Field(..., description="Remediation recommendation")


class PerformanceMetric(BaseModel):
    """Performance metric."""
    metric_name: str = Field(..., description="Metric name")
    value: Union[int, float] = Field(..., description="Metric value")
    unit: str = Field(..., description="Metric unit")
    description: str = Field(..., description="Metric description")


class ExtensionSettings(BaseModel):
    """Chrome extension settings."""
    monitoring_enabled: bool = Field(True, description="Whether monitoring is enabled")
    capture_headers: bool = Field(True, description="Capture request/response headers")
    capture_payloads: bool = Field(True, description="Capture request payloads")
    filter_patterns: List[str] = Field(default_factory=list, description="URL patterns to capture")
    exclude_patterns: List[str] = Field(default_factory=list, description="URL patterns to exclude")
    max_requests_stored: int = Field(1000, description="Maximum requests to store")
    backend_url: str = Field("http://localhost:8000", description="Cipher-Spy backend URL")
    auto_analysis: bool = Field(True, description="Enable automatic analysis")
    rate_limit_delay: float = Field(1.0, description="Delay between requests (seconds)")


class DiscoveryTask(BaseModel):
    """Background API discovery task."""
    task_id: str = Field(..., description="Unique task identifier")
    session_id: str = Field(..., description="Associated session ID")
    domain: str = Field(..., description="Target domain")
    status: str = Field(..., description="Task status")
    start_time: datetime = Field(..., description="Task start time")
    end_time: Optional[datetime] = Field(None, description="Task completion time")
    result: Optional[Dict[str, Any]] = Field(None, description="Discovery results")
    error: Optional[str] = Field(None, description="Error message if failed")


class ExportFormat(str, Enum):
    """Export format options."""
    JSON = "json"
    CSV = "csv"
    HAR = "har"
    OPENAPI = "openapi"


class ExportRequest(BaseModel):
    """Request for exporting session data."""
    session_id: str = Field(..., description="Session to export")
    format: ExportFormat = Field(..., description="Export format")
    include_analysis: bool = Field(True, description="Include analysis results")
    include_raw_data: bool = Field(True, description="Include raw request data")
    filter_api_only: bool = Field(False, description="Export only API requests")


class ExtensionStatus(BaseModel):
    """Extension service status."""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Extension API version")
    active_sessions: int = Field(..., description="Number of active sessions")
    total_requests_processed: int = Field(..., description="Total requests processed")
    uptime: float = Field(..., description="Service uptime in seconds")
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")


class WebSocketMessage(BaseModel):
    """WebSocket message for real-time updates."""
    type: str = Field(..., description="Message type")
    session_id: str = Field(..., description="Session identifier")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: datetime = Field(..., description="Message timestamp")


class APIDiscoveryResult(BaseModel):
    """Result of API discovery process."""
    domain: str = Field(..., description="Target domain")
    discovery_time: datetime = Field(..., description="Discovery timestamp")
    endpoints_discovered: int = Field(..., description="Number of endpoints found")
    parameters_discovered: int = Field(..., description="Number of parameters found")
    confidence_score: float = Field(..., description="Overall confidence score")
    documentation_generated: bool = Field(..., description="Whether docs were generated")
    openapi_spec_available: bool = Field(..., description="Whether OpenAPI spec is available")
    business_intelligence: Dict[str, Any] = Field(..., description="Business intelligence data")


class RealTimeUpdate(BaseModel):
    """Real-time update message."""
    update_type: str = Field(..., description="Type of update")
    session_id: str = Field(..., description="Session identifier")
    data: Dict[str, Any] = Field(..., description="Update data")
    timestamp: datetime = Field(default_factory=datetime.now, description="Update timestamp")


# Request/Response models for API endpoints
class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    service: str
    timestamp: str
    active_sessions: int
    version: str


class SessionListResponse(BaseModel):
    """Session list response."""
    active_sessions: int
    sessions: List[Dict[str, Any]]


class SessionResponse(BaseModel):
    """Single session response."""
    session_id: str
    start_time: str
    last_analysis: Optional[str]
    request_count: int
    analysis_count: int
    session_duration: float
    has_analysis: bool


class AnalysisResponse(BaseModel):
    """Analysis response."""
    success: bool
    request_id: Optional[str] = None
    session_id: Optional[str] = None
    analysis: Dict[str, Any]
    session_stats: Optional[Dict[str, Any]] = None


class DiscoveryTriggerResponse(BaseModel):
    """Discovery trigger response."""
    success: bool
    session_id: str
    domains: List[str]
    discovery_tasks: List[str]
    message: str
