#!/usr/bin/env python3
"""
Debug API Discovery

Simple debug script to test API discovery functionality step by step.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor
from src.utils.logging import setup_logging


async def debug_api_discovery():
    """Debug API discovery step by step."""
    print("🐛 Debug API Discovery")
    print("="*50)
    
    # Setup logging
    setup_logging(level="INFO", environment="development")
    
    # Initialize components
    print("1. Initializing network interceptor...")
    interceptor = NetworkInterceptor()
    
    print("2. Initializing crawler...")
    crawler = PlaywrightCrawler(headless=False, delay_ms=1000)  # Show browser for debugging
    
    try:
        print("3. Starting crawler...")
        await crawler.start()
        page = crawler.page
        
        print("4. Setting up network interception...")
        await interceptor.setup_page(page)
        
        print("5. Navigating to pump.fun...")
        await page.goto("https://pump.fun", wait_until="networkidle")
        
        print("6. Waiting for initial requests...")
        await asyncio.sleep(3)
        
        # Check statistics
        stats = interceptor.get_statistics()
        print(f"\n📊 Initial Statistics:")
        print(f"   Total Requests: {stats['total_requests']}")
        print(f"   Total Responses: {stats['total_responses']}")
        print(f"   API Endpoints: {stats['api_endpoints_discovered']}")
        
        # Get endpoints
        endpoints = interceptor.get_discovered_endpoints()
        print(f"\n🔗 Discovered Endpoints: {len(endpoints)}")
        
        if endpoints:
            print("   First 5 endpoints:")
            for i, endpoint in enumerate(endpoints[:5]):
                method = endpoint.get('method', 'GET')
                url = endpoint.get('url', 'Unknown')
                print(f"   {i+1}. {method} {url}")
        
        # Navigate to board page to trigger more API calls
        print("\n7. Navigating to board page...")
        await page.goto("https://pump.fun/board", wait_until="networkidle")
        await asyncio.sleep(3)
        
        # Check again
        stats2 = interceptor.get_statistics()
        endpoints2 = interceptor.get_discovered_endpoints()
        
        print(f"\n📊 After Board Navigation:")
        print(f"   Total Requests: {stats2['total_requests']}")
        print(f"   Total Responses: {stats2['total_responses']}")
        print(f"   API Endpoints: {len(endpoints2)}")
        
        if len(endpoints2) > len(endpoints):
            print(f"   New endpoints discovered: {len(endpoints2) - len(endpoints)}")
            new_endpoints = endpoints2[len(endpoints):]
            for i, endpoint in enumerate(new_endpoints[:3]):
                method = endpoint.get('method', 'GET')
                url = endpoint.get('url', 'Unknown')
                print(f"   NEW {i+1}. {method} {url}")
        
        # Save debug results
        debug_data = {
            'timestamp': datetime.now().isoformat(),
            'initial_stats': stats,
            'final_stats': stats2,
            'total_endpoints': len(endpoints2),
            'endpoints': endpoints2
        }
        
        with open('debug_api_discovery.json', 'w') as f:
            json.dump(debug_data, f, indent=2, default=str)
        
        print(f"\n💾 Debug data saved to debug_api_discovery.json")
        
        return len(endpoints2) > 0
        
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print("\n8. Cleaning up...")
        try:
            await crawler.stop()
        except:
            pass


async def main():
    """Main entry point."""
    print("🚀 Starting API Discovery Debug")
    
    success = await debug_api_discovery()
    
    if success:
        print("\n✅ Debug completed successfully!")
        print("Check debug_api_discovery.json for detailed results.")
        return 0
    else:
        print("\n❌ Debug failed!")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
