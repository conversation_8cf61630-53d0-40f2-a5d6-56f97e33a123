"""
Network traffic interceptor for Cipher-Spy.

Captures and analyzes all HTTP requests and responses during web crawling,
providing comprehensive API discovery and reverse engineering capabilities.
"""

import json
import re
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from dataclasses import dataclass, asdict

from playwright.async_api import Page, Route, Request, Response

from ..utils.logging import get_logger


@dataclass
class NetworkRequest:
    """Represents a captured network request."""
    url: str
    method: str
    headers: Dict[str, str]
    post_data: Optional[str]
    timestamp: datetime
    request_id: str


@dataclass
class NetworkResponse:
    """Represents a captured network response."""
    url: str
    status: int
    headers: Dict[str, str]
    body: Optional[str]
    timestamp: datetime
    request_id: str


@dataclass
class APIEndpoint:
    """Represents a discovered API endpoint."""
    url: str
    method: str
    parameters: Dict[str, Any]
    headers: Dict[str, str]
    request_body_schema: Optional[Dict[str, Any]]
    response_schema: Optional[Dict[str, Any]]
    status_codes: Set[int]
    content_types: Set[str]
    authentication_required: bool
    discovered_from: str
    first_seen: datetime
    last_seen: datetime
    request_count: int


class NetworkInterceptor:
    """
    Comprehensive network traffic interceptor and analyzer.

    Features:
    - Complete HTTP request/response capture
    - API endpoint discovery and cataloging
    - Authentication flow detection
    - Response schema inference
    - WebSocket connection monitoring
    """

    def __init__(self):
        self.logger = get_logger(__name__)

        # Storage for captured traffic
        self.requests: Dict[str, NetworkRequest] = {}
        self.responses: Dict[str, NetworkResponse] = {}
        self.endpoints: Dict[str, APIEndpoint] = {}

        # Analysis state
        self.request_counter = 0
        self.new_endpoints: List[Dict[str, Any]] = []

        # Patterns for API detection
        self.api_patterns = [
            r'/api/',
            r'/v\d+/',
            r'\.json$',
            r'/graphql',
            r'/rest/',
            r'/endpoint/',
            r'/service/',
            r'/_next/data/',
            r'/__data\.json'
        ]

        # Content types that indicate API responses
        self.api_content_types = {
            'application/json',
            'application/xml',
            'text/xml',
            'application/graphql',
            'application/x-www-form-urlencoded'
        }

        self.logger.info("Network interceptor initialized")

    async def setup_page(self, page: Page) -> None:
        """Setup comprehensive network interception for a Playwright page."""
        try:
            # Store page reference for advanced operations
            self.page = page

            # Enable comprehensive request interception
            await page.route("**/*", self._handle_route)

            # Listen for response events
            page.on("response", self._handle_response)

            # Listen for request events
            page.on("request", self._handle_request)

            # Listen for WebSocket events
            page.on("websocket", self._handle_websocket)

            # Listen for request failed events
            page.on("requestfailed", self._handle_request_failed)

            # Listen for request finished events
            page.on("requestfinished", self._handle_request_finished)

            self.logger.info("Comprehensive network interception setup completed")

        except Exception as e:
            self.logger.error(f"Error setting up network interception: {e}")
            raise

    async def _handle_route(self, route: Route) -> None:
        """Handle intercepted routes (requests) - capture and continue."""
        try:
            request = route.request

            # Log the request immediately
            self.logger.info(f"🌐 REQUEST: {request.method} {request.url}")

            # Check if this is an API endpoint we should track
            if self._is_api_endpoint(request.url, request.headers):
                self.logger.info(f"🎯 API ENDPOINT DETECTED: {request.method} {request.url}")

            # Continue the request (don't block it)
            await route.continue_()

        except Exception as e:
            self.logger.error(f"Error handling route: {e}")
            # Continue anyway to avoid breaking the page
            try:
                await route.continue_()
            except:
                pass

    async def _handle_request(self, request: Request) -> None:
        """Handle request events."""
        try:
            self.request_counter += 1
            request_id = f"req_{self.request_counter}_{datetime.now().timestamp()}"

            # Extract request data
            headers = {}
            for name, value in request.headers.items():
                headers[name] = value

            # Get POST data if available
            post_data = None
            try:
                if request.method in ['POST', 'PUT', 'PATCH']:
                    post_data = request.post_data
            except:
                pass

            # Store request
            network_request = NetworkRequest(
                url=request.url,
                method=request.method,
                headers=headers,
                post_data=post_data,
                timestamp=datetime.utcnow(),
                request_id=request_id
            )

            self.requests[request_id] = network_request

            # Check if this looks like an API endpoint
            if self._is_api_endpoint(request.url, headers):
                await self._analyze_api_request(network_request)

        except Exception as e:
            self.logger.error(f"Error handling request: {e}")

    async def _handle_response(self, response: Response) -> None:
        """Handle response events."""
        try:
            # Find matching request
            request_id = None
            for rid, req in self.requests.items():
                if req.url == response.url and rid not in self.responses:
                    request_id = rid
                    break

            if not request_id:
                # Create a synthetic request ID
                request_id = f"resp_{self.request_counter}_{datetime.now().timestamp()}"

            # Extract response data
            headers = {}
            for name, value in response.headers.items():
                headers[name] = value

            # Get response body for API endpoints
            body = None
            try:
                content_type = headers.get('content-type', '').lower()
                if any(ct in content_type for ct in self.api_content_types):
                    # Only capture small responses to avoid memory issues
                    if int(headers.get('content-length', '0')) < 1024 * 1024:  # 1MB limit
                        body = await response.text()
            except:
                pass

            # Store response
            network_response = NetworkResponse(
                url=response.url,
                status=response.status,
                headers=headers,
                body=body,
                timestamp=datetime.utcnow(),
                request_id=request_id
            )

            self.responses[request_id] = network_response

            # Analyze API response
            if self._is_api_endpoint(response.url, headers):
                await self._analyze_api_response(network_response, request_id)

        except Exception as e:
            self.logger.error(f"Error handling response: {e}")

    async def _handle_websocket(self, websocket) -> None:
        """Handle WebSocket connections."""
        try:
            self.logger.info(f"WebSocket connection detected: {websocket.url}")

            # Log WebSocket frames
            websocket.on("framereceived", lambda frame: self._log_websocket_frame("received", frame))
            websocket.on("framesent", lambda frame: self._log_websocket_frame("sent", frame))

        except Exception as e:
            self.logger.error(f"Error handling WebSocket: {e}")

    def _log_websocket_frame(self, direction: str, frame) -> None:
        """Log WebSocket frame data."""
        try:
            # Handle different frame types
            if hasattr(frame, 'payload'):
                payload = frame.payload[:200] if frame.payload else ""
            elif isinstance(frame, str):
                payload = frame[:200]
            else:
                payload = str(frame)[:200]

            self.logger.debug(f"WebSocket frame {direction}: {payload}...")
        except Exception as e:
            self.logger.debug(f"Could not log WebSocket frame: {e}")

    async def _handle_request_failed(self, request) -> None:
        """Handle failed requests."""
        try:
            self.logger.warning(f"Request failed: {request.method} {request.url}")
            # Could track failed requests for analysis
        except Exception as e:
            self.logger.error(f"Error handling failed request: {e}")

    async def _handle_request_finished(self, request) -> None:
        """Handle finished requests."""
        try:
            # This gives us timing information
            self.logger.debug(f"Request finished: {request.method} {request.url}")
        except Exception as e:
            self.logger.error(f"Error handling finished request: {e}")

    def _is_api_endpoint(self, url: str, headers: Dict[str, str]) -> bool:
        """Determine if a URL represents an API endpoint."""
        try:
            url_lower = url.lower()

            # Check URL patterns - be more aggressive in detection
            api_indicators = [
                '/api/', '/v1/', '/v2/', '/v3/', '/graphql', '/rest/',
                'api.', '.json', '/json', '/data/', '/fetch',
                '/ajax', '/xhr', '/rpc', '/ws/', '/websocket'
            ]

            for indicator in api_indicators:
                if indicator in url_lower:
                    return True

            # Check for common API patterns in URL
            for pattern in self.api_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return True

            # Check content type
            content_type = headers.get('content-type', '').lower()
            if any(ct in content_type for ct in self.api_content_types):
                return True

            # Check for AJAX headers
            if headers.get('x-requested-with') == 'XMLHttpRequest':
                return True

            # Check for API-like headers
            api_headers = ['x-api-key', 'authorization', 'x-auth-token', 'accept']
            if any(header.lower() in headers for header in api_headers):
                accept_header = headers.get('accept', '').lower()
                if 'application/json' in accept_header or 'application/xml' in accept_header:
                    return True

            # Check if URL looks like it returns data (not static assets)
            static_extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.ttf']
            if not any(url_lower.endswith(ext) for ext in static_extensions):
                # If it's not a static asset and has query parameters, it might be an API
                if '?' in url and ('=' in url or 'query' in url_lower):
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking API endpoint: {e}")
            return False

    async def _analyze_api_request(self, request: NetworkRequest) -> None:
        """Analyze an API request and extract endpoint information."""
        try:
            parsed_url = urlparse(request.url)
            endpoint_key = f"{request.method}:{parsed_url.path}"

            # Extract parameters
            parameters = {}

            # Query parameters
            if parsed_url.query:
                parameters.update(parse_qs(parsed_url.query))

            # POST data parameters
            if request.post_data:
                try:
                    if request.headers.get('content-type', '').startswith('application/json'):
                        json_data = json.loads(request.post_data)
                        parameters['body'] = json_data
                    elif request.headers.get('content-type', '').startswith('application/x-www-form-urlencoded'):
                        form_data = parse_qs(request.post_data)
                        parameters.update(form_data)
                except:
                    parameters['raw_body'] = request.post_data

            # Check for authentication
            auth_required = any(
                header in request.headers
                for header in ['authorization', 'x-api-key', 'x-auth-token']
            )

            # Create or update endpoint
            if endpoint_key not in self.endpoints:
                self.endpoints[endpoint_key] = APIEndpoint(
                    url=request.url,  # Store full URL instead of just path
                    method=request.method,
                    parameters=parameters,
                    headers=dict(request.headers),
                    request_body_schema=None,
                    response_schema=None,
                    status_codes=set(),
                    content_types=set(),
                    authentication_required=auth_required,
                    discovered_from=request.url,
                    first_seen=request.timestamp,
                    last_seen=request.timestamp,
                    request_count=1
                )
                self.logger.info(f"📝 Stored new endpoint: {request.method} {request.url}")
            else:
                endpoint = self.endpoints[endpoint_key]
                endpoint.last_seen = request.timestamp
                endpoint.request_count += 1
                endpoint.parameters.update(parameters)

        except Exception as e:
            self.logger.error(f"Error analyzing API request: {e}")

    async def _analyze_api_response(self, response: NetworkResponse, request_id: str) -> None:
        """Analyze an API response and update endpoint information."""
        try:
            # Find corresponding request
            request = self.requests.get(request_id)
            if not request:
                return

            parsed_url = urlparse(response.url)
            endpoint_key = f"{request.method}:{parsed_url.path}"

            if endpoint_key in self.endpoints:
                endpoint = self.endpoints[endpoint_key]

                # Update status codes
                endpoint.status_codes.add(response.status)

                # Update content types
                content_type = response.headers.get('content-type', '')
                if content_type:
                    endpoint.content_types.add(content_type.split(';')[0])

                # Analyze response schema
                if response.body and content_type.startswith('application/json'):
                    try:
                        json_data = json.loads(response.body)
                        schema = self._infer_json_schema(json_data)
                        endpoint.response_schema = schema
                    except:
                        pass

                # Add to new endpoints list for processing
                endpoint_data = {
                    "url": endpoint.url,
                    "method": endpoint.method,
                    "parameters": endpoint.parameters,
                    "headers": endpoint.headers,
                    "response_status": response.status,
                    "response_content_type": content_type,
                    "response_sample": response.body[:500] if response.body else None,
                    "discovered_from": endpoint.discovered_from,
                    "status_codes": list(endpoint.status_codes),
                    "content_types": list(endpoint.content_types)
                }

                self.new_endpoints.append(endpoint_data)

        except Exception as e:
            self.logger.error(f"Error analyzing API response: {e}")

    def _infer_json_schema(self, data: Any) -> Dict[str, Any]:
        """Infer JSON schema from response data."""
        try:
            if isinstance(data, dict):
                schema = {"type": "object", "properties": {}}
                for key, value in data.items():
                    schema["properties"][key] = self._infer_json_schema(value)
                return schema
            elif isinstance(data, list):
                if data:
                    return {"type": "array", "items": self._infer_json_schema(data[0])}
                else:
                    return {"type": "array", "items": {}}
            elif isinstance(data, str):
                return {"type": "string"}
            elif isinstance(data, int):
                return {"type": "integer"}
            elif isinstance(data, float):
                return {"type": "number"}
            elif isinstance(data, bool):
                return {"type": "boolean"}
            else:
                return {"type": "unknown"}

        except Exception as e:
            self.logger.error(f"Error inferring JSON schema: {e}")
            return {"type": "unknown"}

    def get_discovered_endpoints(self) -> List[Dict[str, Any]]:
        """Get all discovered API endpoints."""
        try:
            endpoints = []
            for endpoint in self.endpoints.values():
                endpoint_dict = {
                    'url': endpoint.url,
                    'method': endpoint.method,
                    'parameters': endpoint.parameters,
                    'headers': endpoint.headers,
                    'request_body_schema': endpoint.request_body_schema,
                    'response_schema': endpoint.response_schema,
                    'status_codes': list(endpoint.status_codes),
                    'content_types': list(endpoint.content_types),
                    'authentication_required': endpoint.authentication_required,
                    'discovered_from': endpoint.discovered_from,
                    'first_seen': endpoint.first_seen,
                    'last_seen': endpoint.last_seen,
                    'request_count': endpoint.request_count
                }
                endpoints.append(endpoint_dict)

            self.logger.info(f"Returning {len(endpoints)} discovered endpoints")
            return endpoints
        except Exception as e:
            self.logger.error(f"Error getting discovered endpoints: {e}")
            return []

    def get_new_endpoints(self) -> List[Dict[str, Any]]:
        """Get new endpoints since last call and clear the list."""
        new = self.new_endpoints.copy()
        self.new_endpoints.clear()
        return new

    def get_statistics(self) -> Dict[str, Any]:
        """Get network interception statistics."""
        return {
            "total_requests": len(self.requests),
            "total_responses": len(self.responses),
            "api_endpoints_discovered": len(self.endpoints),
            "unique_domains": len(set(urlparse(req.url).netloc for req in self.requests.values())),
            "methods_used": list(set(req.method for req in self.requests.values())),
            "status_codes_seen": list(set(resp.status for resp in self.responses.values()))
        }
