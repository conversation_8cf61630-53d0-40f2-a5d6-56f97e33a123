Cipher-Spy: AI-Driven Red Team Swarm – PRD and Development Roadmap
High-Level Vision
Cipher-Spy is a locally deployed, AI-driven red team swarm designed for professional penetration testers. It empowers a human red teamer with an autonomous suite of AI agents that collaboratively perform web reconnaissance, vulnerability analysis, and exploit planning in a controlled environment. The vision is to replicate a full red-team workflow – from initial recon to exploit execution – using orchestrated LLM-driven agents, while keeping the human “pilot” in ultimate control. Cipher-Spy aims to dramatically speed up penetration testing by automating tedious discovery tasks and suggesting exploit strategies, all within a local, containerized system for security and privacy. Key aspects of this vision include:
Multi-Agent Orchestration: Utilizing LangGraph to coordinate specialized AI agents as nodes in a directed graph workflow
blog.langchain.dev
. Each agent focuses on a specific phase (crawling, analysis, exploitation) similar to how human red-team specialists collaborate. This graph-based orchestration ensures complex tasks are divided and executed in logical sequence, reducing information loss and increasing efficiency
arxiv.org
arxiv.org
.
Autonomous Reconnaissance: The system will autonomously crawl target web applications using Playwright, interacting with pages, forms, and links to map the target’s surface. All HTTP traffic (requests and responses) will be intercepted and recorded for analysis
medium.com
. Cipher-Spy’s vision is to act as a “swarm” of bots scouring the application, cataloging every endpoint, parameter, and response observed, much like a team of diligent testers working in parallel.
Integrated Knowledge and Reasoning: Cipher-Spy will embed a knowledge graph of known exploits and vulnerabilities (from Exploit-DB and other open sources) and use Retrieval-Augmented Generation (RAG) to reason about the target’s weaknesses. By integrating Graphiti for real-time knowledge graphs, the system can continuously enrich its understanding of the target with external exploit data
github.com
github.com
. This enables the AI to suggest relevant exploits based on discovered technologies or API patterns, effectively bringing an encyclopedic hacker’s knowledge into the tool.
Human-in-the-Loop Control: While largely autonomous in investigation, Cipher-Spy’s core principle is “human-in-the-loop” for any destructive action. The AI agents can draft detailed exploit plans (complete with payloads and step-by-step execution flows), but execution will pause for human approval. This ensures safety and oversight – the human operator reviews each proposed exploit before it’s run, preventing unintended harm. The vision balances AI speed with expert judgment, so the human red teamer remains the ultimate decision-maker.
Open-Source and Local-First: All components of Cipher-Spy are built on open-source tools and run locally (in Docker containers) for privacy. The only cloud service optionally used is OpenRouter for accessing advanced LLMs, meaning no sensitive target data needs to leave the local environment. This aligns with professional red-team needs for confidentiality and control over tools.
By realizing this vision, Cipher-Spy will function as a tireless AI co-pilot that finds more vulnerabilities faster, augments the tester’s expertise with global exploit knowledge, and accelerates the pentesting process – all without replacing the expert’s critical decision-making role.
Core Features and User Stories
Core Features Overview
Multi-Agent Orchestration with LangGraph: The system organizes agents in a graph workflow (via LangGraph) where each node is a specialized agent and edges define their communication and control flow
blog.langchain.dev
. This enables complex, branching workflows (loops, conditional steps) and concurrent agent actions. The design mimics a collaborative team: e.g., one agent crawls the site, another analyzes collected data, another formulates exploits, all coordinated by a central logic. This multi-agent design has been shown to improve efficiency by letting agents focus on specialized tasks
arxiv.org
.
Autonomous Web Crawling (Playwright): Cipher-Spy will automatically navigate through the target web application using Playwright, much like a headless browser. It will click links, fill forms (with safe test inputs), handle authentication if credentials are provided, and trigger single-page app navigations. The crawler agent systematically maps out pages and functions, generating a site map. During this process, network interception is enabled: every API call (XHR/fetch) is intercepted and logged with its URL, method, parameters, and response
medium.com
. This yields a complete catalog of REST/GraphQL endpoints and their sample responses for later analysis. The agent will also record form endpoints and UI behaviors, providing a thorough coverage of the target’s attack surface.
API Endpoint Interception & Cataloging: As the Playwright crawler runs, Cipher-Spy intercepts all HTTP requests and responses using Playwright’s routing hooks
medium.com
. It logs each unique API endpoint discovered (URL path, query parameters, POST bodies) along with response data (status codes, content type, maybe response samples). This forms an API catalog of the application. The system will attempt basic reverse engineering of these APIs – e.g., identifying common patterns or parameters, noting if authentication or specific tokens are required, and grouping endpoints by functionality. The result is akin to an autogenerated API documentation for the target, which is invaluable for understanding potential attack vectors (like hidden API endpoints that aren’t obvious from the UI).
Technology Stack Fingerprinting: Cipher-Spy will analyze the site to identify technologies in use – including server software, programming frameworks, front-end libraries, and infrastructure (CDN/WAF). It leverages open-source fingerprinting tools: for example, Wappalyzer (via its Python library) to detect CMS, frameworks, JS libraries, etc., by examining HTML and script patterns
pypi.org
. It also uses WafW00f for WAF detection, which sends specialized requests to deduce the presence and type of Web Application Firewall based on response behavior
null-byte.wonderhowto.com
. Likewise, CDN or hosting clues (like “Server: cloudflare” headers or known DNS patterns) are detected. All identified tech stack info is stored (e.g., “Target site is built with WordPress 5.8, uses jQuery 3.5, has Cloudflare WAF, running on Apache server”). This fingerprinting is critical for narrowing down relevant exploits and attack techniques.
Graph-RAG Exploit Knowledge Integration: Cipher-Spy includes a Graph-RAG agent that uses Graphiti (knowledge graph framework) combined with a vector database for Exploit-DB data. All Exploit-DB entries (exploits, shellcodes, vulnerability papers) are ingested into a local knowledge base. Using Graphiti, this data is stored as a temporal knowledge graph linking exploits to vulnerabilities and affected technologies, enabling complex queries
github.com
github.com
. For example, nodes might include software products and versions, CVE identifiers, and exploit scripts, with relationships like “Joomla 3.4 HAS_VULNERABILITY CVE-2015-7297” and “Exploit script EDB-12345 EXPLOITS CVE-2015-7297”. Simultaneously, exploit descriptions are vector-embedded for semantic search. This allows the agent to reason over the exploit data: given the target’s fingerprint (say “WordPress 5.8”), the agent can query the graph+vectors to find relevant known vulnerabilities or exploits for that tech, even if the exact version isn’t an exact match (semantic similarity helps find related exploits)
medium.com
medium.com
. This Graph-RAG approach means the system supplements the LLM’s knowledge with a constantly updated, queryable exploit database. (This addresses a key challenge in autonomous pentesting: providing the AI with up-to-date vulnerability knowledge. In research, integrating RAG significantly improved an LLM-based pentest agent’s success in end-to-end exploits
arxiv.org
arxiv.org
.)
Exploit Strategy Generation (with Human Approval): One of Cipher-Spy’s standout features is an agent that generates detailed exploit plans for discovered vulnerabilities – but does not execute them until approved. When the system finds a likely vulnerability (e.g., an outdated library with a known CVE, or a sensitive API with no auth), it triggers the Exploit Planner agent. This agent uses an LLM (via OpenRouter or a local model) to draft an exploit plan of attack. Each plan includes: a summary of the vulnerability, the steps to exploit it, one or more proof-of-concept (PoC) payloads or scripts, and the expected impact or outcome if exploited. The plan is essentially a report advising the red teamer on how the exploit would work. Crucially, the agent then pauses and asks for human approval before any actual exploit action is taken. The human user can review the plan (ensuring it’s safe and appropriate) and then approve execution, modify the plan, or reject it. Only upon approval will Cipher-Spy proceed to execute the exploit steps (either in a safe, simulated manner or live, depending on user choice). This guarantees that no potentially harmful action is taken autonomously, aligning with ethical and safety standards for professional use.
Replayable Exploit Workflows: When an exploit plan is approved, Cipher-Spy can carry out the steps using automated tools (like sending crafted HTTP requests via cURL or driving a browser via Playwright for GUI steps). Every exploit attempt is recorded. The system can output a replay script for each exploit, such as a sequence of cURL commands or a Playwright script, so the exact attack can be reproduced later. This is useful for reports or for verifying the exploit on repeated runs. Essentially, any exploit executed by the system is deterministically documented, allowing the red team to re-play the attack on demand (e.g., against a staging environment, or after a fix to verify it’s patched).
Local Data Storage: All data collected and generated is stored locally in a structured format. Cipher-Spy uses a relational database (PostgreSQL or a self-hosted Supabase) to persist information on discovered hosts, pages, endpoints, and identified vulnerabilities. The choice of Postgres/Supabase ensures we have SQL querying capability and easy integration with other tools. For the knowledge graph, a local Neo4j database (as required by Graphiti) stores the exploit knowledge graph and possibly vector indices for embeddings
github.com
. No sensitive data is sent to external cloud storage. The local storage also means a user can pause and resume engagements, query the data for custom analysis, or integrate it with other security tools.
Containerized Deployment: Cipher-Spy is distributed as a Docker container (or a set of containers orchestrated via Docker Compose). This makes it easy to deploy on any system (laptop, server) without complex setup. The container includes the web UI (if any), the backend agents, and necessary dependencies (Playwright browsers, Neo4j, etc.). The recommended install will be via docker compose, which will spin up (for example) a service for the main application, a Postgres DB service, and a Neo4j service for Graphiti. By containerizing, we ensure environment consistency and easy teardown/startup between projects. Users can run Cipher-Spy on isolated networks or VMs as needed for safety.
Use of Open-Source Components: Except for optional LLM access via OpenRouter, every component in Cipher-Spy is open-source. Key libraries include LangChain/LangGraph for agent orchestration, Playwright (browser automation), Wappalyzer and WafW00f for fingerprinting, Graphiti and Neo4j for the knowledge graph, a vector DB like Chroma or Faiss (if not using Neo4j’s vector capabilities) for embeddings, and various Python libraries (FastAPI for a local API, etc.). This not only avoids licensing costs but allows users to inspect and trust the code. It also means the tool can be extended or customized by the community. (OpenRouter will be used only to interface with premium LLMs like GPT-4 if the user provides an API key – OpenRouter acts as a unified API to many model providers
helicone.ai
. The system will default to open-source LLMs if no OpenRouter key is present.)
Sample User Stories
Story 1: Automated Reconnaissance – “As a red team penetration tester, I want the system to automatically map out a target web application (crawl pages, follow links, and list all endpoints) so that I can save time on initial reconnaissance and ensure no area of the app is missed.”
Acceptance Criteria: After pointing Cipher-Spy at a target URL, it should produce a site map listing all discovered pages and API endpoints (including parameters). For each endpoint, methods and example responses should be recorded. The tester can review a comprehensive list of the application’s surface (including hidden API calls not directly linked on pages).
Story 2: Tech Stack Identification – “As a security tester, I want to know what technologies and defenses a target is running (e.g., frameworks, libraries, WAF/CDN) so that I can tailor my attacks to known vulnerabilities in those technologies.”
Acceptance Criteria: After a scan, Cipher-Spy provides a report like “Identified: Django 3.x backend, jQuery 3.5, Bootstrap 4, Cloudflare WAF detected, using AWS CloudFront CDN, etc.” This should be accurate and sourced from the site’s HTML/JS, headers, or responses. For example, if Wafw00f identifies Cloudflare, that should be listed along with confidence. The tester can use this info to focus on relevant exploit modules (e.g., known CVEs for Django 3.x).
Story 3: Vulnerability Knowledge Integration – “As a red teamer, I want the tool to inform me of known vulnerabilities and exploits relevant to the target’s software versions, so that I don’t have to manually research each technology for known CVEs.”
Acceptance Criteria: Suppose the target runs Apache Struts 2.3 (from fingerprinting). The system should highlight if that version has known critical CVEs and perhaps mention famous exploits (e.g., CVE-2017-5638) from Exploit-DB. It might present a short summary: “Apache Struts 2.3 is vulnerable to a known RCE (CVE-2017-5638)
exploit-db.com
; Exploit-DB #41570 available.” This information should be retrieved from the Graph-RAG knowledge base. The tester thus immediately knows to attempt that exploit (with caution).
Story 4: Exploit Plan Generation – “As an experienced pentester, I want the AI to draft a step-by-step exploit plan for any potential vulnerability it finds, so I can quickly assess the viability of the attack and execute it if appropriate.”
Acceptance Criteria: If Cipher-Spy finds an SQL injection point or an outdated plugin, it should use the LLM to generate an exploit plan. For example: “Vulnerability: SQL Injection in /api/search endpoint. Plan: 1) Craft a payload ' OR '1'='1 to dump data… 2) Use SQLMap with the provided URL… 3) Escalate to dump user credentials. Expected Result: Database contents of users table exposed.” The plan should include a POC payload (like a union-select query) and clearly state the expected effect. It must then pause for user confirmation. The tester reviews the plan and either approves or adjusts it. No actual attack is performed until they approve.
Story 5: Controlled Execution and Logging – “As a user, after approving an exploit, I want the system to execute the steps and log the outcome, so I can verify the exploit’s impact and have evidence for reporting.”
Acceptance Criteria: When the tester clicks “Execute” on an approved exploit plan, Cipher-Spy runs the actions (e.g., sends the malicious request, or runs a script). It then logs what happened – e.g., “Response received: 200 OK, extracted 100 user records.” If integrated with a headless browser for a UI exploit, it might take a screenshot or record relevant output. Additionally, the system should provide a way to replay the attack: for instance, showing the exact cURL command used or saving a Postman collection. This ensures the tester can later demonstrate the exploit (e.g., in a debrief or to the development team of the target).
Story 6: Data Persistence and Review – “As a user, I want all findings (pages, endpoints, vulnerabilities, exploits, etc.) saved so I can analyze them later or compare results across scans.”
Acceptance Criteria: After a scan, the user can query the local database (or view via a UI) to see all collected data. For example, they can list all API endpoints and see for each the methods and sample responses, or list all identified libraries and their versions. If they run the tool again on the same target after some time, the data can be updated and changes noted (if we implement differential analysis). The data should remain available locally until the user deletes it, ideally in a well-structured SQL schema that the user can also query manually for custom needs.
These user stories illustrate the primary ways a professional might interact with Cipher-Spy, ensuring the product is aligned with real-world pentesting workflows.
Agent Architecture and LangGraph Node Map
Cipher-Spy’s architecture is built around multiple AI agents orchestrated by LangGraph. Each agent is responsible for a specific aspect of the red teaming process, and LangGraph connects them in a directed graph workflow
blog.langchain.dev
. Below is an outline of the agent roles (nodes) and how they interact (edges), forming the “red team swarm”:
Node 1: Orchestrator / Controller Agent – This is the LangGraph root node (akin to a supervisor) that initiates and coordinates the overall process. It decides which agent to invoke next based on the state of the scan. The Orchestrator ensures the sequence Recon → Analysis → Exploitation runs in the correct order and feeds outputs of one stage as inputs to the next. (In LangGraph terms, it can be seen as the router controlling state transitions between agents.) For example, once recon is done and data is stored, the Orchestrator triggers the analysis agents. It also enforces the rule that exploitation steps pause for approval. This agent might be implemented as a simple state machine or rule-based agent within LangGraph. (Notably, research like VulnBot underscores modeling pentest tasks as a graph to enforce logical ordering and avoid conflicts
arxiv.org
 – our Orchestrator plays a similar role using LangGraph’s stateful control flow.)
Node 2: Reconnaissance Crawler Agent – A specialized agent using Playwright (with possibly an LLM for strategic guidance). Its goal is to explore the target web app thoroughly. The Orchestrator starts this agent with the target URL. The Crawler agent opens pages, clicks buttons, submits forms (with preset safe values), and follows discovered links. It keeps track of visited URLs to avoid repetition and respects the target scope (not leaving the base domain, etc.). As it runs, it emits two types of data:
Discovered Pages/Links – which get stored and possibly passed to the fingerprinting agent.
Network calls (API endpoints) – via integrated network listeners. The Crawler agent attaches Playwright’s page.route or page.on('request', ...) handlers to intercept calls to URLs containing “/api/” or similar
medium.com
. These requests (and corresponding responses) are captured in real-time. The agent can log the details (URL, method, headers, body, response code, length) to the database. It may also do a quick analysis, e.g., “Observed /api/search?q=test returning JSON, likely a search endpoint”. Once the crawling depth/time limits are reached, this agent concludes. The Orchestrator then knows the recon phase is complete.
(Edges: Orchestrator -> Crawler to start crawling. Crawler -> Data Storage to save findings. Crawler -> Fingerprint agent (next) provides list of pages and captured responses for analysis.)
Node 3: Fingerprinting & Analysis Agent – After crawling, the Orchestrator invokes this agent to analyze the collected data. It has two sub-tasks:
Tech Fingerprinting: It uses the page content (HTML, scripts) and headers to identify technologies. This involves calling Wappalyzer’s library on the main pages and any significant endpoints. The agent might scan the home page and login page with Wappalyzer, and also inspect captured JavaScript files for library banners or version comments. It also uses heuristic rules and tools like Wafw00f (pointed at the base URL) for WAF detection
null-byte.wonderhowto.com
. The output is a list of identified tech components: e.g., frameworks, server, OS (if detectable via headers), third-party JS libraries, and security tools in place.
API Analysis: The agent goes through the list of API endpoints captured by the crawler. It might group them (for example, cluster by URL pattern), attempt to reverse-engineer basic specs (like, “these endpoints /api/user/* seem related to user profiles”, or “this endpoint returns error messages containing SQL syntax, potential SQLi”). The LLM can help here by summarizing what each API might do based on names and response data. It flags any “interesting” endpoints, such as those that returned 500 errors or large data blobs, for the Exploit agent to consider. It also notes any potential vulnerability clues – e.g., an endpoint returning an overly verbose error (possible debug mode), or presence of known vulnerable libraries (like a /wp-content/plugins/ path indicating a WordPress plugin that might have vulnerabilities).
All findings from this agent are saved: tech stack info and annotated endpoint catalog.
(Edges: Orchestrator -> Fingerprint agent after crawling. Fingerprint agent -> database to save findings. Fingerprint agent -> Exploit Planning agent: passes along key findings, such as identified software versions and noteworthy endpoints, as input for exploit generation.)
Node 4: Graph-RAG Exploit Recommendation Agent – This agent is triggered once the system knows what tech and potential vulns are present. It interfaces with the Exploit Knowledge Graph (powered by Graphiti + vector search). The agent formulates queries to find relevant exploits. For instance: “Find exploits for WordPress 5.8”, or “Are there known CVEs for Apache version X.Y?” – Graphiti can handle such queries by matching the tech node and traversing to vulnerability nodes
github.com
. The agent also does semantic search: e.g., embedding the fingerprint info or error messages and querying the vector index for similar exploit descriptions. The result is a set of possibly relevant vulnerabilities/exploits, each with some metadata (CVE id, description, exploitDB id or code). The agent filters and prioritizes these based on severity and context (e.g., if the target is running an old version with a public RCE exploit, that’s high priority). It then produces a “Vulnerability Brief” for the next step: a summary of candidate vulns/exploits the system might attempt. For example: “Detected PHP 7.2 – known memory corruption CVE-2019-11043 with exploit available
exploit-db.com
; Detected jQuery 3.5 – no known critical CVEs; WAF is Cloudflare – might block common SQLi payloads.”
(Edges: Fingerprint agent -> Exploit Recommendation agent: provides tech list and any discovered version info. Exploit agent -> Exploit Planner agent: passes the curated list of likely exploits or vulnerabilities to attempt.)
Node 5: Exploit Planner Agent – This is an LLM-driven agent responsible for crafting the exploit plan for each selected vulnerability. It takes input from the previous agent (e.g., “Apache Struts CVE-2017-5638 likely present”) and the data collected (endpoints, parameters, etc.), and then generates a detailed plan. The plan includes: exploit method (e.g., a specific HTTP request or code snippet), payload (exact code or injection to use), and expected outcome. The agent may use few-shot examples of exploit writing to ensure a structured output. It also references the knowledge base for any existing PoC (some Exploit-DB entries have code that could be re-used). For instance, for CVE-2017-5638, it might outline: “Send a Content-Type header with malicious OGNL expression to trigger RCE; see EDB-41570 for payload. Expect a reverse shell.” The output is formatted clearly (possibly as markdown or JSON with fields for steps, payloads, etc.).
After preparing the plan, this agent notifies the Orchestrator that human approval is needed. LangGraph can model this as a waiting state: no edge to execution until approval input is received. The system will present the exploit plan to the user (e.g., via CLI prompt or UI) for review.
(Human Decision Point) – Here, the flow leaves autonomous operation until the user responds. The human can approve, reject, or ask for modifications. In LangGraph’s graph, one could model the user response as triggering different edges (approve -> Execution agent, reject -> skip to next exploit or end, etc.).
Node 6: Exploit Execution Agent – If the user approves an exploit plan, the Orchestrator triggers this agent to carry it out. The Execution agent uses the instructions from the plan to perform actions. This could be as simple as making an HTTP request with requests library or cURL, or as involved as launching a Playwright browser to bypass a WAF (for example, solving a CAPTCHA or using a headless browser to execute an XSS payload). The agent runs the steps exactly as outlined and logs each action and result. It will capture outputs – for instance, if the exploit was dumping a database table, the agent saves the dumped data (in a safe location) or at least the fact that it succeeded along with a snippet of output. If an exploit fails or is partially successful, the agent notes that as well. After attempting the exploit, control returns to the Orchestrator. The system can either iterate to the next identified vulnerability (back to Node 5 for the next plan) or conclude the engagement.
(Edges: Approval -> Execution agent for a specific exploit. Execution agent -> Data storage: saves results. Execution agent -> Orchestrator: signals completion of that exploit attempt, possibly with success/failure state. Orchestrator may loop back to the Exploit planner for another item, or end.)
Node 7: Reporting/Logging Agent – This is not so much an active “agent” requiring AI, but a module that runs throughout to compile results. It could be implemented as event listeners or simply at the end by collating the database content. The idea is to produce a coherent report or knowledge base of everything found. This includes a graph of relationships (perhaps generated from Graphiti or separate) showing host → tech → vulnerabilities → exploits. It may also generate an OWASP-style report or at least a summary for the user. In LangGraph terms, this could be a final node that triggers after all exploits are done, calling an LLM to help format a nice summary. (For now, consider it a utility component.)
In summary, the LangGraph node map is roughly: Orchestrator → Crawler → Fingerprinter → Exploit Recommender → Exploit Planner –(wait for approval)→ Execution (→ loop back or finish). The edges ensure a logical flow; e.g., the Crawler must finish before fingerprinting starts, exploit planning must wait until after we know what’s vulnerable, and execution waits for the user’s go-ahead. This graph approach brings robustness: each agent can be improved or replaced independently, and the Orchestrator can handle errors or iterations (for example, if no vulns are found, skip exploit phase; or loop if new pages discovered during exploit, etc.). Notably, this architecture mirrors the real penetration testing phases (reconnaissance, scanning/analysis, exploitation) as separate agents, an approach proven effective in research
arxiv.org
. By using LangGraph, we gain the flexibility of complex flows (including possible parallelism, e.g. fingerprinting might parallelize analysis of different data streams) with a clear visualizable node graph. Future enhancements like adding a “Reflection agent” (to assess and maybe re-run certain steps if something was missed) can be inserted into the graph with minimal disruption. (For a visual representation, one could imagine a flowchart where nodes are labeled as above and arrows indicate the sequence. The use of a directed acyclic graph (DAG) for tasks is intentional – it prevents circular dependencies and ensures the pentest progresses in stages
arxiv.org
. However, the design also allows going back (e.g., if exploitation of one vulnerability reveals new pages or info, the system could feed that back into the recon or analysis stage – possibly as an enhancement).)
Suggested Tech Stack (Open-Source First)
Cipher-Spy is composed entirely of open-source technologies, aligning with the requirement to avoid proprietary components (aside from optional OpenRouter access). The suggested stack is as follows:
Programming Language: Python 3.10+ – The majority of the system will be implemented in Python, due to its rich ecosystem of security tools and AI libraries. Python is ideal for integrating Playwright (via playwright Python library), data analysis, and interacting with ML models. Additionally, many security tools (Wappalyzer, Wafw00f, SearchSploit, etc.) have Python interfaces or CLI that can be invoked from Python.
Multi-Agent Orchestration: LangGraph (with LangChain) – LangGraph provides the framework to define our agent workflow as a graph and manage stateful execution
blog.langchain.dev
. It builds on LangChain, so we can leverage LangChain’s tools and agent patterns as needed. By using LangGraph, we get support for cycles, concurrency, and complex control flows beyond simple sequential chains (important for handling iterative scans or conditional logic). The agents themselves can be implemented as LangChain custom agents or Chains, and LangGraph will coordinate them.
Browser Automation: Playwright – We will use Playwright for its powerful headless browser automation capabilities. Playwright supports Chromium, Firefox, and WebKit, which is useful for evading certain client-side defenses. It also provides network interception APIs to catch HTTP traffic
medium.com
. Playwright’s resilience (auto-waiting, etc.) helps in crawling dynamic SPAs or handling login flows. We considered Selenium, but Playwright is more modern and scriptable, plus it has a Python library. For crawling large sites, we might integrate an existing crawler logic or heuristics (to avoid infinite crawling loops, handle robots, etc.). Playwright will run inside the Docker container (likely using headless mode).
Network Interception & Analysis: We rely on Playwright’s page.on('request') and page.on('response') events to intercept API calls
medium.com
. No separate proxy is needed, though an alternative could be OWASP ZAP in daemon mode for more extensive scanning (optional). However, to keep everything in one flow, Playwright’s built-in routing suffices for capturing and even modifying requests if needed.
Fingerprinting Tools:
Wappalyzer: We will use the Wappalyzer Python library (wappalyzer-next)
pypi.org
, which includes up-to-date fingerprints. This requires a browser context to run (it uses Selenium or Playwright internally to render pages), but since we already have Playwright, we can possibly reuse the browser context for Wappalyzer. If that’s complex, we can use a headless Firefox instance via Selenium just for Wappalyzer’s scanning as the PyPI suggests (geckodriver needed). Wappalyzer gives us detections of hundreds of technologies (CMS, JS libraries, server, etc.) by matching known patterns in HTML/JS/CSS.
WafW00f: For WAF detection, WafW00f (Python) will be included. It sends a series of requests and inspects responses to identify common WAF signatures
null-byte.wonderhowto.com
. We can run WafW00f against the target host (it’s a command-line tool/library). If WafW00f finds a WAF (like Cloudflare, F5 BIG-IP, etc.), that info is logged.
Other Fingerprinters: Possibly whatweb or BuiltWith CLI (though BuiltWith is not fully OSS). WhatWeb (Ruby-based) could be considered, but Wappalyzer covers a lot of the same ground. We might also incorporate Nmap for server fingerprinting if needed (e.g., an Nmap scan on the host to identify open ports and services, if in scope). Nmap has scripts for detecting tech, but since this is web-focused, it’s optional.
Exploit Knowledge Base:
Graphiti (by Zep) for the knowledge graph core
github.com
. Graphiti requires a Neo4j graph database (open source community edition)
github.com
. We will run Neo4j in a container. Graphiti will be used to ingest Exploit-DB data as a graph of nodes/edges. We’ll use the Python graphiti-core package. The vector database can be handled in two ways: Graphiti itself can use Neo4j’s indexing for embeddings (Neo4j 5 has support for vector similarity) or we integrate a separate vector store like ChromaDB or FAISS. Since Graphiti is built for hybrid search (semantic + graph)
github.com
, it likely expects embeddings to be stored; Neo4j might store them as properties or rely on an external index. We could also use Weaviate or Milvus, but that’s heavy – likely Chroma (an open-source Python vector DB) in-memory or persisted to disk.
Exploit-DB Data: We will use the Exploit-DB repository (searchsploit)
exploit-db.com
. The plan is to either directly use the CSV files that come with searchsploit (Exploit-DB has files_exploits.csv listing all exploits with fields like EDB-ID, description, date, author, platform, etc., and similar for shellcodes and papers). We can periodically update this data by pulling from the repo (possibly an updater script, or instruct users to run searchsploit -u). This data is ingested into Graphiti: e.g., each exploit entry becomes a node or an episode in Graphiti, annotated with its description and any CVE references. If CVE IDs are present, we link those to CVE nodes. If a platform or product name is in the title, we create a tech node (though matching product names to what Wappalyzer finds can be tricky; we might rely on CVEs or common aliases). We will also embed exploit descriptions using an open-source embedding model (perhaps SentenceTransformers like all-MiniLM) for semantic search. This setup ensures our Graph-RAG agent can find relevant exploits quickly by either direct graph lookup (e.g., find exploits connected to “WordPress”) or vector similarity (find exploits whose description is similar to “WordPress 5.8 attack”).
LLM (Language Model) Integration:
Local LLM: We plan to include support for an open-source LLM for offline or budget-conscious operation. This could be a model like Llama 2 (70B) or CodeGPT, possibly running on the user’s hardware if feasible (or via a local server). However, generating complex exploit plans might require a powerful model; smaller local models might struggle with accuracy. We will integrate with LangChain’s interface so that swapping the model is easy.
OpenRouter: For the best results, the system can use OpenRouter to access models like GPT-4, GPT-3.5, or Claude. OpenRouter provides a unified API for many top-tier models
helicone.ai
, so the user just needs one API key. The default configuration will attempt to use GPT-4 via OpenRouter for tasks like summarizing complex output or generating exploit plans, since GPT-4’s knowledge and reasoning are very useful (and it has knowledge up to 2021 which covers many exploits, though we supplement with our own DB for 2022-2025 exploits). If OpenRouter is not configured, the system will fall back to the local model. All LLM calls will go through a careful prompt design to ensure they follow instructions (for example, when generating exploit steps, emphasize not to actually execute anything in the generation, just describe steps, etc.).
Why not fully local? – The only non-open-source dependency we allow is OpenRouter (and through it, potentially an OpenAI model). This is explicitly allowed as per requirements. While we aim for fully open operation, having GPT-4 accessible can significantly enhance the quality of vulnerability analysis and exploit generation. It’s a trade-off left to the user; the system is functional with open models, but “bring your own key” for GPT via OpenRouter can supercharge it.
Backend Framework: The orchestrator and agents will be coded in Python scripts, but we will likely structure the tool with a FastAPI or Flask back-end. This could provide a local web service or UI. For example, FastAPI can serve a simple web dashboard where the user enters the target URL, starts scans, and views results in real-time. It can also expose REST endpoints (for advanced users to integrate Cipher-Spy in their pipelines). FastAPI is open-source and lightweight, a good fit for a containerized app. If we choose to build a minimal GUI, we might use a small React or Svelte front-end served by this API (though a heavy GUI is optional). For CLI-only usage, we might not need a web server at all – just a CLI interface.
Database: PostgreSQL will serve as the primary data store for scan results. We choose Postgres for reliability and its ability to store JSON (for flexible data like API responses) if needed. Alternatively, Supabase (which is essentially Postgres with an API layer) could be used if someone wanted an easy cloud-like interface, but since we stay local, we’ll run Postgres directly (Supabase’s open-source docker could be used as well, but it’s not necessary unless we want the convenience of its RESTful API or auth features, which we probably don’t need). Postgres schemas/tables will include: hosts, pages, endpoints, technologies, vulnerabilities, etc. (detailed in Data Schema section). We will use an ORM like SQLAlchemy or Pydantic models to manage data.
We also use Neo4j for the knowledge graph (as mentioned). So effectively, we have two datastores: Postgres for operational data, and Neo4j for the exploit knowledge. This is by design – each is used for what it’s best at. They can be connected by storing references (e.g., if we have a vulnerability entry in Postgres that has a CVE, we might also point to the Neo4j node for that CVE if needed for advanced querying).
Containerization and DevOps: We will create a Docker Compose setup:
A container for the Cipher-Spy Python app (with LangChain, Playwright, etc.). This container will include browsers for Playwright (likely installed via Playwright’s installer in Docker).
A container for PostgreSQL database (or the user can provide a DSN to an existing Postgres, but out-of-the-box we supply one for easy start).
A container for Neo4j (community edition) for Graphiti. We will configure it with appropriate memory in the compose file.
Optionally, a container for a Vector DB if not using Neo4j for that (e.g., a Chroma server or Weaviate, but this might be overkill – we can likely just do embedding search in-memory).
The Docker images will be based on official Python slim images plus needed libs. We’ll ensure the image has all dependencies (like nmap or wafw00f if needed).
For deployment, the user just needs Docker installed, then docker-compose up to get everything running.
Open-Source Tools Summary:
LangGraph (or LangChain) – Orchestration (OSS, LangChain’s license is MIT).
Playwright – Crawling & interaction (Microsoft Playwright, Apache 2.0 license).
Wappalyzer – Tech fingerprint (GPLv3, but since it’s local and we’re not redistributing code beyond container, should be fine; the project by s0md3v is GPL).
WafW00f – WAF detection (MIT License).
Graphiti – Knowledge graph (Apache 2.0, via Zep). Requires Neo4j (Neo4j CE is GPL AGPL but the community edition is free to use).
Neo4j – Graph DB (AGPL for community, acceptable for OSS usage as long as we’re not modifying it).
Chroma or FAISS – vector search (Chroma is MIT, FAISS is MIT).
LLMs – If using local, something like Llama 2 (meta license) or OpenLLM. If using OpenRouter, that’s just an API aggregator (the models behind it might be OpenAI’s GPT which is not open-source but accessed via API; since this is a user-provided key scenario and optional, it adheres to the “only allowed paid service”).
FastAPI – for any API/UI (MIT).
PostgreSQL – database (open source, PostgreSQL license).
This tech stack ensures compliance with the requirement of open-source-first and local deployment. By combining these tools, we leverage state-of-the-art capabilities: modern web automation, robust fingerprinting, a cutting-edge approach to AI memory (Graphiti’s knowledge graphs)
github.com
, and the power of LLMs – all orchestrated in one cohesive system.
Deployment Strategy
Deployment of Cipher-Spy is intended to be straightforward for end users, as it targets professionals who may not want to fuss with complex setups. Key points of the deployment strategy:
Local Installation via Docker: We will distribute a Docker Compose configuration that defines all necessary services. The user should be able to get started with:
git clone <cipher-spy-repo>
cd cipher-spy
docker-compose up --build
This will fetch/build the images and start the system. We’ll publish a pre-built Docker image on DockerHub for the main app to speed this up (so docker-compose will just pull it). The Postgres and Neo4j images will be official ones. The compose will also mount a volume for persistent storage (so the database data isn’t lost between runs, unless user chooses to run ephemeral).
Resource Considerations: Running Playwright with a headless browser and possibly a large LLM can be resource-intensive. We will advise at least 4 CPU cores and 8 GB RAM available. The Neo4j DB for exploit graph might consume a couple of GB if the entire Exploit-DB (~40k entries) is ingested with embeddings. We might provide options to limit data (like only load exploits after year 2010, or only web-related exploits) to reduce footprint if needed. The container can be configured with environment variables for memory limits, etc.
Configuration: Users can configure Cipher-Spy via environment variables or a config file. Notable configs:
OpenRouter API key (if they want to use GPT-4) as OPENROUTER_API_KEY. If not set, we assume local LLM mode.
Target URL or scope settings could be given via the CLI each run (or through the UI).
Safe mode vs live mode: perhaps a flag to indicate if actual exploits should be executed or just simulated (for users who want to ensure no real damage, e.g., on a production-like environment do only simulations).
Credentials for target (if they want the crawler to authenticate, they could supply a username/password or session token).
Database connection (if someone wants to use an external Postgres instead of the one in Docker).
Logging level, etc.
Running Modes:
Interactive CLI Mode: By default, if launched in a terminal, it might drop into an interactive CLI or TUI (text UI) where the user can input commands like “start scan”, “show endpoints”, etc. This is for users who prefer console tools.
Web Dashboard: Optionally, starting the container could also launch a small web server at http://localhost:8000 where a UI is available. This UI could show progress (like “Crawling… found X pages”), list findings as they come, and present exploit plans with Approve/Reject buttons. This is user-friendly for those who prefer a GUI. We must ensure this interface is also running locally and not exposed publicly (Docker by default will bind to localhost or the Docker host network as configured).
API Mode: The tool could be used purely via REST API for integration. For instance, a user could start it and then use an API call to trigger a scan and later retrieve results in JSON. This would allow scripting or integration with other systems (like a CI/CD pipeline or a custom interface).
Security of Deployment: Since Cipher-Spy interacts with potentially sensitive client data (the target web app might be confidential), running it locally is crucial. We will highlight that no data is sent out except optional LLM queries (which, if using GPT-4, will send prompts to OpenAI via OpenRouter). Users with strict data policies can opt to use only local LLMs to avoid even that. All data at rest is in the user’s environment (Postgres DB, etc.), which they control. The Docker environment should be run on an isolated network if testing a live target to avoid unintended network exposure. Also, if the user’s machine has other network connectivity (like VPN to a client’s environment), Docker networking needs to be configured appropriately so that the container can reach the target through that VPN (this might require network_mode: "host" in compose if necessary to use host’s network interfaces).
Updating: We will provide update instructions. Because it’s containerized, updating might mean pulling a new container version. However, the Exploit-DB data needs updating regularly. We can include a step in the startup that checks for new exploit DB entries (e.g., runs git pull on the exploitdb repo if internet is available) or instruct the user to periodically update (maybe a command docker exec cipher-spy python update_exploits.py). As an alternative, we could host a lightweight feed of new exploits (since Exploit-DB updates daily
exploit-db.com
) but that reintroduces reliance on external service, so better to let the user update manually.
Testing Environment: For safety, we’ll recommend users test Cipher-Spy on known vulnerable test sites first (like OWASP Juice Shop, DVWA, etc.) to see how it behaves. We might include some of these as part of integration tests. Also, ensure that if someone accidentally points it to something out-of-scope or a production site, it doesn’t auto-fire exploits without permission (our human approval gate is key here).
Deployment Diagram (conceptual): The deployment has the user’s machine (or VM) running Docker. Inside Docker, multiple containers: one for the Cipher-Spy core, one for Postgres, one for Neo4j. The core container communicates with Postgres and Neo4j over internal Docker network. The user interacts either through CLI attached to the core container or through a browser to the web UI served by the core container. The core container (which has the LLM agents and crawler) will directly reach out to the target URL over the internet or local network. If using OpenRouter (LLM API), the core container will also make HTTPS requests to api.openrouter.ai (or similar) with prompts – this is the only external service used. All other components are local.
In summary, deployment is local by default, containerized for portability, and configurable for different usage styles (CLI vs web). By shipping as Docker images, we avoid the “works on my machine” issues – the correct versions of Node (for Playwright), Python libs, system dependencies (like nmap or wkhtmltopdf if needed for reporting) are all baked in.
Data Schema Design
Cipher-Spy will accumulate a variety of data (targets, pages, APIs, technologies, vulnerabilities, etc.). A clear schema is needed both for internal use and to allow users to query the results. We will design a relational schema in PostgreSQL for the core scan data, and outline the knowledge graph schema in Neo4j for exploit/vulnerability data. Relational Database (PostgreSQL) Schema:
We propose the following tables (with relationships) to capture the scanning results:
targets: Represents a target scope (typically one per run, e.g., a base URL or domain).
target_id (PK)
name – a human-friendly name or the URL/domain.
base_url – the starting URL.
scope – text field for scope definitions (e.g., allowed domains or paths).
scan_date – timestamp of the scan.
Possibly fields like status (completed, in-progress).
pages: Web pages discovered.
page_id (PK)
target_id (FK to targets)
url – full URL of the page.
status_code – HTTP status code received.
title – Page <title> if any.
content_hash – maybe a hash of content to detect duplicates.
screenshot_path – if we capture a screenshot for reference (optional).
html_content – we might not store full HTML in DB (could be huge), but perhaps store snippet or none. More likely, we store raw pages only if needed; otherwise, just metadata.
Relationships: One target has many pages.
endpoints: API endpoints or significant HTTP requests captured.
endpoint_id (PK)
target_id (FK)
url – the URL (path and query) without domain (or with, depending if multiple subdomains).
method – GET/POST/etc.
request_params – could be JSON (e.g., {"q": "test"}) for query or body params.
response_status – e.g., 200, 404.
response_content_type – to know if JSON, HTML, etc.
response_sample – maybe a snippet of the response body or the whole body if small (if JSON, we can store truncated version).
observed_in_page – FK or link to page that initiated it (if known).
notes – text for any analysis notes (e.g., "returns user data, possibly sensitive").
Uniqueness: We might treat unique endpoint by URL template. Possibly have separate table for “endpoint definitions” vs “instances” because the crawler might hit the same endpoint multiple times with different params. To simplify, we record each unique URL+method encountered; we can generalize later (like replace numeric IDs in URL with {id}).
Relationship: target has many endpoints. Could also link to a page if the endpoint was called from a specific page.
technologies: Detected technologies for a target.
tech_id (PK)
target_id (FK)
name – e.g., "Drupal", "jQuery", "Cloudflare WAF".
version – e.g., "9.3.0" (if detected; might be null if not exact).
category – e.g., "CMS", "JS Library", "Web Server", "WAF", "Database".
confidence – perhaps a score or enum (High/Medium/Low) indicating detection confidence.
This table gets populated by the fingerprinting agent using Wappalyzer’s results and other clues.
Relationship: target has many technologies.
vulnerabilities: This table lists vulnerabilities discovered or strongly suspected on the target.
vuln_id (PK)
target_id (FK)
description – short description e.g., "Outdated OpenSSL version susceptible to Heartbleed".
type – category like "SQL Injection", "XSS", "Known CVE", etc.
severity – e.g., High/Med/Low (maybe auto-filled from CVSS if known CVE).
evidence – details or references (could be a link to exploit or some proof like error messages).
related_endpoint_id – if the vuln is tied to a specific endpoint (e.g., SQLi on /api/search).
related_tech_id – if tied to a technology (e.g., a CVE in a library).
cve_id – if applicable, store CVE or exploit identifiers.
The vulnerabilities table is filled after analysis/Graph-RAG steps. It basically flags things the agents believe are exploitable. Some entries might come directly from known CVEs (like tech fingerprint found Drupal 9.3, which has CVE-2023-XXXX; we add that as a vuln).
Relationship: target has many vulnerabilities. Each vulnerability may link to one tech and/or one endpoint.
exploits_attempted: Records exploits that were executed (or at least planned).
exploit_id (PK)
target_id (FK)
vuln_id (FK, the vuln this exploit addressed, if applicable)
exploit_name – like "Exploit-DB 41700" or a descriptive name.
plan – the plan that was generated (store the text or JSON of steps for reference).
approved_by_user – boolean/timestamp, indicating user gave the go-ahead.
execution_time – timestamp when run.
status – e.g., "Success", "Failed", "Blocked by WAF".
result – details of outcome (could be lengthy text or reference to an output file).
This table is crucial for logging what we did to the target. It essentially forms an audit log of our actions.
agents_log (optional): If we want to store a history of agent communications or decisions (like an agent reasoning chain). This could be a JSON or text log capturing prompts and responses of the LLMs for debugging. This might be too granular to store in DB; maybe just keep in memory or write to a log file.
The above tables cover the core data. We will ensure to use foreign keys and indices (e.g., index endpoints by URL for quick search, index technologies by name, etc.) so that the data is queryable and maintain referential integrity. Knowledge Graph (Neo4j) Schema:
The knowledge graph in Neo4j (managed by Graphiti) is more about general exploit/vuln knowledge rather than target-specific. However, we might also incorporate some target-specific knowledge into Neo4j if it aids reasoning (like a node for the target that links to tech nodes it uses). Initially, we can keep it separate: Proposed node labels and relationships:
Technology nodes: e.g., "WordPress 5.8", "Django 3.2". These can have properties like name, version, maybe normalized product ("WordPress"). We might also have generic Product nodes separate from version-specific ones (Graphiti allows custom ontology). For example, Product(name="WordPress") can link to Version nodes or vulnerabilities. Simplification: just include version in node name for now.
CVE nodes: represent specific vulnerabilities, identified by CVE-ID. Properties: cve_id, description, cvss_score, etc if available.
Exploit nodes: represent exploit instances (mostly from Exploit-DB). Properties: edb_id, title, date, maybe author, and a snippet or link to the actual code. Possibly a type (remote/local/webapp, etc.).
Paper or Article nodes: (if including Exploit-DB papers or external writeups, not sure if needed; could skip).
Shellcode nodes: if we ingest shellcode separate from exploits.
Relationships:
TECH_HAS_VULN – between Technology and CVE (or could directly link Tech to Exploit if no CVE known). For example, Tech("Apache Struts 2.3") -[HAS_VULN]-> CVE("CVE-2017-5638").
CVE_HAS_EXPLOIT – links CVE to Exploit nodes. E.g., CVE-2017-5638 -[HAS_EXPLOIT]-> Exploit(EDB-41570).
If no CVE reference is available but exploit title says the product, we might link Technology to Exploit directly with TECH_HAS_EXPLOIT or something. Or create a pseudo-vuln node for “Unknown vuln in Product X”.
We might also link Technology to Technology for version hierarchy or to a Product node (like WordPress 5.8 -> WordPress (generic) if needed).
If we incorporate target context: we could create a node representing the Target and connect it to Technology nodes it uses (Target -[USES]-> Tech). Then to find exploits for a target, we find all Tech it uses, traverse to vulns and exploits. This might be overkill since we can just query SQL for tech then graph for exploits. But Graphiti could allow a single Cypher query to get a full picture, which is elegant. As an optional integration, we might push discovered tech from Postgres into Neo4j as well (since Graphiti supports incremental updates
github.com
). This would truly unify knowledge and target data in one graph.
The vector index: Graphiti associates embeddings with nodes/edges for hybrid search
github.com
. We will embed textual info:
Exploit descriptions (and maybe CVE descriptions) get embeddings.
Possibly technology names too (though those are short, not needed).
Graphiti can then do similarity search: e.g., given a query “Oracle database exploit”, it finds relevant nodes. The Graph-RAG agent will use Graphiti’s API to query: Graphiti likely has functions like graphiti.search("Oracle exploit") which returns relevant nodes/edges via semantic + graph search.
All of this graph data is stored in Neo4j. We will ensure to enforce some basic uniqueness (like single node per CVE, unique index on cve_id; single node per exploit by edb_id, etc.). Graphiti likely handles a lot of that if we define Pydantic models. Data Flow:
When a target scan starts, we insert a row in targets.
Crawler finds pages -> insert into pages.
Crawler finds endpoint -> insert into endpoints (ensuring no duplicates; maybe check by URL).
Fingerprinter finds tech -> insert into technologies (with target_id).
Graph-RAG finds possible vulns -> insert into vulnerabilities (with references).
When exploit is approved/executed -> insert into exploits_attempted.
The knowledge graph is pre-populated (or populated on startup) with exploits and CVEs. We might have a command to do that separately since it’s heavy (e.g., ./ingest_exploits.py). Once done, during scans we query it read-only. If we update the exploit DB, we update Neo4j accordingly.
Example Scenario: Target has WordPress 5.8:
In technologies: (target=Target1, name=WordPress, version=5.8).
Our knowledge graph has Tech node "WordPress 5.8" -> CVE-2020-XX (example) -> Exploit EDB-123. Suppose one such vulnerability is found.
The Graph-RAG agent would identify that and we add a row in vulnerabilities: (target=Target1, description="WordPress 5.8 RCE CVE-2020-XXXXX", type="Known Vulnerability", severity="High", cve_id=... , related_tech=WordPress tech_id).
Then Exploit planner might create a plan for EDB-123. When user approves, an entry in exploits_attempted is added.
This structured approach means the user (or an external system) can retrieve results easily. For instance, an SQL query can join technologies->vulnerabilities->exploits_attempted to see which known vulns were exploited on which tech. Finally, we ensure that graph relationships are also understandable to the user. In a report, we might present them as a visual or listing:
“Target uses X, which has vulnerability Y (CVE), which has exploit Z
exploit-db.com
.” Essentially drawing from the knowledge graph relations. We will maintain a mapping between the relational data and knowledge graph where it makes sense (like using the CVE or technology name as a key). This dual storage might seem redundant but serves different performance goals: SQL for structured target data, graph for rich knowledge querying. The data schema is designed to be extensible. If we later add host scanning (like IP ports, etc.), we could add a services table for open ports. If we integrate code scanning, we add tables for code issues, etc. The current schema covers web app pentest needs thoroughly.
Development Milestones
To build Cipher-Spy, we’ll break development into clear milestones, each delivering a set of functional features. This roadmap allows incremental testing and feedback at each stage: Milestone 1: Basic Crawler and Data Capture (Weeks 1-3)
Goal: Establish the core crawling ability and database integration.
Implement Playwright Crawler: Write a Python module to launch Playwright, input a target URL, and crawl links. Ensure it can handle a simple site (e.g., a few pages) and record visited URLs.
Network Logging: Enable Playwright’s request/response logging
medium.com
. For every request to the target domain, log URL, method, status, and save perhaps first 1KB of response content. Verify we can capture AJAX calls.
Database Setup: Define the Postgres schema (initially: targets, pages, endpoints). Integrate SQLAlchemy and create the tables.
Saving Data: On crawling, save discovered pages and endpoints to the DB. Test that if we crawl again, we can avoid duplicate entries (maybe by checking URL uniqueness).
CLI Invocation: Create a basic CLI command (e.g., cipher_spy crawl <url>) that triggers the crawler and prints a summary (number of pages, endpoints found).
Demo/Test: Run against a known test site (like http://example.com or a deliberately vulnerable app) and ensure pages and endpoints are listed. No AI or analysis yet, just raw data capture.
Milestone 2: Technology Fingerprinting (Weeks 4-5)
Goal: Add Wappalyzer and WAF detection to identify tech stack.
Integrate Wappalyzer: Add the Python Wappalyzer library. Use Playwright’s browser to run Wappalyzer on the main pages. Save results in the technologies table. Each technology detection gets recorded with name/version.
Integrate WafW00f: Use WafW00f to scan the base URL for a WAF. If found, record it as a technology (e.g., “Cloudflare WAF”).
Basic Analysis Agent: Implement an agent (function) that given the crawled data, runs the above fingerprinting and maybe does simple analysis (like scanning responses for error messages or known library file names).
Output User Stories: After crawling, output a summary to user: e.g., “Tech identified: X, Y, Z. No WAF detected” or “WAF detected: Cloudflare”. Also list if any obvious vulns known for those techs (in this milestone, we might hardcode a couple for demonstration).
Testing: Use a site like an old WordPress demo to see if Wappalyzer correctly identifies WordPress and plugins. Test WafW00f on a site behind Cloudflare (perhaps a personal site) for detection.
Milestone 3: Multi-Agent Orchestration with LangGraph (Weeks 6-8)
Goal: Refactor the code to use LangGraph for orchestration and add at least two agents (crawler and analyzer) working together.
Set up LangChain/LangGraph: Install and configure a simple graph where NodeA = Crawler, NodeB = Fingerprinter/Analyzer. The Orchestrator logic to sequence them (Crawler feeds data to Analyzer).
Agent Implementation: Wrap the crawler into an Agent or Tool that can be called (or possibly treat it as just a function called by the chain – since crawler is not LLM-based, it’s more like a tool).
LLM Integration (basic): Include a lightweight LLM (maybe GPT-3.5 via OpenRouter or a small model) and use it in the Analyzer agent to summarize findings. E.g., have it read through the list of endpoints and pages and ask “does anything look interesting?” as a test of integration.
State Management: Use LangGraph state to pass the list of endpoints from Crawler to Analyzer agent. Ensure that works (we may use a shared memory or context object).
Human Approval Node (Placeholder): Introduce a dummy node in the graph to represent the human approval step, to ensure the graph architecture can handle a pause. This can be simulated by an agent that always “waits” for input (we might skip actual waiting in tests but lay groundwork).
Testing: Create a scenario in LangGraph (maybe via a YAML or code) and execute it end-to-end on a small site. Confirm that after crawling, the next agent prints out tech stack info. This milestone might not yet include the exploit knowledge, but it sets up the skeleton.
Milestone 4: Exploit Knowledge Graph Integration (Weeks 9-12)
Goal: Build the vulnerability knowledge base and connect it to the analysis.
Set up Neo4j and Graphiti: Launch a Neo4j instance and connect using Graphiti. Define Graphiti data models for Tech, CVE, Exploit as per our schema.
Ingest Exploit-DB data: Write a script to parse Exploit-DB’s CSV or use searchsploit output. Create nodes and relationships in Neo4j via Graphiti. This is heavy – ensure it’s done offline or at startup. We should see in Neo4j, e.g., Node “PHP 5.4” -> CVE-2014-0160 (Heartbleed) -> Exploit #32745.
Graph-RAG Agent: Implement an agent that given the identified technologies (from Milestone 2’s output), queries Graphiti. Use both exact matching (tech name) and embedding similarity (Graphiti or manual search). For each tech, retrieve top N relevant exploits.
LLM Summarization of Results: Possibly use LLM to filter/prioritize exploits: The agent can prompt the LLM with “Among these known issues: [list], which are likely relevant and serious for this target?” to get a refined list.
Populate vulnerabilities table: For each likely vulnerability found, insert into the vulnerabilities table with details (CVE, description). This populates evidence of potential vulns before actually exploiting.
User Feedback: When the scan completes, output something like: “Potential vulnerabilities identified: 1) CVE-2021-40438 (Apache httpd mod_proxy SSRF) – target uses Apache 2.4.48 which is vulnerable. 2) Outdated jQuery 1.12 – known XSS issues (low severity).” This will show that our knowledge integration works.
Testing: Try with a known vulnerable stack (e.g., Juice Shop uses Angular older version – see if we have anything on that). Or deliberately include a known CVE in a tech and see if it’s found. Evaluate that Graph queries are performing (tune queries if needed).
Milestone 5: Exploit Planning and Human-in-the-Loop (Weeks 13-15)
Goal: Enable the system to generate exploit plans and enforce a pause for approval.
Exploit Planning Agent: Use an LLM (preferably GPT-4 via OpenRouter at this stage to leverage its knowledge) to create exploit plans. Develop prompt templates with placeholders for vulnerability info and any context (like endpoint URLs). The prompt might be like: “You are an expert exploit developer. We have a vulnerability CVE-XXXX affecting X. We have an endpoint Y. Produce a step-by-step plan...”.
Plan Content: Ensure the plan includes summary, steps, payloads. We might define a structured output (like JSON with fields "steps":[], "payloads":[]) for easier parsing. But a well-formatted markdown might be fine too.
Approval Workflow: Implement the mechanism for human approval. In a CLI scenario, after generating the plan, the program should prompt “Execute this exploit? (y/N)”. In a GUI, show an “Approve” button. Under the hood, LangGraph might pause waiting for an external event. If needed, implement a simple wait loop that checks a user input flag.
Execution Agent: Develop the logic to actually perform an exploit step. This could be as simple as running a requests.post with a given payload to test if the vulnerability is present. We will initially implement a safe mode where instead of firing a possibly destructive payload, we do a dry-run. For example, if it’s SQL injection, maybe run a benign input that won’t break anything but can indicate vulnerability (like a time-based SQLi payload AND SLEEP(5) to see if response is delayed). The Execution agent will handle these details and record results.
Logging Outcome: After execution (or simulation), log the result in exploits_attempted and also output to user. E.g., “Exploit attempt of CVE-XXXX: SUCCESS, we retrieved admin account.” Or “FAILED, target seems patched.”
Testing: Use a deliberately vulnerable environment (like DVWA or a test instance with a known CVE) to validate. For example, set up an old WordPress with a known exploit. See if the system identifies it and then test an exploit payload (maybe a harmless one). Ensure the user approval works – i.e., the system waits and does nothing destructive until the tester says yes.
Milestone 6: UI/UX and Final Touches (Weeks 16-18)
Goal: Improve usability and finalize container deployment.
Web Dashboard (Minimal): Create a simple web interface (using FastAPI’s templating or a small React app). It should allow starting a scan by entering a URL, show a live feed of what’s happening (we can push events via WebSocket or simple AJAX polling), and crucially, present exploit plans for approval with a button. This greatly improves UX for those who prefer it over CLI.
CLI polish: Make sure the CLI outputs are clear and nicely formatted (use rich library or at least clear headings). Possibly support interactive mode to navigate results in terminal.
Reporting: Implement a function to generate a PDF or markdown report at the end of a scan, summarizing findings (targets, tech, vulnerabilities, exploits). This could use an LLM to nicely format the narrative parts, and include tables or lists from the DB.
Performance Tuning: Profile the crawling speed (maybe add multi-threading for fetching pages in parallel, within limits) – though Playwright is single-browser, we could spawn multiple browser contexts if needed for performance. Optimize any slow Cypher queries in the Graph-RAG phase by adding indexes or adjusting approach.
Security & Stability: Ensure timeouts are in place (e.g., crawler doesn’t hang forever on a page, LLM calls have a timeout, etc.). Also implement any safety checks (like to avoid infinite redirect loops, respect robots.txt optionally, etc.).
Container Testing: Test the full docker-compose on various OS (Windows, Linux, Mac) to catch any environment issues (e.g., Xvfb for Playwright on Linux, file permissions for volumes, etc.).
Documentation: Write docs for installation and usage. Provide examples. Also document how to update the exploit database and how to configure OpenRouter.
Final Demo: Run a full demo scan on a known vulnerable web app and verify that: pages are discovered, tech is identified, a known vuln is found, exploit plan is generated, and after approval, it’s executed with logged results. This demonstrates the complete flow working.
Each milestone builds on the previous, and after Milestone 5 we essentially have a working prototype with all major features. Milestone 6 is about making it user-friendly and robust for real-world use. The timeline of ~18 weeks (roughly 4-5 months) is feasible for an experienced team, given many components are leveraging existing libraries. Throughout development, we will engage in testing after each milestone, likely with increasing complexity. Unit tests for smaller components (like the parser for Wappalyzer output, or the function that queries Graphiti) will be written. Integration tests with something like a local Juice Shop instance will validate multiple components together (especially the LLM decisions). Also, we’ll incorporate user feedback after milestone 4 or 5 by perhaps sharing with a friendly tester to get impressions on workflow. Regular updates to the Exploit-DB knowledge will be considered – maybe by milestone 4, we script an update mechanism. Also, as an ongoing task, we ensure compliance with the open-source only mandate: double-check licenses and that container doesn’t inadvertently include something non-OSS. Using GPT-4 is behind an API (OpenRouter) so it’s fine. By the end of these milestones, Cipher-Spy should be a comprehensive, usable tool that meets the initial requirements.
Optional Enhancements
With the core system in place, there are several enhancements we can consider for future versions. These are optional and not required for the initial release, but they would add significant value:
Scheduled or Continuous Scanning: Allow Cipher-Spy to run on a schedule (e.g., nightly scans of a target) or continuously monitor a target for changes. This would involve a scheduler within the app or simply allowing it to be triggered by external cron. The idea is to automatically detect new endpoints or vulnerabilities over time. For example, if a new version of a library is released with a security fix, and the target is still on the old version, the knowledge base (if updated) could flag that in a subsequent scan. Continuous scanning turns Cipher-Spy into more of a monitoring tool, potentially useful for ongoing security posture management.
Integration with External Exploit Tools (Plugin System): Provide a plugin mechanism to integrate exploit execution with tools like Metasploit, SQLMap, or custom scripts. While the current design uses our own execution agent, some users might want to leverage well-known tools for certain exploits (e.g., use SQLMap for SQL injection for thorough exploitation, use Metasploit modules for RCE). A plugin system could allow adding modules that, for instance, take a vulnerability description and run Metasploit with a matching module. This would broaden what Cipher-Spy can do (especially post-exploitation tasks like establishing shells). The architecture would treat these as additional execution methods the user can approve. Open-source tools like Metasploit (with its database and RPC API) could be run in another container and triggered by Cipher-Spy. This optional feature could even enable a sort of “App Store” of exploits where community can contribute specific exploit modules.
Graphical Visualization of Findings: Implement a UI feature to visualize the knowledge graph or the site structure. For example, show an interactive graph where nodes are tech and vulns, edges show exploit relations (like a mini Neo4j Bloom in the app). Or a graph of the site map with endpoints and how they connect (like nodes for pages and endpoints, edges for navigation or API calls). Visualizations can greatly aid understanding for the user:
A Knowledge Graph View could show: Target -> Tech -> Vulnerability -> Exploit, in a web diagram. The user can see at a glance which part of their stack is most at risk (more connections = more known vulns).
A Site Map View could illustrate all pages and endpoints discovered, maybe color-coded by whether they have vulnerabilities.
We could use libraries like D3.js or Cytoscape for web-based graph viz, or integrate Neo4j’s Bloom (if license allows, though Bloom is not open-source). Alternatively, output a GraphML or Gephi file that the user can load to see the graph.
Advanced Crawling Options: The crawling could be enhanced with options like:
JS Execution and DOM analysis: Already using Playwright we do some, but we might incorporate something like heuristic clickers to trigger menu drop-downs or infinite scroll.
Wordlists for fuzzing: Basic directory brute-forcing (like trying common admin paths) to find hidden pages not linked.
Authentication flows: Provide ways to handle OAuth or multi-step logins, or allow the user to script the login (maybe through a plugin or recording a login sequence).
These improvements would make recon more powerful and reduce manual steps.
Machine Learning based Anomaly/Vuln Detection: Beyond known CVEs, use ML to detect potential issues. E.g., training a model on common API mistakes or scanning responses for hints of SQL errors (beyond simple regex). This is experimental, but could catch 0-days or logic flaws by pattern (for example, an LLM could analyze an API’s functionality and guess if it might be vulnerable to IDOR (Insecure Direct Object Reference) by how it’s structured). This would augment the system’s ability to suggest exploits even for vulnerabilities that aren’t in Exploit-DB.
Collaboration and Multi-Target Management: If multiple team members use it, one might want to have a centralized server where results are stored and team can collaborate on the findings (like an appsec team sharing a running instance). This leads to features like user accounts, multiple target projects in the DB, role-based access (some team members can approve exploits, others only view data). It would transform Cipher-Spy from a single-user tool to a multi-user platform. It’s beyond initial scope but a possible evolution for enterprise use.
Integration with Ticketing/Reporting Systems: As an enhancement for workflow, allow exporting findings to systems like JIRA, or generating compliance reports. For example, if a vulnerability is found, a plugin could create a ticket for developers. Or integrate with DefectDojo (an open-source vuln management platform) by pushing results via API. This would make it easier to operationalize the results of Cipher-Spy’s scans in a larger organization context.
Enhanced LLM Agents (Chain-of-Thought and Reflection): We can incorporate more advanced agent behaviors like self-reflection or iterative improvement. For example, after an exploit attempt, an agent could analyze why it failed and attempt a different payload automatically (within safe limits). This would use the LLM to reason “Maybe the WAF blocked that, I should try a variation”. Autonomy in exploit retry (with guardrails) could improve success rates. Also, using something like GPT-4’s function calling or other new features (if available via OpenRouter) to structure outputs might make parsing easier.
Add Support for Non-web Targets: While Cipher-Spy is currently web-focused, an extension could target other services (network scanning, etc.). For instance, integrating Nmap/Masscan to scan ports, then using an LLM to analyze those results for vulnerabilities (like if port 21 FTP is open, suggest trying anonymous login, etc.). Essentially broaden the scope to full-scope pentest (web, network, maybe even social engineering simulation). This would be quite extensive, so it would likely be a separate mode or module.
User Customization of Knowledge Base: Allow users to add their own exploit data or knowledge. Perhaps they have internal exploit scripts or private vulnerability findings not in Exploit-DB. We could allow them to input those into the Graphiti knowledge graph (maybe via a simple CSV import or UI form). This way, the system can reason over proprietary knowledge too. For example, a user could add “Our in-house app framework v2 has a known flaw” as a node, so if that framework is detected, the system can warn about it.
Each of these enhancements would be planned based on user demand. They all aim to make Cipher-Spy even more powerful and flexible, but care must be taken to maintain usability and avoid turning the tool overly complex. The modular architecture with LangGraph and the solid base we built in v1 should make adding new agents or integrations relatively straightforward. For example, adding Metasploit integration could be as simple as a new execution agent node for exploits that calls Metasploit RPC, and adding a config to enable it. In conclusion, Cipher-Spy’s roadmap doesn’t end with the core features; it opens possibilities to move towards an autonomous security platform. By focusing on a strong foundation and then layering these optional features, we ensure that the system can grow to meet advanced red teaming needs, all while staying true to its open-source, local-first philosophy.