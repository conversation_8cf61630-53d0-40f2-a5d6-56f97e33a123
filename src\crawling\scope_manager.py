"""
Scope management for Cipher-Spy crawling.

Manages crawling scope, enforces domain boundaries, and handles robots.txt
compliance to ensure responsible and targeted reconnaissance.
"""

import re
import asyncio
from typing import Set, List, Optional, Dict, Any
from urllib.parse import urlparse, urljoin
from urllib.robotparser import RobotFileParser
import aiohttp

from ..utils.logging import get_logger


class ScopeManager:
    """
    Manages crawling scope and boundaries.
    
    Features:
    - Domain boundary enforcement
    - Robots.txt compliance
    - URL pattern filtering
    - Depth and breadth limiting
    - Loop detection and prevention
    """
    
    def __init__(
        self,
        base_url: str,
        allowed_domains: Optional[List[str]] = None,
        excluded_patterns: Optional[List[str]] = None,
        respect_robots: bool = True,
        max_depth: int = 5,
        max_pages_per_domain: int = 1000
    ):
        """
        Initialize scope manager.
        
        Args:
            base_url: Base URL for the scan
            allowed_domains: List of allowed domains (wildcards supported)
            excluded_patterns: URL patterns to exclude
            respect_robots: Whether to respect robots.txt
            max_depth: Maximum crawling depth
            max_pages_per_domain: Maximum pages per domain
        """
        self.base_url = base_url
        self.base_domain = urlparse(base_url).netloc
        self.allowed_domains = allowed_domains or [self.base_domain]
        self.excluded_patterns = excluded_patterns or []
        self.respect_robots = respect_robots
        self.max_depth = max_depth
        self.max_pages_per_domain = max_pages_per_domain
        
        self.logger = get_logger(__name__)
        
        # Robots.txt cache
        self.robots_cache: Dict[str, RobotFileParser] = {}
        
        # URL tracking
        self.visited_urls: Set[str] = set()
        self.domain_page_counts: Dict[str, int] = {}
        
        # Default excluded patterns
        self.default_excluded_patterns = [
            r'\.pdf$', r'\.doc$', r'\.docx$', r'\.xls$', r'\.xlsx$',
            r'\.zip$', r'\.rar$', r'\.tar$', r'\.gz$',
            r'\.jpg$', r'\.jpeg$', r'\.png$', r'\.gif$', r'\.bmp$',
            r'\.mp3$', r'\.mp4$', r'\.avi$', r'\.mov$',
            r'\.css$', r'\.js$', r'\.ico$',
            r'/logout', r'/signout', r'/exit',
            r'/delete', r'/remove', r'/unsubscribe'
        ]
        
        self.logger.info(f"Scope manager initialized for {base_url}")
    
    def is_in_scope(self, url: str) -> bool:
        """
        Check if a URL is within the defined scope.
        
        Args:
            url: URL to check
            
        Returns:
            bool: True if URL is in scope
        """
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # Check if domain is allowed
            if not self._is_domain_allowed(domain):
                return False
            
            # Check excluded patterns
            if self._is_url_excluded(url):
                return False
            
            # Check domain page limit
            if self._is_domain_limit_exceeded(domain):
                return False
            
            # Check if already visited
            if url in self.visited_urls:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking scope for {url}: {e}")
            return False
    
    def _is_domain_allowed(self, domain: str) -> bool:
        """Check if a domain is in the allowed list."""
        try:
            for allowed in self.allowed_domains:
                # Support wildcard domains
                if allowed.startswith('*.'):
                    pattern = allowed[2:]  # Remove *.
                    if domain.endswith(pattern):
                        return True
                elif domain == allowed:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking domain {domain}: {e}")
            return False
    
    def _is_url_excluded(self, url: str) -> bool:
        """Check if URL matches any excluded patterns."""
        try:
            # Check custom excluded patterns
            for pattern in self.excluded_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return True
            
            # Check default excluded patterns
            for pattern in self.default_excluded_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking exclusion for {url}: {e}")
            return False
    
    def _is_domain_limit_exceeded(self, domain: str) -> bool:
        """Check if domain page limit is exceeded."""
        try:
            current_count = self.domain_page_counts.get(domain, 0)
            return current_count >= self.max_pages_per_domain
            
        except Exception as e:
            self.logger.error(f"Error checking domain limit for {domain}: {e}")
            return False
    
    async def is_robots_allowed(self, url: str, user_agent: str = "*") -> bool:
        """
        Check if URL is allowed by robots.txt.
        
        Args:
            url: URL to check
            user_agent: User agent string
            
        Returns:
            bool: True if allowed by robots.txt
        """
        if not self.respect_robots:
            return True
        
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            robots_url = f"{parsed_url.scheme}://{domain}/robots.txt"
            
            # Get robots.txt parser for this domain
            if domain not in self.robots_cache:
                await self._load_robots_txt(domain, robots_url)
            
            robots_parser = self.robots_cache.get(domain)
            if robots_parser:
                return robots_parser.can_fetch(user_agent, url)
            
            # If no robots.txt or error loading, allow by default
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking robots.txt for {url}: {e}")
            return True
    
    async def _load_robots_txt(self, domain: str, robots_url: str) -> None:
        """Load and parse robots.txt for a domain."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(robots_url, timeout=10) as response:
                    if response.status == 200:
                        robots_content = await response.text()
                        
                        # Create and configure robots parser
                        robots_parser = RobotFileParser()
                        robots_parser.set_url(robots_url)
                        
                        # Parse the content
                        robots_lines = robots_content.split('\n')
                        for line in robots_lines:
                            robots_parser.read()
                        
                        self.robots_cache[domain] = robots_parser
                        self.logger.debug(f"Loaded robots.txt for {domain}")
                    else:
                        # No robots.txt found, create permissive parser
                        self.robots_cache[domain] = None
                        
        except Exception as e:
            self.logger.debug(f"Could not load robots.txt for {domain}: {e}")
            self.robots_cache[domain] = None
    
    def mark_visited(self, url: str) -> None:
        """Mark a URL as visited and update domain counts."""
        try:
            self.visited_urls.add(url)
            
            domain = urlparse(url).netloc
            self.domain_page_counts[domain] = self.domain_page_counts.get(domain, 0) + 1
            
        except Exception as e:
            self.logger.error(f"Error marking URL as visited {url}: {e}")
    
    def get_normalized_url(self, url: str, base_url: str) -> str:
        """
        Normalize a URL relative to a base URL.
        
        Args:
            url: URL to normalize
            base_url: Base URL for relative resolution
            
        Returns:
            str: Normalized absolute URL
        """
        try:
            # Handle relative URLs
            if url.startswith('//'):
                # Protocol-relative URL
                base_scheme = urlparse(base_url).scheme
                return f"{base_scheme}:{url}"
            elif url.startswith('/'):
                # Absolute path
                return urljoin(base_url, url)
            elif url.startswith('#'):
                # Fragment only, ignore
                return None
            elif url.startswith('javascript:') or url.startswith('mailto:'):
                # Non-HTTP schemes, ignore
                return None
            elif not url.startswith('http'):
                # Relative path
                return urljoin(base_url, url)
            else:
                # Already absolute
                return url
                
        except Exception as e:
            self.logger.error(f"Error normalizing URL {url}: {e}")
            return None
    
    def filter_urls(self, urls: List[str], base_url: str) -> List[str]:
        """
        Filter a list of URLs based on scope rules.
        
        Args:
            urls: List of URLs to filter
            base_url: Base URL for relative resolution
            
        Returns:
            List[str]: Filtered URLs in scope
        """
        filtered_urls = []
        
        for url in urls:
            try:
                # Normalize URL
                normalized_url = self.get_normalized_url(url, base_url)
                
                if normalized_url and self.is_in_scope(normalized_url):
                    filtered_urls.append(normalized_url)
                    
            except Exception as e:
                self.logger.debug(f"Error filtering URL {url}: {e}")
        
        return filtered_urls
    
    def get_scope_statistics(self) -> Dict[str, Any]:
        """Get scope management statistics."""
        return {
            "base_domain": self.base_domain,
            "allowed_domains": self.allowed_domains,
            "visited_urls_count": len(self.visited_urls),
            "domain_page_counts": dict(self.domain_page_counts),
            "robots_cache_domains": list(self.robots_cache.keys()),
            "max_depth": self.max_depth,
            "max_pages_per_domain": self.max_pages_per_domain,
            "respect_robots": self.respect_robots
        }
    
    def add_allowed_domain(self, domain: str) -> None:
        """Add a domain to the allowed list."""
        if domain not in self.allowed_domains:
            self.allowed_domains.append(domain)
            self.logger.info(f"Added allowed domain: {domain}")
    
    def add_excluded_pattern(self, pattern: str) -> None:
        """Add a URL pattern to the excluded list."""
        if pattern not in self.excluded_patterns:
            self.excluded_patterns.append(pattern)
            self.logger.info(f"Added excluded pattern: {pattern}")
    
    def reset_visited_urls(self) -> None:
        """Reset the visited URLs tracking."""
        self.visited_urls.clear()
        self.domain_page_counts.clear()
        self.logger.info("Reset visited URLs tracking")
