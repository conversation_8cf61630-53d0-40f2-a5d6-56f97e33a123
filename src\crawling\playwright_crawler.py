"""
Playwright-based web crawler for Cipher-Spy.

Provides autonomous navigation, intelligent interaction with page elements,
and comprehensive data extraction using Playwright browser automation.
"""

import asyncio
import json
import re
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from urllib.parse import urljoin, urlparse
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, Locator
from playwright.async_api import TimeoutError as PlaywrightTimeoutError

from ..core.exceptions import CrawlingException
from ..utils.logging import get_logger


class PlaywrightCrawler:
    """
    Autonomous web crawler using Playwright for browser automation.

    Features:
    - Intelligent DOM navigation and interaction
    - Form filling and submission
    - Modal and popup handling
    - Screenshot capture
    - Network traffic monitoring integration
    """

    def __init__(
        self,
        network_interceptor=None,
        form_handler=None,
        scope_manager=None,
        user_agent: str = "CipherSpy/1.0",
        delay_ms: int = 1000,
        headless: bool = True,
        viewport_size: Dict[str, int] = None
    ):
        """
        Initialize Playwright crawler.

        Args:
            network_interceptor: Network traffic interceptor
            form_handler: Form interaction handler
            scope_manager: URL scope manager
            user_agent: User agent string
            delay_ms: Delay between actions in milliseconds
            headless: Whether to run browser in headless mode
            viewport_size: Browser viewport dimensions
        """
        self.network_interceptor = network_interceptor
        self.form_handler = form_handler
        self.scope_manager = scope_manager
        self.user_agent = user_agent
        self.delay_ms = delay_ms
        self.headless = headless
        self.viewport_size = viewport_size or {"width": 1920, "height": 1080}

        self.logger = get_logger(__name__)

        # Playwright instances
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        # Crawling state
        self.current_url: Optional[str] = None
        self.discovered_urls: Set[str] = set()
        self.interaction_count = 0

        # Element selectors for intelligent interaction
        self.interactive_selectors = [
            'button:not([disabled])',
            'a[href]:not([href="#"]):not([href="javascript:void(0)"])',
            'input[type="submit"]:not([disabled])',
            'input[type="button"]:not([disabled])',
            '[role="button"]:not([disabled])',
            '[onclick]:not([disabled])',
            'select:not([disabled])',
            'input[type="checkbox"]:not([disabled])',
            'input[type="radio"]:not([disabled])',
            '.btn:not([disabled])',
            '.button:not([disabled])'
        ]

        self.form_selectors = [
            'form',
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="search"]',
            'textarea',
            'select'
        ]

    async def start(self) -> None:
        """Start the Playwright browser and context."""
        try:
            self.playwright = await async_playwright().start()

            # Launch browser with minimal args for better compatibility
            browser_args = ['--no-sandbox']

            # Add additional args only if needed
            if self.headless:
                browser_args.extend([
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security'
                ])

            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args
            )

            # Create context
            self.context = await self.browser.new_context(
                user_agent=self.user_agent,
                viewport=self.viewport_size,
                ignore_https_errors=True,
                java_script_enabled=True
            )

            # Create page
            self.page = await self.context.new_page()

            # Setup network interception if available
            if self.network_interceptor:
                await self.network_interceptor.setup_page(self.page)

            self.logger.info("Playwright crawler started successfully")

        except Exception as e:
            raise CrawlingException(f"Failed to start Playwright crawler: {e}")

    async def stop(self) -> None:
        """Stop the Playwright browser and cleanup."""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            self.logger.info("Playwright crawler stopped")

        except Exception as e:
            self.logger.error(f"Error stopping Playwright crawler: {e}")

    async def crawl_page(self, url: str):
        """
        Crawl a single page with autonomous interaction.

        Args:
            url: URL to crawl

        Returns:
            Dict containing page data and discovered information
        """
        try:
            self.current_url = url
            self.logger.info(f"Crawling page: {url}")

            # Navigate to page
            response = await self.page.goto(url, wait_until="networkidle", timeout=30000)

            if not response:
                raise CrawlingException(f"Failed to load page: {url}")

            # Wait for page to stabilize
            await asyncio.sleep(self.delay_ms / 1000)

            # Extract basic page information
            page_data = await self._extract_page_data()

            # Discover links
            discovered_urls = await self._discover_links()

            # Perform intelligent interactions
            interactions_count = await self._perform_interactions()

            # Handle forms if present
            await self._handle_forms()

            # Take screenshot
            screenshot_path = await self._take_screenshot(url)

            # Create a simple result object
            from ..models.schemas import PageInfo
            from datetime import datetime

            result = PageInfo(
                url=url,
                title=page_data.get("title", ""),
                status_code=response.status,
                content_type=response.headers.get("content-type", ""),
                screenshot_path=screenshot_path,
                discovered_at=datetime.utcnow()
            )

            return result

        except PlaywrightTimeoutError:
            self.logger.warning(f"Timeout crawling page: {url}")
            return None
        except Exception as e:
            self.logger.error(f"Error crawling page {url}: {e}")
            return None

    async def _extract_page_data(self) -> Dict[str, Any]:
        """Extract basic information from the current page."""
        try:
            # Get page title
            title = await self.page.title()

            # Get page URL (may have changed due to redirects)
            current_url = self.page.url

            # Get meta information
            meta_description = await self.page.get_attribute('meta[name="description"]', 'content') or ""

            # Count various elements
            link_count = await self.page.locator('a[href]').count()
            form_count = await self.page.locator('form').count()
            button_count = await self.page.locator('button, input[type="button"], input[type="submit"]').count()

            # Check for JavaScript frameworks
            frameworks = await self._detect_frameworks()

            return {
                "title": title,
                "url": current_url,
                "meta_description": meta_description,
                "link_count": link_count,
                "form_count": form_count,
                "button_count": button_count,
                "frameworks": frameworks
            }

        except Exception as e:
            self.logger.error(f"Error extracting page data: {e}")
            return {}

    async def _discover_links(self) -> Set[str]:
        """Discover all links on the current page."""
        discovered = set()

        try:
            # Get all links
            links = await self.page.locator('a[href]').all()

            for link in links:
                try:
                    href = await link.get_attribute('href')
                    if href:
                        # Convert relative URLs to absolute
                        absolute_url = urljoin(self.current_url, href)

                        # Check scope if scope manager is available
                        if not self.scope_manager or self.scope_manager.is_in_scope(absolute_url):
                            discovered.add(absolute_url)

                except Exception as e:
                    self.logger.debug(f"Error processing link: {e}")

            self.logger.debug(f"Discovered {len(discovered)} links on {self.current_url}")
            return discovered

        except Exception as e:
            self.logger.error(f"Error discovering links: {e}")
            return set()

    async def _perform_interactions(self) -> int:
        """Perform intelligent interactions with page elements."""
        interactions = 0

        try:
            # Find interactive elements
            for selector in self.interactive_selectors:
                try:
                    elements = await self.page.locator(selector).all()

                    for element in elements[:5]:  # Limit interactions per selector
                        try:
                            # Check if element is visible and enabled
                            if await element.is_visible() and await element.is_enabled():
                                # Get element info for logging
                                tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                                text_content = (await element.text_content() or "").strip()[:50]

                                self.logger.debug(f"Interacting with {tag_name}: {text_content}")

                                # Perform interaction based on element type
                                if tag_name == 'button' or 'button' in selector:
                                    await self._safe_click(element)
                                elif tag_name == 'a':
                                    # For links, just hover to trigger any hover effects
                                    await element.hover()
                                elif tag_name == 'select':
                                    await self._interact_with_select(element)
                                elif 'checkbox' in selector or 'radio' in selector:
                                    await self._safe_click(element)

                                interactions += 1

                                # Wait between interactions
                                await asyncio.sleep(0.5)

                        except Exception as e:
                            self.logger.debug(f"Error interacting with element: {e}")

                except Exception as e:
                    self.logger.debug(f"Error with selector {selector}: {e}")

            self.logger.debug(f"Performed {interactions} interactions on {self.current_url}")
            return interactions

        except Exception as e:
            self.logger.error(f"Error performing interactions: {e}")
            return 0

    async def _safe_click(self, element: Locator) -> None:
        """Safely click an element with error handling."""
        try:
            # Scroll element into view
            await element.scroll_into_view_if_needed()

            # Wait for element to be stable
            await element.wait_for(state="visible", timeout=5000)

            # Click the element
            await element.click(timeout=5000)

            # Wait for any resulting navigation or changes
            await asyncio.sleep(1)

        except Exception as e:
            self.logger.debug(f"Safe click failed: {e}")

    async def _interact_with_select(self, select_element: Locator) -> None:
        """Interact with select dropdown elements."""
        try:
            # Get all options
            options = await select_element.locator('option').all()

            if len(options) > 1:
                # Select the second option (skip first which is often default)
                await select_element.select_option(index=1)
                await asyncio.sleep(0.5)

        except Exception as e:
            self.logger.debug(f"Error interacting with select: {e}")

    async def _handle_forms(self) -> None:
        """Handle form interactions if form handler is available."""
        try:
            if self.form_handler:
                forms = await self.page.locator('form').all()
                for form in forms:
                    await self.form_handler.handle_form(form, self.page)

        except Exception as e:
            self.logger.error(f"Error handling forms: {e}")

    async def _detect_frameworks(self) -> List[str]:
        """Detect JavaScript frameworks and libraries on the page."""
        frameworks = []

        try:
            # Check for common frameworks
            framework_checks = {
                'React': 'window.React || document.querySelector("[data-reactroot]")',
                'Vue': 'window.Vue || document.querySelector("[data-v-]")',
                'Angular': 'window.angular || document.querySelector("[ng-app], [ng-controller]")',
                'jQuery': 'window.jQuery || window.$',
                'Bootstrap': 'document.querySelector(".container, .row, .col-")',
                'Tailwind': 'document.querySelector("[class*=\\"w-\\"], [class*=\\"h-\\"]")'
            }

            for framework, check in framework_checks.items():
                try:
                    result = await self.page.evaluate(f'!!({check})')
                    if result:
                        frameworks.append(framework)
                except:
                    pass

            return frameworks

        except Exception as e:
            self.logger.error(f"Error detecting frameworks: {e}")
            return []

    async def _take_screenshot(self, url: str) -> Optional[str]:
        """Take a screenshot of the current page."""
        try:
            # Create screenshots directory
            screenshots_dir = Path("data/screenshots")
            screenshots_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename from URL
            parsed_url = urlparse(url)
            filename = f"{parsed_url.netloc}_{parsed_url.path.replace('/', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            screenshot_path = screenshots_dir / filename

            # Take screenshot
            await self.page.screenshot(path=str(screenshot_path), full_page=True)

            return str(screenshot_path)

        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            return None
