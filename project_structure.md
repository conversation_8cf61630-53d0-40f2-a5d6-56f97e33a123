# Cipher-Spy Project Structure

```
cipher-spy/
├── README.md
├── docker-compose.yml
├── Dockerfile
├── requirements.txt
├── .env.example
├── .gitignore
├── pyproject.toml
│
├── src/
│   ├── __init__.py
│   ├── main.py                     # FastAPI application entry point
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py             # Configuration management
│   │   └── database.py             # Database connection setup
│   │
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── base_agent.py           # Base agent class
│   │   ├── orchestrator.py         # Main orchestrator agent
│   │   ├── crawler.py              # Web crawling agent
│   │   ├── fingerprinter.py        # Technology fingerprinting
│   │   ├── graph_rag.py            # Knowledge graph queries
│   │   ├── exploit_planner.py      # Exploit plan generation
│   │   └── executor.py             # Exploit execution agent
│   │
│   ├── core/
│   │   ├── __init__.py
│   │   ├── workflow.py             # LangGraph workflow definition
│   │   ├── state.py                # Shared state management
│   │   └── exceptions.py           # Custom exceptions
│   │
│   ├── crawling/
│   │   ├── __init__.py
│   │   ├── playwright_crawler.py   # Playwright implementation
│   │   ├── network_interceptor.py  # HTTP traffic capture
│   │   ├── form_handler.py         # Form interaction logic
│   │   └── scope_manager.py        # Crawling scope enforcement
│   │
│   ├── fingerprinting/
│   │   ├── __init__.py
│   │   ├── wappalyzer_scanner.py   # Wappalyzer integration
│   │   ├── wafw00f_scanner.py      # WAF detection
│   │   ├── header_analyzer.py      # HTTP header analysis
│   │   └── tech_detector.py        # Combined tech detection
│   │
│   ├── knowledge/
│   │   ├── __init__.py
│   │   ├── graph_manager.py        # Graphiti knowledge graph
│   │   ├── exploit_db.py           # Exploit-DB integration
│   │   ├── vector_search.py        # Semantic search
│   │   └── cve_matcher.py          # CVE matching logic
│   │
│   ├── exploitation/
│   │   ├── __init__.py
│   │   ├── plan_generator.py       # LLM-based plan generation
│   │   ├── payload_builder.py      # Exploit payload construction
│   │   ├── executor_engine.py      # Safe execution engine
│   │   └── approval_handler.py     # Human approval workflow
│   │
│   ├── models/
│   │   ├── __init__.py
│   │   ├── database.py             # SQLAlchemy models
│   │   ├── schemas.py              # Pydantic schemas
│   │   └── graph_models.py         # Neo4j/Graphiti models
│   │
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── scans.py            # Scan management endpoints
│   │   │   ├── targets.py          # Target management
│   │   │   ├── findings.py         # Results and findings
│   │   │   └── exploits.py         # Exploit management
│   │   └── dependencies.py         # FastAPI dependencies
│   │
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── openrouter_client.py    # OpenRouter integration
│   │   ├── local_llm.py            # Local LLM support
│   │   ├── prompt_templates.py     # Prompt engineering
│   │   └── response_parser.py      # LLM response parsing
│   │
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── postgres_manager.py     # PostgreSQL operations
│   │   ├── neo4j_manager.py        # Neo4j operations
│   │   └── file_storage.py         # File system storage
│   │
│   ├── reporting/
│   │   ├── __init__.py
│   │   ├── report_generator.py     # Report compilation
│   │   ├── templates/              # Report templates
│   │   └── exporters.py            # Export formats (JSON, PDF, etc.)
│   │
│   └── utils/
│       ├── __init__.py
│       ├── logging.py              # Logging configuration
│       ├── security.py             # Security utilities
│       ├── validators.py           # Input validation
│       └── helpers.py              # General utilities
│
├── tests/
│   ├── __init__.py
│   ├── conftest.py                 # Pytest configuration
│   ├── unit/
│   │   ├── test_agents/
│   │   ├── test_crawling/
│   │   ├── test_fingerprinting/
│   │   └── test_exploitation/
│   ├── integration/
│   │   ├── test_workflow.py
│   │   └── test_api.py
│   └── fixtures/
│       ├── sample_targets.py
│       └── mock_responses.py
│
├── scripts/
│   ├── setup_db.py                 # Database initialization
│   ├── load_exploitdb.py           # Exploit-DB data loading
│   ├── update_knowledge.py         # Knowledge base updates
│   └── dev_server.py               # Development server
│
├── data/
│   ├── exploitdb/                  # Exploit-DB repository
│   ├── fingerprints/               # Custom fingerprint rules
│   └── payloads/                   # Exploit payload templates
│
├── docs/
│   ├── installation.md
│   ├── configuration.md
│   ├── api_reference.md
│   ├── agent_architecture.md
│   └── security_considerations.md
│
├── frontend/                       # Optional web UI
│   ├── package.json
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── services/
│   └── public/
│
└── docker/
    ├── app.dockerfile
    ├── postgres.dockerfile
    └── neo4j.dockerfile
```

## Key Design Principles

1. **Modular Architecture**: Each component is self-contained and testable
2. **Agent Separation**: Clear boundaries between different agent responsibilities
3. **Data Layer Abstraction**: Separate managers for different storage systems
4. **Configuration Management**: Centralized settings with environment overrides
5. **Security First**: Isolated execution environments and approval workflows
6. **Extensibility**: Plugin-like structure for adding new fingerprinting tools
7. **Testing Support**: Comprehensive test structure with fixtures and mocks
