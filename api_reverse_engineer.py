#!/usr/bin/env python3
"""
API Reverse Engineering Suite

Analyzes discovered pump.fun APIs and creates replayable test harnesses
for making our own API calls.
"""

import asyncio
import json
import requests
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse, parse_qs
import re

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor
from src.utils.logging import setup_logging


class PumpFunAPIReverseEngineer:
    """
    Reverse engineer pump.fun APIs and create replayable test harnesses.
    """
    
    def __init__(self):
        self.discovered_apis = []
        self.api_categories = {
            'core_apis': [],
            'token_apis': [],
            'image_apis': [],
            'analytics_apis': [],
            'auth_apis': [],
            'blockchain_apis': []
        }
        self.headers_template = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
    
    async def discover_and_analyze_apis(self) -> Dict[str, Any]:
        """Discover APIs and analyze them for reverse engineering."""
        print("🔬 Pump.fun API Reverse Engineering")
        print("="*50)
        
        # Setup logging
        setup_logging(level="INFO", environment="development")
        
        # Discover APIs first
        print("1. 🕷️  Discovering APIs through autonomous navigation...")
        discovered_apis = await self._discover_apis()
        
        print(f"2. 🔍 Analyzing {len(discovered_apis)} discovered APIs...")
        analysis = self._analyze_apis(discovered_apis)
        
        print("3. 🧪 Creating test harnesses...")
        test_harnesses = self._create_test_harnesses(analysis)
        
        print("4. 🚀 Testing API calls...")
        test_results = await self._test_api_calls(test_harnesses)
        
        # Generate comprehensive report
        report = {
            'discovery_timestamp': datetime.now().isoformat(),
            'total_apis_discovered': len(discovered_apis),
            'api_analysis': analysis,
            'test_harnesses': test_harnesses,
            'test_results': test_results,
            'reverse_engineering_summary': self._generate_summary(analysis, test_results)
        }
        
        # Save results
        await self._save_reverse_engineering_results(report)
        
        return report
    
    async def _discover_apis(self) -> List[Dict[str, Any]]:
        """Discover APIs through network interception."""
        interceptor = NetworkInterceptor()
        crawler = PlaywrightCrawler(headless=True, delay_ms=1000)
        
        try:
            await crawler.start()
            page = crawler.page
            await interceptor.setup_page(page)
            
            # Navigate to key pump.fun pages
            pages_to_visit = [
                "https://pump.fun",
                "https://pump.fun/board",
                # We'll add more as we discover them
            ]
            
            for url in pages_to_visit:
                try:
                    print(f"   📄 Visiting {url}...")
                    await page.goto(url, wait_until="domcontentloaded", timeout=15000)
                    await asyncio.sleep(3)  # Wait for API calls
                except Exception as e:
                    print(f"   ⚠️  {url} timed out: {e}")
                    continue
            
            # Get discovered endpoints
            endpoints = interceptor.get_discovered_endpoints()
            print(f"   ✅ Discovered {len(endpoints)} API endpoints")
            
            return endpoints
            
        except Exception as e:
            print(f"   💥 Discovery error: {e}")
            return []
        
        finally:
            try:
                await crawler.stop()
            except:
                pass
    
    def _analyze_apis(self, apis: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze discovered APIs and categorize them."""
        analysis = {
            'total_apis': len(apis),
            'categorized_apis': {},
            'api_patterns': {},
            'authentication_analysis': {},
            'parameter_analysis': {},
            'reverse_engineering_opportunities': []
        }
        
        # Categorize APIs
        for api in apis:
            url = api.get('url', '')
            method = api.get('method', 'GET')
            
            category = self._categorize_api(url, method)
            if category not in analysis['categorized_apis']:
                analysis['categorized_apis'][category] = []
            
            analysis['categorized_apis'][category].append({
                'method': method,
                'url': url,
                'headers': api.get('headers', {}),
                'parameters': api.get('parameters', {}),
                'reverse_engineering_potential': self._assess_reverse_engineering_potential(api)
            })
        
        # Analyze patterns
        analysis['api_patterns'] = self._analyze_api_patterns(apis)
        
        # Analyze authentication
        analysis['authentication_analysis'] = self._analyze_authentication(apis)
        
        # Find reverse engineering opportunities
        analysis['reverse_engineering_opportunities'] = self._find_reverse_engineering_opportunities(apis)
        
        return analysis
    
    def _categorize_api(self, url: str, method: str) -> str:
        """Categorize an API endpoint."""
        url_lower = url.lower()
        
        # Core pump.fun APIs
        if 'frontend-api-v3.pump.fun' in url_lower:
            if 'coins' in url_lower:
                return 'token_apis'
            return 'core_apis'
        
        # Blockchain/RPC APIs
        if 'helius-rpc.com' in url_lower or 'rpc' in url_lower:
            return 'blockchain_apis'
        
        # Authentication APIs
        if any(auth_term in url_lower for auth_term in ['auth', 'login', 'privy']):
            return 'auth_apis'
        
        # Image/IPFS APIs
        if 'ipfs' in url_lower or 'pinata' in url_lower:
            return 'image_apis'
        
        # Analytics APIs
        if any(analytics_term in url_lower for analytics_term in ['analytics', 'gtm', 'datadog', 'fullstory']):
            return 'analytics_apis'
        
        # Main pump.fun pages
        if 'pump.fun' in url_lower and not any(term in url_lower for term in ['api', 'static', '_next']):
            return 'core_apis'
        
        return 'other'
    
    def _assess_reverse_engineering_potential(self, api: Dict[str, Any]) -> str:
        """Assess how easy it would be to reverse engineer this API."""
        url = api.get('url', '')
        method = api.get('method', 'GET')
        headers = api.get('headers', {})
        
        # High potential: Public APIs with clear patterns
        if 'frontend-api-v3.pump.fun' in url:
            return 'HIGH'
        
        # Medium potential: APIs that might need auth but are discoverable
        if method == 'GET' and not any(auth_header in headers for auth_header in ['authorization', 'x-api-key']):
            return 'MEDIUM'
        
        # Low potential: Complex auth or internal APIs
        if any(term in url.lower() for term in ['auth', 'internal', 'admin']):
            return 'LOW'
        
        return 'MEDIUM'
    
    def _analyze_api_patterns(self, apis: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns in the APIs."""
        patterns = {
            'base_urls': set(),
            'common_parameters': {},
            'http_methods': set(),
            'content_types': set()
        }
        
        for api in apis:
            url = api.get('url', '')
            method = api.get('method', 'GET')
            headers = api.get('headers', {})
            
            # Extract base URL
            parsed = urlparse(url)
            base_url = f"{parsed.scheme}://{parsed.netloc}"
            patterns['base_urls'].add(base_url)
            
            # Track methods
            patterns['http_methods'].add(method)
            
            # Track content types
            content_type = headers.get('content-type', headers.get('Content-Type', ''))
            if content_type:
                patterns['content_types'].add(content_type)
            
            # Analyze query parameters
            if parsed.query:
                params = parse_qs(parsed.query)
                for param_name in params.keys():
                    patterns['common_parameters'][param_name] = patterns['common_parameters'].get(param_name, 0) + 1
        
        # Convert sets to lists for JSON serialization
        patterns['base_urls'] = list(patterns['base_urls'])
        patterns['http_methods'] = list(patterns['http_methods'])
        patterns['content_types'] = list(patterns['content_types'])
        
        return patterns
    
    def _analyze_authentication(self, apis: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze authentication patterns."""
        auth_analysis = {
            'auth_required_apis': [],
            'public_apis': [],
            'auth_headers_found': set(),
            'auth_patterns': []
        }
        
        for api in apis:
            headers = api.get('headers', {})
            url = api.get('url', '')
            
            # Check for auth headers
            auth_headers = ['authorization', 'x-api-key', 'x-auth-token', 'cookie']
            has_auth = any(header.lower() in [h.lower() for h in headers.keys()] for header in auth_headers)
            
            if has_auth:
                auth_analysis['auth_required_apis'].append(api)
                for header in headers.keys():
                    if any(auth_term in header.lower() for auth_term in ['auth', 'token', 'key']):
                        auth_analysis['auth_headers_found'].add(header)
            else:
                auth_analysis['public_apis'].append(api)
        
        auth_analysis['auth_headers_found'] = list(auth_analysis['auth_headers_found'])
        
        return auth_analysis
    
    def _find_reverse_engineering_opportunities(self, apis: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find the best APIs to reverse engineer."""
        opportunities = []
        
        for api in apis:
            url = api.get('url', '')
            method = api.get('method', 'GET')
            
            # Look for high-value APIs
            if 'frontend-api-v3.pump.fun' in url:
                if 'coins' in url:
                    opportunities.append({
                        'api': api,
                        'opportunity_type': 'Token Data API',
                        'description': 'Get token information, trending coins, market data',
                        'reverse_engineering_difficulty': 'Easy',
                        'business_value': 'High'
                    })
                elif 'search' in url:
                    opportunities.append({
                        'api': api,
                        'opportunity_type': 'Search API',
                        'description': 'Search for tokens and projects',
                        'reverse_engineering_difficulty': 'Easy',
                        'business_value': 'Medium'
                    })
            
            elif 'helius-rpc.com' in url:
                opportunities.append({
                    'api': api,
                    'opportunity_type': 'Blockchain RPC',
                    'description': 'Direct Solana blockchain interactions',
                    'reverse_engineering_difficulty': 'Medium',
                    'business_value': 'High'
                })
        
        return opportunities
    
    def _create_test_harnesses(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create test harnesses for the most promising APIs."""
        harnesses = {}
        
        # Focus on high-value APIs
        categorized_apis = analysis.get('categorized_apis', {})
        
        # Token APIs
        if 'token_apis' in categorized_apis:
            harnesses['token_apis'] = self._create_token_api_harnesses(categorized_apis['token_apis'])
        
        # Core APIs
        if 'core_apis' in categorized_apis:
            harnesses['core_apis'] = self._create_core_api_harnesses(categorized_apis['core_apis'])
        
        # Blockchain APIs
        if 'blockchain_apis' in categorized_apis:
            harnesses['blockchain_apis'] = self._create_blockchain_api_harnesses(categorized_apis['blockchain_apis'])
        
        return harnesses
    
    def _create_token_api_harnesses(self, token_apis: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test harnesses for token APIs."""
        harnesses = []
        
        for api in token_apis:
            url = api['url']
            method = api['method']
            
            if 'coins' in url and method == 'GET':
                # Parse the original URL to understand parameters
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                
                harness = {
                    'name': 'Get Coins Data',
                    'description': 'Retrieve coin/token information from pump.fun',
                    'method': method,
                    'base_url': f"{parsed.scheme}://{parsed.netloc}{parsed.path}",
                    'parameters': {
                        'offset': params.get('offset', ['0'])[0] if 'offset' in params else '0',
                        'limit': params.get('limit', ['50'])[0] if 'limit' in params else '50',
                        'sort': params.get('sort', ['market_cap'])[0] if 'sort' in params else 'market_cap',
                        'order': params.get('order', ['DESC'])[0] if 'order' in params else 'DESC',
                        'includeNsfw': params.get('includeNsfw', ['false'])[0] if 'includeNsfw' in params else 'false'
                    },
                    'headers': self.headers_template.copy(),
                    'example_call': self._generate_example_call(method, url, self.headers_template),
                    'curl_command': self._generate_curl_command(method, url, self.headers_template)
                }
                
                harnesses.append(harness)
        
        return harnesses
    
    def _create_core_api_harnesses(self, core_apis: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test harnesses for core APIs."""
        harnesses = []
        
        for api in core_apis:
            url = api['url']
            method = api['method']
            
            if method == 'GET' and 'pump.fun' in url:
                harness = {
                    'name': f'Access {url}',
                    'description': f'Access pump.fun page: {url}',
                    'method': method,
                    'url': url,
                    'headers': self.headers_template.copy(),
                    'example_call': self._generate_example_call(method, url, self.headers_template),
                    'curl_command': self._generate_curl_command(method, url, self.headers_template)
                }
                
                harnesses.append(harness)
        
        return harnesses
    
    def _create_blockchain_api_harnesses(self, blockchain_apis: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test harnesses for blockchain APIs."""
        harnesses = []
        
        for api in blockchain_apis:
            url = api['url']
            method = api['method']
            
            if 'helius-rpc.com' in url:
                harness = {
                    'name': 'Solana RPC Call',
                    'description': 'Make RPC calls to Solana blockchain via Helius',
                    'method': method,
                    'url': url,
                    'headers': {
                        **self.headers_template,
                        'Content-Type': 'application/json'
                    },
                    'example_payload': {
                        'jsonrpc': '2.0',
                        'id': 1,
                        'method': 'getAccountInfo',
                        'params': ['********************************']  # System program
                    },
                    'example_call': self._generate_rpc_example_call(url),
                    'curl_command': self._generate_rpc_curl_command(url)
                }
                
                harnesses.append(harness)
        
        return harnesses
    
    def _generate_example_call(self, method: str, url: str, headers: Dict[str, str]) -> str:
        """Generate Python example call."""
        return f"""
import requests

response = requests.{method.lower()}(
    '{url}',
    headers={json.dumps(headers, indent=4)}
)

print(f"Status: {{response.status_code}}")
print(f"Response: {{response.text[:500]}}")
"""
    
    def _generate_curl_command(self, method: str, url: str, headers: Dict[str, str]) -> str:
        """Generate cURL command."""
        header_args = ' '.join([f"-H '{k}: {v}'" for k, v in headers.items()])
        return f"curl -X {method} {header_args} '{url}'"
    
    def _generate_rpc_example_call(self, url: str) -> str:
        """Generate RPC example call."""
        return f"""
import requests
import json

payload = {{
    'jsonrpc': '2.0',
    'id': 1,
    'method': 'getAccountInfo',
    'params': ['********************************']
}}

response = requests.post(
    '{url}',
    headers={json.dumps({**self.headers_template, 'Content-Type': 'application/json'}, indent=4)},
    json=payload
)

print(f"Status: {{response.status_code}}")
print(f"Response: {{response.json()}}")
"""
    
    def _generate_rpc_curl_command(self, url: str) -> str:
        """Generate RPC cURL command."""
        return f"""curl -X POST '{url}' \\
  -H 'Content-Type: application/json' \\
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' \\
  -d '{{"jsonrpc":"2.0","id":1,"method":"getAccountInfo","params":["********************************"]}}'"""
    
    async def _test_api_calls(self, harnesses: Dict[str, Any]) -> Dict[str, Any]:
        """Test the API calls to see which ones work."""
        results = {}
        
        for category, category_harnesses in harnesses.items():
            results[category] = []
            
            for harness in category_harnesses:
                print(f"   🧪 Testing {harness['name']}...")
                
                try:
                    # Test the API call
                    if harness['method'] == 'GET':
                        response = requests.get(
                            harness.get('url', harness.get('base_url', '')),
                            headers=harness['headers'],
                            timeout=10
                        )
                    elif harness['method'] == 'POST':
                        response = requests.post(
                            harness['url'],
                            headers=harness['headers'],
                            json=harness.get('example_payload', {}),
                            timeout=10
                        )
                    else:
                        continue
                    
                    test_result = {
                        'harness_name': harness['name'],
                        'status_code': response.status_code,
                        'success': 200 <= response.status_code < 300,
                        'response_size': len(response.content),
                        'content_type': response.headers.get('content-type', ''),
                        'response_preview': response.text[:200] if response.text else '',
                        'headers_received': dict(response.headers)
                    }
                    
                    if test_result['success']:
                        print(f"      ✅ Success: {response.status_code}")
                    else:
                        print(f"      ❌ Failed: {response.status_code}")
                    
                    results[category].append(test_result)
                    
                except Exception as e:
                    print(f"      💥 Error: {e}")
                    results[category].append({
                        'harness_name': harness['name'],
                        'error': str(e),
                        'success': False
                    })
        
        return results
    
    def _generate_summary(self, analysis: Dict[str, Any], test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate reverse engineering summary."""
        total_apis = analysis['total_apis']
        opportunities = analysis['reverse_engineering_opportunities']
        
        # Count successful tests
        successful_tests = 0
        total_tests = 0
        
        for category_results in test_results.values():
            for result in category_results:
                total_tests += 1
                if result.get('success', False):
                    successful_tests += 1
        
        return {
            'total_apis_discovered': total_apis,
            'reverse_engineering_opportunities': len(opportunities),
            'test_harnesses_created': sum(len(harnesses) for harnesses in test_results.values()),
            'successful_api_calls': successful_tests,
            'success_rate': f"{(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
            'high_value_apis': [opp for opp in opportunities if opp.get('business_value') == 'High'],
            'recommendations': [
                "Focus on frontend-api-v3.pump.fun endpoints for token data",
                "Helius RPC endpoints provide direct blockchain access",
                "Most GET endpoints don't require authentication",
                "Rate limiting may apply - implement delays between calls"
            ]
        }
    
    async def _save_reverse_engineering_results(self, report: Dict[str, Any]):
        """Save reverse engineering results."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save main report
        report_file = f"pump_fun_reverse_engineering_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n💾 Reverse engineering report saved: {report_file}")
        
        # Generate executable test scripts
        await self._generate_test_scripts(report, timestamp)
    
    async def _generate_test_scripts(self, report: Dict[str, Any], timestamp: str):
        """Generate executable test scripts."""
        harnesses = report.get('test_harnesses', {})
        
        # Generate Python test script
        python_script = self._generate_python_test_script(harnesses)
        python_file = f"pump_fun_api_tests_{timestamp}.py"
        with open(python_file, 'w') as f:
            f.write(python_script)
        
        print(f"🐍 Python test script saved: {python_file}")
        
        # Generate bash script with cURL commands
        bash_script = self._generate_bash_test_script(harnesses)
        bash_file = f"pump_fun_api_tests_{timestamp}.sh"
        with open(bash_file, 'w') as f:
            f.write(bash_script)
        
        print(f"🔧 Bash test script saved: {bash_file}")
    
    def _generate_python_test_script(self, harnesses: Dict[str, Any]) -> str:
        """Generate Python test script."""
        script = '''#!/usr/bin/env python3
"""
Pump.fun API Test Script
Auto-generated reverse engineering test harnesses
"""

import requests
import json
import time

def test_pump_fun_apis():
    """Test discovered pump.fun APIs."""
    print("🧪 Testing Pump.fun APIs")
    print("="*40)
    
'''
        
        for category, category_harnesses in harnesses.items():
            script += f'''
    print(f"\\n📂 Testing {category.replace('_', ' ').title()}")
    print("-" * 30)
'''
            
            for harness in category_harnesses:
                script += f'''
    # Test: {harness['name']}
    try:
        print(f"🔍 {harness['name']}...")
        response = requests.{harness['method'].lower()}(
            '{harness.get('url', harness.get('base_url', ''))}',
            headers={json.dumps(harness['headers'], indent=12)},
            timeout=10
        )
        
        print(f"   Status: {{response.status_code}}")
        if 200 <= response.status_code < 300:
            print(f"   ✅ Success! Response size: {{len(response.content)}} bytes")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    data = response.json()
                    print(f"   📊 JSON keys: {{list(data.keys()) if isinstance(data, dict) else 'Array with ' + str(len(data)) + ' items'}}")
                except:
                    pass
        else:
            print(f"   ❌ Failed: {{response.status_code}}")
        
        time.sleep(1)  # Rate limiting
        
    except Exception as e:
        print(f"   💥 Error: {{e}}")
'''
        
        script += '''

if __name__ == "__main__":
    test_pump_fun_apis()
'''
        
        return script
    
    def _generate_bash_test_script(self, harnesses: Dict[str, Any]) -> str:
        """Generate bash test script."""
        script = '''#!/bin/bash
# Pump.fun API Test Script
# Auto-generated reverse engineering test harnesses

echo "🧪 Testing Pump.fun APIs"
echo "========================================"

'''
        
        for category, category_harnesses in harnesses.items():
            script += f'''
echo ""
echo "📂 Testing {category.replace('_', ' ').title()}"
echo "------------------------------"
'''
            
            for harness in category_harnesses:
                script += f'''
echo "🔍 {harness['name']}..."
{harness.get('curl_command', '')}
echo ""
sleep 1
'''
        
        return script


async def main():
    """Main entry point."""
    print("🔬 Pump.fun API Reverse Engineering Suite")
    print("Discover, analyze, and create test harnesses for pump.fun APIs")
    print("="*70)
    
    engineer = PumpFunAPIReverseEngineer()
    
    try:
        report = await engineer.discover_and_analyze_apis()
        
        print(f"\n🎉 Reverse Engineering Complete!")
        print(f"📊 Summary:")
        summary = report['reverse_engineering_summary']
        print(f"   • APIs Discovered: {summary['total_apis_discovered']}")
        print(f"   • Test Harnesses: {summary['test_harnesses_created']}")
        print(f"   • Successful Calls: {summary['successful_api_calls']}")
        print(f"   • Success Rate: {summary['success_rate']}")
        print(f"   • High-Value APIs: {len(summary['high_value_apis'])}")
        
        print(f"\n🎯 Key Findings:")
        for rec in summary['recommendations']:
            print(f"   • {rec}")
        
        return 0
        
    except Exception as e:
        print(f"\n💥 Reverse engineering failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
