#!/usr/bin/env python3
"""
Enhanced Pump.fun API Discovery Demo

Comprehensive autonomous exploration of pump.fun with systematic navigation
through all sections to discover maximum API endpoints.
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.agents.api_discovery_agent import APIDiscoveryAgent
from src.utils.logging import setup_logging


class EnhancedPumpFunDemo:
    """Enhanced pump.fun API discovery with comprehensive navigation."""

    def __init__(self):
        self.target_url = "https://pump.fun"
        self.results_dir = Path("enhanced_pump_results")
        self.results_dir.mkdir(exist_ok=True)

        # Enhanced configuration for comprehensive exploration
        self.config = {
            'headless': False,  # Show browser for demo
            'crawl_delay_ms': 2000,  # Respectful but efficient
            'max_interactions': 100,  # Comprehensive exploration
            'max_navigation_depth': 4,  # Deep navigation
            'respect_robots_txt': True,
            'safe_mode': True,
            'autonomous_navigation': True,
            'explore_all_links': True,
            'comprehensive_mode': True
        }

    async def run_enhanced_discovery(self):
        """Run enhanced API discovery with comprehensive navigation."""
        print("🚀 Enhanced Cipher-Spy API Discovery: pump.fun")
        print("="*70)
        print("🎯 Target: pump.fun")
        print("🔍 Mode: Comprehensive autonomous navigation")
        print("📊 Expected: 50+ API endpoints across all sections")
        print("="*70)

        try:
            # Initialize enhanced discovery agent
            agent = APIDiscoveryAgent(self.target_url, self.config)

            print("🤖 Initializing enhanced autonomous navigation agent...")
            print("📍 Will systematically explore:")
            print("   • Main navigation (Home, Board, Create, Live)")
            print("   • Individual coin pages")
            print("   • Interactive elements and forms")
            print("   • Advanced features and documentation")
            print()

            # Run comprehensive discovery
            print("🔍 Starting comprehensive API discovery...")
            start_time = datetime.now()

            results = await agent.discover_apis()

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            print(f"\n✅ Enhanced discovery completed in {duration:.2f} seconds")

            # Process and display enhanced results
            await self._process_enhanced_results(results, duration)

            return results

        except Exception as e:
            print(f"\n💥 Enhanced discovery failed: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}

    async def _process_enhanced_results(self, results: Dict[str, Any], duration: float):
        """Process and display enhanced discovery results."""
        print("\n📊 ENHANCED API DISCOVERY RESULTS")
        print("="*70)

        # Enhanced summary
        summary = results.get('summary', {})
        exploration = results.get('exploration_results', {})

        print(f"🎯 Target Explored: {self.target_url}")
        print(f"⏱️  Total Duration: {duration:.2f} seconds")
        print(f"📄 Pages Explored: {len(exploration.get('pages_explored', []))}")
        print(f"🔗 Total API Endpoints: {summary.get('total_endpoints_discovered', 0)}")
        print(f"🤖 Navigation Actions: {exploration.get('navigation_flows', 0)}")
        print(f"📡 API Calls Triggered: {summary.get('api_calls_triggered', 0)}")

        # Navigation summary
        if exploration.get('pages_explored'):
            print(f"\n📍 NAVIGATION SUMMARY")
            print("-" * 40)

            for page in exploration['pages_explored']:
                if isinstance(page, dict):
                    section = page.get('section', page.get('url', 'Unknown'))
                    api_calls = page.get('api_calls_triggered', 0)
                    status = "✅" if page.get('success', False) else "❌"
                    print(f"  {status} {section}: {api_calls} API calls")

        # Enhanced API analysis
        api_analysis = results.get('api_analysis', {})
        if api_analysis and not api_analysis.get('error'):
            print(f"\n🔗 COMPREHENSIVE API ANALYSIS")
            print("-" * 50)

            categorized = api_analysis.get('categorized_endpoints', {})

            # Show detailed breakdown by category
            for category, endpoints in categorized.items():
                if endpoints:
                    print(f"\n📂 {category.upper().replace('_', ' ')} ({len(endpoints)} endpoints):")

                    # Group by domain/service
                    domains = {}
                    for endpoint in endpoints:
                        url = endpoint.get('url', '')
                        domain = self._extract_domain(url)
                        if domain not in domains:
                            domains[domain] = []
                        domains[domain].append(endpoint)

                    for domain, domain_endpoints in domains.items():
                        print(f"  🌐 {domain} ({len(domain_endpoints)} calls):")
                        for endpoint in domain_endpoints[:3]:  # Show first 3
                            method = endpoint.get('method', 'GET')
                            url = endpoint.get('url', '')
                            status = endpoint.get('response_status', 'N/A')
                            path = self._extract_path(url)
                            print(f"    • {method} {path} → {status}")

                        if len(domain_endpoints) > 3:
                            print(f"    ... and {len(domain_endpoints) - 3} more")

            # API patterns analysis
            patterns = api_analysis.get('patterns', {})
            if patterns:
                print(f"\n📈 API PATTERNS DISCOVERED")
                print("-" * 30)

                base_urls = patterns.get('base_urls', [])
                print(f"🌐 API Base URLs ({len(base_urls)}):")
                for base_url in base_urls:
                    print(f"  • {base_url}")

                methods = patterns.get('methods_used', [])
                print(f"🔧 HTTP Methods: {', '.join(methods)}")

                common_paths = patterns.get('common_paths', {})
                if common_paths:
                    print("📍 Common API Paths:")
                    for path, count in sorted(common_paths.items(), key=lambda x: x[1], reverse=True)[:10]:
                        print(f"  • /{path} (used {count} times)")

        # Authentication analysis
        auth_analysis = results.get('authentication_analysis', {})
        if auth_analysis and not auth_analysis.get('error'):
            print(f"\n🔐 AUTHENTICATION & SECURITY ANALYSIS")
            print("-" * 45)

            auth_endpoints = auth_analysis.get('auth_endpoints', [])
            if auth_endpoints:
                print(f"🔑 Authentication Endpoints ({len(auth_endpoints)}):")
                for endpoint in auth_endpoints:
                    method = endpoint.get('method', 'GET')
                    url = endpoint.get('url', '')
                    path = self._extract_path(url)
                    print(f"  • {method} {path}")

            token_patterns = auth_analysis.get('token_patterns', {})
            for token_type, tokens in token_patterns.items():
                if tokens:
                    print(f"🎫 {token_type.replace('_', ' ').title()}: {len(tokens)} detected")

            auth_flows = auth_analysis.get('auth_flows', [])
            if auth_flows:
                print("🔄 Authentication Flows:")
                for flow in auth_flows:
                    print(f"  • {flow.get('type', 'unknown')}: {flow.get('description', 'N/A')}")

        # Network statistics
        network_stats = results.get('network_statistics', {})
        if network_stats:
            print(f"\n📊 NETWORK TRAFFIC ANALYSIS")
            print("-" * 35)
            print(f"📡 Total Requests: {network_stats.get('total_requests', 0)}")
            print(f"📨 Total Responses: {network_stats.get('total_responses', 0)}")
            print(f"🌐 Unique Domains: {network_stats.get('unique_domains', 0)}")
            print(f"📈 Status Codes: {network_stats.get('status_codes_seen', [])}")

        # Save enhanced results
        await self._save_enhanced_results(results, duration)

    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except:
            return "unknown"

    def _extract_path(self, url: str) -> str:
        """Extract path from URL for display."""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            path = parsed.path
            if parsed.query:
                path += f"?{parsed.query[:50]}{'...' if len(parsed.query) > 50 else ''}"
            return path if path else "/"
        except:
            return url

    async def _save_enhanced_results(self, results: Dict[str, Any], duration: float):
        """Save enhanced results with detailed analysis."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save comprehensive results
        results_file = self.results_dir / f"enhanced_pump_discovery_{timestamp}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\n💾 Enhanced results saved: {results_file}")

        # Generate enhanced documentation
        await self._generate_enhanced_docs(results, timestamp)

        # Generate testing artifacts
        await self._generate_testing_artifacts(results, timestamp)

    async def _generate_enhanced_docs(self, results: Dict[str, Any], timestamp: str):
        """Generate enhanced API documentation."""
        docs = []
        docs.append("# Enhanced Pump.fun API Discovery Report")
        docs.append(f"Generated by Cipher-Spy Enhanced Navigator on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        docs.append("")
        docs.append("## Executive Summary")
        docs.append("This report contains comprehensive API discovery results from autonomous")
        docs.append("navigation of pump.fun, including all major sections and interactive elements.")
        docs.append("")

        # Add detailed sections
        summary = results.get('summary', {})
        docs.append(f"- **Total API Endpoints Discovered**: {summary.get('total_endpoints_discovered', 0)}")
        docs.append(f"- **Pages Explored**: {len(results.get('exploration_results', {}).get('pages_explored', []))}")
        docs.append(f"- **Authentication Required**: {'Yes' if results.get('authentication_analysis', {}).get('requires_authentication') else 'No'}")
        docs.append("")

        # Add API categories
        api_analysis = results.get('api_analysis', {})
        categorized = api_analysis.get('categorized_endpoints', {})

        if categorized:
            docs.append("## API Endpoints by Category")
            docs.append("")

            for category, endpoints in categorized.items():
                if endpoints:
                    docs.append(f"### {category.upper().replace('_', ' ')} ({len(endpoints)} endpoints)")
                    docs.append("")

                    for endpoint in endpoints:
                        method = endpoint.get('method', 'GET')
                        url = endpoint.get('url', '')
                        status = endpoint.get('response_status', 'N/A')
                        docs.append(f"- **{method}** `{url}` → Status: {status}")

                    docs.append("")

        # Save enhanced documentation
        docs_file = self.results_dir / f"enhanced_pump_api_docs_{timestamp}.md"
        with open(docs_file, 'w') as f:
            f.write('\n'.join(docs))

        print(f"📚 Enhanced documentation saved: {docs_file}")

    async def _generate_testing_artifacts(self, results: Dict[str, Any], timestamp: str):
        """Generate comprehensive testing artifacts."""
        # Generate cURL commands
        api_analysis = results.get('api_analysis', {})
        schemas = api_analysis.get('schemas', {})

        if schemas:
            commands = []
            commands.append("#!/bin/bash")
            commands.append("# Enhanced pump.fun API testing commands")
            commands.append(f"# Generated by Cipher-Spy on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            commands.append("")

            for endpoint_key, schema in schemas.items():
                method = schema.get('method', 'GET')
                url = schema.get('url', '')

                if url:
                    commands.append(f"# {endpoint_key}")
                    curl_cmd = f"curl -X {method} '{url}'"

                    # Add common headers
                    curl_cmd += " -H 'User-Agent: Mozilla/5.0 (compatible; API-Test)'"
                    curl_cmd += " -H 'Accept: application/json'"

                    commands.append(curl_cmd)
                    commands.append("")

            # Save cURL commands
            curl_file = self.results_dir / f"enhanced_pump_curl_{timestamp}.sh"
            with open(curl_file, 'w') as f:
                f.write('\n'.join(commands))

            curl_file.chmod(0o755)
            print(f"🔧 Enhanced cURL commands saved: {curl_file}")


async def main():
    """Main entry point for enhanced demo."""
    # Setup logging
    setup_logging(level="INFO", environment="development")

    print("🚀 Cipher-Spy Enhanced Pump.fun API Discovery")
    print("Comprehensive autonomous navigation and API discovery")
    print("="*70)

    demo = EnhancedPumpFunDemo()

    try:
        results = await demo.run_enhanced_discovery()

        if results.get('error'):
            print(f"\n❌ Enhanced discovery failed: {results['error']}")
            return 1
        else:
            print(f"\n🎉 Enhanced API discovery completed successfully!")
            print(f"📁 Results saved to: {demo.results_dir}")
            print("\n🎯 Key Achievements:")
            print("  ✅ Comprehensive site navigation")
            print("  ✅ Maximum API endpoint discovery")
            print("  ✅ Detailed authentication analysis")
            print("  ✅ Complete testing artifacts generated")
            return 0

    except KeyboardInterrupt:
        print("\n👋 Enhanced discovery interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
