# pump_flags_variant_1

## Endpoint Information
- **URL**: https://pump.fun/api/flags
- **Method**: GET
- **Parameters**: {}

## Response Information
- **Response Time**: 285ms
- **Response Size**: 634 bytes
- **Content Type**: application/json

## Response Schema
```json
{
  "type": "object",
  "properties": {
    "trending_carousel_enabled": {
      "type": "boolean"
    },
    "semantic_search_enabled": {
      "type": "boolean"
    },
    "similar_coins_enabled": {
      "type": "boolean"
    },
    "trade_history_recs_enabled": {
      "type": "boolean"
    },
    "multi_column_advanced_enabled": {
      "type": "boolean"
    },
    "hybrid_search_enabled": {
      "type": "boolean"
    },
    "search_ranked_enabled": {
      "type": "boolean"
    },
    "homepage_v2_enabled": {
      "type": "boolean"
    },
    "livestreams_enabled": {
      "type": "boolean"
    },
    "create_coin_v2_enabled": {
      "type": "boolean"
    }
  },
  "total_keys": 19
}
```

## Sample Response
```json
{
  "trending_carousel_enabled": false,
  "semantic_search_enabled": false,
  "similar_coins_enabled": false,
  "trade_history_recs_enabled": false,
  "multi_column_advanced_enabled": false,
  "hybrid_search_enabled": false,
  "search_ranked_enabled": false,
  "homepage_v2_enabled": false,
  "livestreams_enabled": false,
  "create_coin_v2_enabled": false,
  "featured_coins_market_cap_updates_enabled": false,
  "livestream_overlay_enabled": false,
  "connect_x_account_enabled": false,
  "coinpage_v2_enabled": false,
  "creator_rewards_rollout_stage": false,
  "coin_preview_v2_enabled": false,
  "coin_creation_button_disabled": false,
  "screener_table_enabled": false,
  "zoom_interview_enabled": false
}
```

## Usage Example
```python
import requests

response = requests.get(
    'https://pump.fun/api/flags',
    params={}
)

data = response.json()
print(data)
```
