<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Cipher-Spy DevTools Panel</title>
  <link rel="stylesheet" href="panel.css">
</head>
<body>
  <div class="panel-container">
    <!-- Header -->
    <div class="panel-header">
      <div class="logo">
        <span class="icon">CS</span>
        <span class="title">Cipher-Spy Network Analysis</span>
      </div>
      <div class="controls">
        <button id="clearData" class="btn btn-secondary">Clear</button>
        <button id="exportData" class="btn btn-primary">Export</button>
      </div>
    </div>

    <!-- Tabs -->
    <div class="tabs">
      <button class="tab-button active" data-tab="requests">Requests</button>
      <button class="tab-button" data-tab="analysis">Analysis</button>
      <button class="tab-button" data-tab="discovery">Discovery</button>
      <button class="tab-button" data-tab="docs">Documentation</button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Requests Tab -->
      <div id="requests-tab" class="tab-pane active">
        <div class="filter-bar">
          <input type="text" id="urlFilter" placeholder="Filter by URL..." class="filter-input">
          <select id="methodFilter" class="filter-select">
            <option value="">All Methods</option>
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
          </select>
          <label class="filter-checkbox">
            <input type="checkbox" id="apiOnly"> API Only
          </label>
        </div>

        <div class="requests-table">
          <div class="table-header">
            <div class="col-method">Method</div>
            <div class="col-url">URL</div>
            <div class="col-status">Status</div>
            <div class="col-type">Type</div>
            <div class="col-time">Time</div>
          </div>
          <div id="requestsList" class="table-body">
            <div class="empty-state">No requests captured yet. Start browsing to see network activity.</div>
          </div>
        </div>
      </div>

      <!-- Analysis Tab -->
      <div id="analysis-tab" class="tab-pane">
        <div class="analysis-section">
          <h3>Session Analysis</h3>
          <div id="sessionStats" class="stats-grid">
            <div class="stat-card">
              <div class="stat-number" id="totalRequests">0</div>
              <div class="stat-label">Total Requests</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="apiRequests">0</div>
              <div class="stat-label">API Requests</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="uniqueDomains">0</div>
              <div class="stat-label">Unique Domains</div>
            </div>
            <div class="stat-card">
              <div class="stat-number" id="errorRate">0%</div>
              <div class="stat-label">Error Rate</div>
            </div>
          </div>

          <div class="analysis-details">
            <h4>Detected Technologies</h4>
            <div id="technologiesList" class="tech-list">
              <div class="empty-state">No technologies detected yet.</div>
            </div>

            <h4>API Patterns</h4>
            <div id="patternsList" class="patterns-list">
              <div class="empty-state">No patterns detected yet.</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Discovery Tab -->
      <div id="discovery-tab" class="tab-pane">
        <div class="discovery-section">
          <div class="discovery-controls">
            <button id="triggerDiscovery" class="btn btn-primary">Start API Discovery</button>
            <button id="refreshDiscovery" class="btn btn-secondary">Refresh</button>
          </div>

          <div class="discovery-status">
            <div id="discoveryProgress" class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div id="discoveryStatus" class="status-text">Ready to start discovery</div>
          </div>

          <div class="discovered-apis">
            <h4>Discovered APIs</h4>
            <div id="discoveredEndpoints" class="endpoints-list">
              <div class="empty-state">No APIs discovered yet. Click "Start API Discovery" to begin.</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Documentation Tab -->
      <div id="docs-tab" class="tab-pane">
        <div class="docs-section">
          <div class="docs-controls">
            <button id="generateDocs" class="btn btn-primary">Generate Documentation</button>
            <select id="docsFormat" class="format-select">
              <option value="openapi">OpenAPI 3.0</option>
              <option value="markdown">Markdown</option>
              <option value="html">HTML</option>
            </select>
          </div>

          <div class="docs-preview">
            <div id="docsContent" class="docs-content">
              <div class="empty-state">No documentation generated yet. Discover APIs first, then generate documentation.</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="panel.js"></script>
</body>
</html>
