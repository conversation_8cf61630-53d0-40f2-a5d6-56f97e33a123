"""
Crawler Agent for Cipher-Spy.

Autonomous web reconnaissance agent that intelligently navigates websites,
interacts with all page elements, and comprehensively reverse-engineers
API traffic to generate detailed reports.
"""

import asyncio
import json
import re
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass

# Optional LangChain imports
try:
    from langchain.tools import BaseTool
    from langchain.schema import BaseMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    BaseTool = None
    BaseMessage = None
    LANGCHAIN_AVAILABLE = False
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext, Route, Request, Response

from .base_agent import BaseAgent
from ..core.state import ScanState, ScanStatus, AgentStatus, PageInfo, EndpointInfo
from ..core.exceptions import CipherSpyException, CrawlingException
from ..crawling.playwright_crawler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..crawling.network_interceptor import NetworkInterceptor
from ..crawling.form_handler import <PERSON><PERSON><PERSON><PERSON>
from ..crawling.scope_manager import <PERSON><PERSON><PERSON>anager
from ..utils.logging import get_logger


@dataclass
class CrawlResult:
    """Result of a crawling operation."""
    pages_discovered: int
    endpoints_discovered: int
    forms_found: int
    interactions_performed: int
    errors_encountered: List[str]
    crawl_duration: float


class CrawlerAgent(BaseAgent):
    """
    Autonomous web crawler agent for comprehensive reconnaissance.

    This agent performs intelligent web crawling with the following capabilities:
    - Autonomous navigation using DOM heuristics
    - Complete network traffic interception and analysis
    - Form interaction and authentication handling
    - API endpoint discovery and reverse engineering
    - Scope-aware crawling with loop detection
    """

    def __init__(self, **kwargs):
        super().__init__(
            agent_id="crawler",
            agent_type="crawler",
            **kwargs
        )
        self.logger = get_logger(f"{__name__}.crawler")

        # Crawler components
        self.playwright_crawler: Optional[PlaywrightCrawler] = None
        self.network_interceptor: Optional[NetworkInterceptor] = None
        self.form_handler: Optional[FormHandler] = None
        self.scope_manager: Optional[ScopeManager] = None

        # Crawling state
        self.visited_urls: Set[str] = set()
        self.pending_urls: Set[str] = set()
        self.discovered_endpoints: List[Dict[str, Any]] = []
        self.interaction_log: List[Dict[str, Any]] = []

        # Configuration
        self.max_depth = self.get_config_value("max_crawl_depth", 5)
        self.max_pages = self.get_config_value("max_pages_per_domain", 1000)
        self.crawl_delay = self.get_config_value("crawl_delay_ms", 1000)
        self.respect_robots = self.get_config_value("respect_robots_txt", True)
        self.user_agent = self.get_config_value("user_agent", "CipherSpy/1.0")

        self.logger.info(f"Crawler agent initialized with max_depth={self.max_depth}, max_pages={self.max_pages}")

    async def execute(self, state: ScanState) -> ScanState:
        """
        Execute autonomous web crawling and reconnaissance.

        Args:
            state: Current scan state

        Returns:
            ScanState: Updated state with discovered pages and endpoints
        """
        await self.start_execution(state)

        try:
            # Validate target
            self.validate_state(state)
            target_url = state.target.url

            self.log_progress("Initializing crawler components", 0.1)
            await self._initialize_components(state)

            self.log_progress("Starting autonomous crawling", 0.2)
            crawl_result = await self._perform_crawling(target_url, state)

            self.log_progress("Processing discovered data", 0.8)
            await self._process_crawl_results(crawl_result, state)

            self.log_progress("Crawling completed successfully", 1.0)
            await self.complete_execution(state, success=True)

        except Exception as e:
            await self.handle_error(state, e)
            raise CrawlingException(f"Crawler execution failed: {e}", url=state.target.url if state.target else None)

        finally:
            await self._cleanup_components()

        return state

    async def _initialize_components(self, state: ScanState) -> None:
        """Initialize crawler components."""
        try:
            # Initialize scope manager
            self.scope_manager = ScopeManager(
                base_url=state.target.url,
                allowed_domains=state.target.scope or [],
                respect_robots=self.respect_robots
            )

            # Initialize network interceptor
            self.network_interceptor = NetworkInterceptor()

            # Initialize form handler
            self.form_handler = FormHandler(
                credentials=state.target.credentials
            )

            # Initialize Playwright crawler
            self.playwright_crawler = PlaywrightCrawler(
                network_interceptor=self.network_interceptor,
                form_handler=self.form_handler,
                scope_manager=self.scope_manager,
                user_agent=self.user_agent,
                delay_ms=self.crawl_delay
            )

            self.logger.info("Crawler components initialized successfully")

        except Exception as e:
            raise CrawlingException(f"Failed to initialize crawler components: {e}")

    async def _perform_crawling(self, target_url: str, state: ScanState) -> CrawlResult:
        """
        Perform the main crawling operation.

        Args:
            target_url: Starting URL for crawling
            state: Current scan state

        Returns:
            CrawlResult: Summary of crawling results
        """
        start_time = datetime.utcnow()
        errors = []

        try:
            # Start the crawler
            await self.playwright_crawler.start()

            # Add initial URL to pending
            self.pending_urls.add(target_url)

            pages_discovered = 0
            interactions_performed = 0

            # Main crawling loop
            while self.pending_urls and pages_discovered < self.max_pages:
                if not self.should_continue(state):
                    break

                current_url = self.pending_urls.pop()

                if current_url in self.visited_urls:
                    continue

                try:
                    self.log_progress(
                        f"Crawling page {pages_discovered + 1}/{self.max_pages}: {current_url}",
                        0.2 + (0.6 * pages_discovered / self.max_pages)
                    )

                    # Crawl the page
                    page_result = await self.playwright_crawler.crawl_page(current_url)

                    if page_result:
                        # Mark as visited
                        self.visited_urls.add(current_url)
                        pages_discovered += 1

                        # Add page to state
                        page_info = PageInfo(
                            url=current_url,
                            title=page_result.get("title"),
                            status_code=page_result.get("status_code", 200),
                            content_type=page_result.get("content_type"),
                            screenshot_path=page_result.get("screenshot_path")
                        )
                        state.add_page(page_info)

                        # Add discovered URLs to pending
                        for url in page_result.get("discovered_urls", []):
                            if self.scope_manager.is_in_scope(url) and url not in self.visited_urls:
                                self.pending_urls.add(url)

                        # Track interactions
                        interactions_performed += page_result.get("interactions_count", 0)

                        # Process network traffic
                        await self._process_network_traffic(state)

                except Exception as e:
                    error_msg = f"Error crawling {current_url}: {e}"
                    errors.append(error_msg)
                    self.logger.error(error_msg)

                # Respect crawl delay
                if self.crawl_delay > 0:
                    await asyncio.sleep(self.crawl_delay / 1000)

            # Get final network data
            endpoints_discovered = len(self.network_interceptor.get_discovered_endpoints())
            forms_found = len(self.form_handler.get_discovered_forms())

            crawl_duration = (datetime.utcnow() - start_time).total_seconds()

            return CrawlResult(
                pages_discovered=pages_discovered,
                endpoints_discovered=endpoints_discovered,
                forms_found=forms_found,
                interactions_performed=interactions_performed,
                errors_encountered=errors,
                crawl_duration=crawl_duration
            )

        except Exception as e:
            raise CrawlingException(f"Crawling operation failed: {e}")

    async def _process_network_traffic(self, state: ScanState) -> None:
        """Process intercepted network traffic and extract API endpoints."""
        try:
            # Get new endpoints from network interceptor
            new_endpoints = self.network_interceptor.get_new_endpoints()

            for endpoint_data in new_endpoints:
                endpoint_info = EndpointInfo(
                    url=endpoint_data["url"],
                    method=endpoint_data["method"],
                    parameters=endpoint_data.get("parameters", {}),
                    headers=endpoint_data.get("headers", {}),
                    response_status=endpoint_data.get("response_status"),
                    response_content_type=endpoint_data.get("response_content_type"),
                    response_sample=endpoint_data.get("response_sample"),
                    discovered_from=endpoint_data.get("discovered_from")
                )
                state.add_endpoint(endpoint_info)

        except Exception as e:
            self.logger.error(f"Error processing network traffic: {e}")

    async def _process_crawl_results(self, result: CrawlResult, state: ScanState) -> None:
        """Process and summarize crawl results."""
        try:
            # Log summary
            self.logger.info(
                f"Crawling completed: {result.pages_discovered} pages, "
                f"{result.endpoints_discovered} endpoints, "
                f"{result.forms_found} forms in {result.crawl_duration:.2f}s"
            )

            # Update agent metadata
            self.update_state(
                state,
                task=f"Completed crawling: {result.pages_discovered} pages discovered",
                progress=1.0
            )

            if result.errors_encountered:
                self.logger.warning(f"Encountered {len(result.errors_encountered)} errors during crawling")

        except Exception as e:
            self.logger.error(f"Error processing crawl results: {e}")

    async def _cleanup_components(self) -> None:
        """Clean up crawler components."""
        try:
            if self.playwright_crawler:
                await self.playwright_crawler.stop()

            self.logger.info("Crawler components cleaned up")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def get_tools(self) -> List:
        """Get tools available to the crawler agent."""
        # Crawler typically doesn't use LLM tools directly
        return []

    async def get_crawl_statistics(self) -> Dict[str, Any]:
        """Get detailed crawling statistics."""
        return {
            "visited_urls_count": len(self.visited_urls),
            "pending_urls_count": len(self.pending_urls),
            "discovered_endpoints_count": len(self.discovered_endpoints),
            "interaction_log_count": len(self.interaction_log),
            "visited_urls": list(self.visited_urls),
            "pending_urls": list(self.pending_urls)
        }
