#!/usr/bin/env python3
"""
Pump.fun Specific API Analyzer

Specialized implementation of BaseAPIAnalyzer for pump.fun cryptocurrency platform.
This analyzer leverages the universal framework while providing pump.fun-specific
business logic, parameter discovery, and intelligence extraction.
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..base_analyzer import BaseAPIAnalyzer, APIEndpointConfig
from ..target_config import TargetConfig, APIType


class PumpFunAPIAnalyzer(BaseAPIAnalyzer):
    """
    Specialized API analyzer for pump.fun cryptocurrency platform.
    
    This analyzer extends the universal framework with pump.fun-specific
    knowledge about cryptocurrency trading APIs, market data patterns,
    and business intelligence extraction.
    """

    def __init__(self, target_config: TargetConfig = None, **kwargs):
        """Initialize pump.fun analyzer with specialized configuration."""
        
        # Use provided config or create pump.fun specific config
        if target_config is None:
            target_config = TargetConfig.create_pump_fun_config()
        
        super().__init__(target_config, **kwargs)
        
        # Pump.fun specific parameter test sets
        self.pump_parameter_sets = {
            'crypto_filtering': [
                'minMarketCap', 'maxMarketCap', 'marketCapMin', 'marketCapMax',
                'minVolume', 'maxVolume', 'volumeMin', 'volumeMax', 'volume24h',
                'minHolders', 'maxHolders', 'holdersMin', 'holdersMax',
                'minAge', 'maxAge', 'ageMin', 'ageMax', 'createdAfter', 'createdBefore',
                'minLiquidity', 'maxLiquidity', 'liquidityMin', 'liquidityMax',
                'minPrice', 'maxPrice', 'priceMin', 'priceMax',
                'minChange24h', 'maxChange24h', 'change24hMin', 'change24hMax'
            ],
            'crypto_search': [
                'search', 'query', 'name', 'symbol', 'description', 'keyword',
                'creator', 'creatorAddress', 'contractAddress', 'address',
                'tag', 'tags', 'category', 'type', 'mint', 'mintAddress'
            ],
            'crypto_sorting': [
                'sortBy', 'orderBy', 'sort', 'order', 'direction', 'desc', 'asc',
                'sortDirection', 'sortOrder', 'reverse'
            ],
            'crypto_time': [
                'timeframe', 'period', 'interval', 'duration',
                'startTime', 'endTime', 'fromTime', 'toTime',
                'since', 'until', 'timestamp', 'date',
                'graduatedAfter', 'graduatedBefore', 'graduationDate'
            ],
            'crypto_advanced': [
                'include', 'exclude', 'fields', 'select', 'expand',
                'format', 'output', 'version', 'v', 'api_version',
                'detailed', 'full', 'minimal', 'compact',
                'fresh', 'cached', 'realtime', 'live'
            ]
        }
        
        # Pump.fun specific test values
        self.pump_test_values = {
            'crypto_numeric': [1, 10, 50, 100, 500, 1000, 5000, 10000, 50000, 100000],
            'crypto_boolean': [True, False, 'true', 'false', '1', '0'],
            'crypto_strings': ['pump', 'meme', 'coin', 'token', 'new', 'hot', 'trending'],
            'crypto_sort_options': [
                'creationTime', 'marketCap', 'volume', 'holders', 'age', 
                'price', 'change24h', 'liquidity', 'graduationTime'
            ],
            'crypto_time_formats': ['1h', '24h', '7d', '30d', '1y', '2024-01-01', '2024-12-01'],
            'crypto_addresses': [
                '********************************', 
                'So111111111********************************',
                'GtGHchFcr48SRZQHMUN4jTr7PpNZoQSXbpBrexuipump'
            ]
        }

    async def discover_endpoints(self) -> List[APIEndpointConfig]:
        """
        Discover pump.fun specific API endpoints.
        
        Returns:
            List[APIEndpointConfig]: Discovered pump.fun endpoints
        """
        print("🔍 Discovering pump.fun specific endpoints...")
        
        discovered_endpoints = []
        
        # Start with known pump.fun endpoints
        discovered_endpoints.extend(self.target_config.known_endpoints)
        print(f"   📋 Added {len(self.target_config.known_endpoints)} known pump.fun endpoints")
        
        # Discover additional pump.fun endpoints
        additional_endpoints = await self._discover_pump_specific_endpoints()
        discovered_endpoints.extend(additional_endpoints)
        print(f"   🎯 Discovered {len(additional_endpoints)} additional pump.fun endpoints")
        
        return discovered_endpoints

    async def _discover_pump_specific_endpoints(self) -> List[APIEndpointConfig]:
        """Discover additional pump.fun specific endpoints."""
        endpoints = []
        
        # Common pump.fun API patterns
        pump_endpoints = [
            ('token_info', '/coins/{mint}', 'Token information lookup'),
            ('token_trades', '/coins/{mint}/trades', 'Token trading history'),
            ('token_holders', '/coins/{mint}/holders', 'Token holder information'),
            ('trending_coins', '/coins/trending', 'Trending cryptocurrency coins'),
            ('new_coins', '/coins/new', 'Recently created coins'),
            ('top_coins', '/coins/top', 'Top performing coins'),
            ('search_coins', '/coins/search', 'Coin search functionality'),
            ('creator_coins', '/creators/{address}/coins', 'Coins by creator'),
            ('graduation_stats', '/stats/graduation', 'Graduation statistics'),
            ('platform_stats', '/stats/platform', 'Platform-wide statistics')
        ]
        
        for name, path, description in pump_endpoints:
            for base_url in self.target_config.base_urls:
                test_url = f"{base_url}{path}"
                
                # Test if endpoint exists (with placeholder values)
                test_url_concrete = test_url.replace('{mint}', 'GtGHchFcr48SRZQHMUN4jTr7PpNZoQSXbpBrexuipump')
                test_url_concrete = test_url_concrete.replace('{address}', '********************************')
                
                try:
                    response = self.session.get(test_url_concrete, timeout=10)
                    if response.status_code in [200, 404]:  # 404 might indicate endpoint exists but needs valid params
                        endpoint = APIEndpointConfig(
                            name=name,
                            url=test_url_concrete,
                            method="GET",
                            api_type=APIType.REST,
                            description=description,
                            business_value="Cryptocurrency trading intelligence"
                        )
                        endpoints.append(endpoint)
                        print(f"      ✅ Found: {name}")
                
                except Exception:
                    continue
                
                # Rate limiting
                await asyncio.sleep(self.rate_limit_delay)
        
        return endpoints

    async def extract_business_logic(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract pump.fun specific business logic and intelligence.
        
        Args:
            endpoint_data: Raw endpoint response data
            
        Returns:
            Dict[str, Any]: Pump.fun specific business intelligence
        """
        business_logic = {
            'cryptocurrency_metrics': {},
            'trading_patterns': {},
            'graduation_intelligence': {},
            'market_dynamics': {},
            'investment_signals': {},
            'competitive_analysis': {}
        }
        
        # Extract cryptocurrency-specific metrics
        business_logic['cryptocurrency_metrics'] = self._extract_crypto_metrics(endpoint_data)
        
        # Analyze trading patterns
        business_logic['trading_patterns'] = self._analyze_trading_patterns(endpoint_data)
        
        # Extract graduation intelligence
        business_logic['graduation_intelligence'] = self._extract_graduation_intelligence(endpoint_data)
        
        # Analyze market dynamics
        business_logic['market_dynamics'] = self._analyze_market_dynamics(endpoint_data)
        
        # Identify investment signals
        business_logic['investment_signals'] = self._identify_investment_signals(endpoint_data)
        
        # Competitive analysis
        business_logic['competitive_analysis'] = self._perform_competitive_analysis(endpoint_data)
        
        return business_logic

    def _extract_crypto_metrics(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract cryptocurrency-specific metrics."""
        metrics = {
            'price_metrics': [],
            'volume_metrics': [],
            'holder_metrics': [],
            'liquidity_metrics': [],
            'performance_metrics': []
        }
        
        if 'fields' in endpoint_data:
            for field_path in endpoint_data['fields'].keys():
                field_lower = field_path.lower()
                
                # Categorize crypto metrics
                if any(keyword in field_lower for keyword in ['price', 'cost', 'value']):
                    metrics['price_metrics'].append(field_path)
                elif any(keyword in field_lower for keyword in ['volume', 'traded', 'turnover']):
                    metrics['volume_metrics'].append(field_path)
                elif any(keyword in field_lower for keyword in ['holder', 'owner', 'wallet']):
                    metrics['holder_metrics'].append(field_path)
                elif any(keyword in field_lower for keyword in ['liquidity', 'pool', 'reserve']):
                    metrics['liquidity_metrics'].append(field_path)
                elif any(keyword in field_lower for keyword in ['change', 'growth', 'return', 'roi']):
                    metrics['performance_metrics'].append(field_path)
        
        return metrics

    def _analyze_trading_patterns(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trading patterns from endpoint data."""
        patterns = {
            'trading_indicators': [],
            'market_signals': [],
            'behavioral_patterns': []
        }
        
        # Look for trading-related fields
        if 'fields' in endpoint_data:
            trading_fields = []
            for field_path in endpoint_data['fields'].keys():
                if any(keyword in field_path.lower() for keyword in [
                    'trade', 'buy', 'sell', 'swap', 'exchange', 'transaction'
                ]):
                    trading_fields.append(field_path)
            
            patterns['trading_indicators'] = trading_fields
        
        return patterns

    def _extract_graduation_intelligence(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract graduation-specific intelligence."""
        graduation_intel = {
            'graduation_criteria': [],
            'success_indicators': [],
            'timeline_factors': []
        }
        
        if 'fields' in endpoint_data:
            for field_path in endpoint_data['fields'].keys():
                field_lower = field_path.lower()
                
                if any(keyword in field_lower for keyword in ['graduation', 'graduated', 'success']):
                    graduation_intel['graduation_criteria'].append(field_path)
                elif any(keyword in field_lower for keyword in ['time', 'date', 'duration', 'age']):
                    graduation_intel['timeline_factors'].append(field_path)
        
        return graduation_intel

    def _analyze_market_dynamics(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market dynamics and trends."""
        dynamics = {
            'market_indicators': [],
            'trend_signals': [],
            'volatility_measures': []
        }
        
        if 'fields' in endpoint_data:
            for field_path in endpoint_data['fields'].keys():
                field_lower = field_path.lower()
                
                if any(keyword in field_lower for keyword in ['market', 'cap', 'capitalization']):
                    dynamics['market_indicators'].append(field_path)
                elif any(keyword in field_lower for keyword in ['trend', 'momentum', 'direction']):
                    dynamics['trend_signals'].append(field_path)
                elif any(keyword in field_lower for keyword in ['volatility', 'variance', 'deviation']):
                    dynamics['volatility_measures'].append(field_path)
        
        return dynamics

    def _identify_investment_signals(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify investment signals and opportunities."""
        signals = {
            'bullish_indicators': [],
            'bearish_indicators': [],
            'risk_factors': [],
            'opportunity_metrics': []
        }
        
        # This would be enhanced with actual data analysis
        # For now, identify potential signal fields
        if 'fields' in endpoint_data:
            for field_path in endpoint_data['fields'].keys():
                field_lower = field_path.lower()
                
                if any(keyword in field_lower for keyword in [
                    'growth', 'increase', 'gain', 'profit', 'positive'
                ]):
                    signals['bullish_indicators'].append(field_path)
                elif any(keyword in field_lower for keyword in [
                    'decline', 'decrease', 'loss', 'negative', 'drop'
                ]):
                    signals['bearish_indicators'].append(field_path)
                elif any(keyword in field_lower for keyword in [
                    'risk', 'volatility', 'uncertainty', 'danger'
                ]):
                    signals['risk_factors'].append(field_path)
        
        return signals

    def _perform_competitive_analysis(self, endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform competitive analysis specific to pump.fun."""
        analysis = {
            'platform_advantages': [],
            'unique_features': [],
            'market_position_indicators': []
        }
        
        # Identify unique pump.fun features
        if 'fields' in endpoint_data:
            unique_fields = []
            for field_path in endpoint_data['fields'].keys():
                if any(keyword in field_path.lower() for keyword in [
                    'pump', 'graduation', 'bonding', 'curve', 'meme'
                ]):
                    unique_fields.append(field_path)
            
            analysis['unique_features'] = unique_fields
        
        return analysis

    def get_parameter_test_sets(self) -> Dict[str, List[str]]:
        """Get pump.fun specific parameter test sets."""
        return self.pump_parameter_sets

    def get_test_values(self) -> Dict[str, List[Any]]:
        """Get pump.fun specific test values."""
        return self.pump_test_values

    def _should_test_combination(self, param: str, value_type: str) -> bool:
        """Determine if a parameter/value type combination should be tested for pump.fun."""
        param_lower = param.lower()
        
        # Crypto-specific filtering
        crypto_numeric_params = [
            'limit', 'offset', 'min', 'max', 'cap', 'volume', 'holders', 
            'age', 'price', 'liquidity', 'change'
        ]
        crypto_string_params = [
            'search', 'query', 'name', 'symbol', 'description', 'creator', 
            'address', 'mint', 'tag', 'category'
        ]
        crypto_boolean_params = ['desc', 'asc', 'reverse', 'detailed', 'full', 'fresh', 'live']
        
        if any(keyword in param_lower for keyword in crypto_numeric_params):
            return value_type in ['crypto_numeric', 'crypto_boolean']
        elif any(keyword in param_lower for keyword in crypto_string_params):
            return value_type in ['crypto_strings', 'crypto_addresses']
        elif any(keyword in param_lower for keyword in crypto_boolean_params):
            return value_type == 'crypto_boolean'
        elif 'sort' in param_lower:
            return value_type in ['crypto_sort_options', 'crypto_boolean']
        elif 'time' in param_lower or 'date' in param_lower:
            return value_type == 'crypto_time_formats'
        else:
            return True  # Test all combinations for unknown parameters

    async def analyze_graduation_patterns(self) -> Dict[str, Any]:
        """
        Specialized method to analyze pump.fun graduation patterns.
        
        Returns:
            Dict[str, Any]: Graduation pattern analysis
        """
        print("🎓 Analyzing pump.fun graduation patterns...")
        
        graduation_analysis = {
            'graduation_thresholds': {},
            'success_patterns': {},
            'timeline_analysis': {},
            'performance_metrics': {}
        }
        
        # Get graduated coins endpoint
        graduated_endpoint = None
        for endpoint in self.target_config.known_endpoints:
            if 'graduated' in endpoint.name.lower():
                graduated_endpoint = endpoint
                break
        
        if graduated_endpoint:
            try:
                # Collect graduated coins data with different parameters
                graduated_samples = []
                for limit in [50, 100, 200]:
                    response = self.session.get(
                        graduated_endpoint.url, 
                        params={'limit': limit, 'sortBy': 'creationTime'}, 
                        timeout=15
                    )
                    if response.status_code == 200:
                        data = response.json()
                        graduated_samples.append(data)
                    await asyncio.sleep(self.rate_limit_delay)
                
                # Analyze graduation patterns
                if graduated_samples:
                    graduation_analysis['success_patterns'] = self._extract_graduation_success_patterns(graduated_samples)
                    graduation_analysis['timeline_analysis'] = self._analyze_graduation_timelines(graduated_samples)
                    graduation_analysis['performance_metrics'] = self._calculate_graduation_performance_metrics(graduated_samples)
                
            except Exception as e:
                print(f"      ⚠️ Graduation analysis error: {e}")
        
        return graduation_analysis

    def _extract_graduation_success_patterns(self, samples: List[Dict]) -> Dict[str, Any]:
        """Extract patterns that indicate successful graduation."""
        patterns = {
            'common_characteristics': [],
            'success_indicators': [],
            'threshold_analysis': {}
        }
        
        # Analyze common characteristics of graduated coins
        all_graduated = []
        for sample in samples:
            if isinstance(sample, list):
                all_graduated.extend(sample)
            elif isinstance(sample, dict) and 'data' in sample:
                all_graduated.extend(sample['data'])
        
        if all_graduated:
            # Analyze numeric thresholds
            numeric_fields = ['marketCap', 'volume', 'holders', 'liquidity', 'price']
            for field in numeric_fields:
                values = [coin.get(field) for coin in all_graduated if coin.get(field) is not None]
                if values:
                    patterns['threshold_analysis'][field] = {
                        'min': min(values),
                        'max': max(values),
                        'avg': sum(values) / len(values),
                        'median': sorted(values)[len(values) // 2]
                    }
        
        return patterns

    def _analyze_graduation_timelines(self, samples: List[Dict]) -> Dict[str, Any]:
        """Analyze graduation timing patterns."""
        timeline_analysis = {
            'average_time_to_graduation': None,
            'graduation_frequency': {},
            'seasonal_patterns': {}
        }
        
        # Extract graduation timestamps and analyze patterns
        graduation_times = []
        for sample in samples:
            if isinstance(sample, list):
                for coin in sample:
                    if coin.get('graduationTime') or coin.get('graduatedAt'):
                        graduation_times.append(coin.get('graduationTime') or coin.get('graduatedAt'))
        
        if graduation_times:
            timeline_analysis['graduation_frequency'] = {
                'total_graduations': len(graduation_times),
                'recent_graduations': len([t for t in graduation_times if self._is_recent_timestamp(t)])
            }
        
        return timeline_analysis

    def _calculate_graduation_performance_metrics(self, samples: List[Dict]) -> Dict[str, Any]:
        """Calculate performance metrics for graduated coins."""
        metrics = {
            'success_rate_indicators': {},
            'performance_distribution': {},
            'roi_analysis': {}
        }
        
        # Calculate various performance metrics
        all_graduated = []
        for sample in samples:
            if isinstance(sample, list):
                all_graduated.extend(sample)
            elif isinstance(sample, dict) and 'data' in sample:
                all_graduated.extend(sample['data'])
        
        if all_graduated:
            # Analyze market cap distribution
            market_caps = [coin.get('marketCap', 0) for coin in all_graduated if coin.get('marketCap')]
            if market_caps:
                metrics['performance_distribution']['market_cap'] = {
                    'count': len(market_caps),
                    'total_value': sum(market_caps),
                    'average': sum(market_caps) / len(market_caps),
                    'top_10_percent': sorted(market_caps, reverse=True)[:max(1, len(market_caps) // 10)]
                }
        
        return metrics

    def _is_recent_timestamp(self, timestamp: str) -> bool:
        """Check if timestamp is recent (within last 30 days)."""
        try:
            from datetime import datetime, timedelta
            if isinstance(timestamp, str):
                # Try different timestamp formats
                for fmt in ['%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%SZ', '%Y-%m-%d']:
                    try:
                        ts = datetime.strptime(timestamp, fmt)
                        return datetime.now() - ts < timedelta(days=30)
                    except:
                        continue
            return False
        except:
            return False
