#!/usr/bin/env python3
"""
Usage Examples for Pump.fun Advanced Scanner API Client

Demonstrates practical applications of the discovered APIs.
"""

from pump_scanner_client import PumpScannerClient, ScannerConfig
import time
import json


def example_basic_scanning():
    """Basic coin scanning example."""
    print("🔍 Basic Coin Scanning Example")
    print("-" * 40)
    
    client = PumpScannerClient()
    
    # Get latest coins
    latest_coins = client.get_advanced_coins(sort_by='creationTime', limit=20)
    print(f"Found {len(latest_coins)} latest coins")
    
    # Get coins sorted by market cap
    top_coins = client.get_advanced_coins(sort_by='marketCap', limit=10)
    print(f"Found {len(top_coins)} top coins by market cap")
    
    # Get graduated coins
    graduated = client.get_graduated_coins(limit=15)
    print(f"Found {len(graduated)} graduated coins")


def example_advanced_filtering():
    """Advanced filtering and analysis example."""
    print("\n🎯 Advanced Filtering Example")
    print("-" * 40)
    
    client = PumpScannerClient()
    
    # Scan for new high-potential coins
    new_coins = client.scan_new_coins(max_age_hours=6, min_market_cap=5000)
    print(f"Found {len(new_coins)} new high-potential coins")
    
    # Analyze social engagement
    social_data = client.get_social_replies(limit=500)
    print(f"Retrieved {len(social_data)} social interactions")


def example_real_time_monitoring():
    """Real-time monitoring example."""
    print("\n📡 Real-time Monitoring Example")
    print("-" * 40)
    
    client = PumpScannerClient()
    
    def on_new_graduated_coins(coins):
        print(f"🚀 New graduated coins detected: {len(coins)}")
        for coin in coins:
            print(f"   💎 {coin}")
    
    # Start monitoring (this would run indefinitely)
    print("Starting graduated coin monitoring...")
    # client.monitor_graduated_coins(callback=on_new_graduated_coins)


def example_feature_analysis():
    """Feature flags and configuration analysis."""
    print("\n⚙️  Feature Analysis Example")
    print("-" * 40)
    
    client = PumpScannerClient()
    
    # Get current feature flags
    flags = client.get_feature_flags()
    print("Current feature flags:")
    print(json.dumps(flags, indent=2))


def example_comprehensive_scanner():
    """Comprehensive scanning workflow."""
    print("\n🔬 Comprehensive Scanner Example")
    print("-" * 40)
    
    client = PumpScannerClient()
    
    # Multi-criteria scanning
    results = {
        'latest_coins': client.get_advanced_coins(sort_by='creationTime', limit=50),
        'top_volume': client.get_advanced_coins(sort_by='volume', limit=20),
        'graduated_coins': client.get_graduated_coins(limit=30),
        'social_activity': client.get_social_replies(limit=200),
        'feature_config': client.get_feature_flags()
    }
    
    print("Comprehensive scan results:")
    for category, data in results.items():
        print(f"   📊 {category}: {len(data) if isinstance(data, list) else 'configured'}")
    
    return results


if __name__ == "__main__":
    print("🚀 Pump.fun Advanced Scanner API Examples")
    print("=" * 50)
    
    # Run examples
    example_basic_scanning()
    example_advanced_filtering()
    example_real_time_monitoring()
    example_feature_analysis()
    
    # Comprehensive example
    comprehensive_results = example_comprehensive_scanner()
    
    print("\n✅ All examples completed successfully!")
