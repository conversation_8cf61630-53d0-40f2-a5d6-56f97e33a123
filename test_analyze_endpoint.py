#!/usr/bin/env python3
"""
Test the new analyze-session endpoint
"""

import requests
import json


def test_analyze_endpoint():
    """Test the analyze-session endpoint."""
    print("🧪 Testing analyze-session endpoint...")
    
    # Test data with sample requests
    test_data = {
        "session_id": "test_analysis",
        "requests": [
            {
                "url": "https://api.example.com/users",
                "method": "GET",
                "headers": {"Content-Type": "application/json"},
                "timestamp": "2025-06-25T13:55:30.000Z"
            },
            {
                "url": "https://example.com/api/posts",
                "method": "POST",
                "headers": {"Content-Type": "application/json"},
                "body": '{"title": "test"}',
                "timestamp": "2025-06-25T13:55:31.000Z"
            },
            {
                "url": "https://api.github.com/repos/user/repo",
                "method": "GET",
                "headers": {"Authorization": "Bearer token123"},
                "timestamp": "2025-06-25T13:55:32.000Z"
            }
        ]
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/extension/analyze-session", 
            json=test_data, 
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Analysis endpoint working!")
            
            if result.get("success"):
                analysis = result.get("analysis", {})
                
                print(f"📊 Analysis Results:")
                print(f"   - Session ID: {analysis.get('session_id')}")
                print(f"   - Request count: {analysis.get('request_count')}")
                print(f"   - API endpoints found: {len(analysis.get('api_endpoints', []))}")
                print(f"   - Technologies detected: {analysis.get('technologies', [])}")
                print(f"   - Security score: {analysis.get('security', {}).get('security_score', 0)}%")
                print(f"   - Unique domains: {len(analysis.get('patterns', {}).get('domains', []))}")
                
                # Show API endpoints
                api_endpoints = analysis.get('api_endpoints', [])
                if api_endpoints:
                    print(f"\n🔗 API Endpoints:")
                    for endpoint in api_endpoints:
                        print(f"   - {endpoint}")
                
                # Show recommendations
                recommendations = analysis.get('recommendations', [])
                if recommendations:
                    print(f"\n💡 Recommendations:")
                    for rec in recommendations:
                        print(f"   - {rec}")
                
            else:
                print(f"❌ Analysis failed: {result.get('error')}")
                
        elif response.status_code == 404:
            print("❌ Endpoint not found - server may need restart to pick up new endpoint")
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
    except Exception as e:
        print(f"❌ Request failed: {e}")


if __name__ == "__main__":
    test_analyze_endpoint()
