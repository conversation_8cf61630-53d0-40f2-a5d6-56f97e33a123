# Pump.fun Advanced API Documentation

## Overview

This documentation covers the discovered pump.fun advanced APIs that provide enhanced access to coin data and graduation information.

## Endpoints

### 1. Advanced Coin Listing API

**URL:** `https://advanced-api-v2.pump.fun/coins/list`

**Method:** GET

**Parameters:**
- `sortBy` (string): Sort order - 'creationTime', 'marketCap', 'volume', 'numHolders'
- `limit` (integer): Number of results (1-200)
- `offset` (integer): Pagination offset

**Example Request:**
```
GET https://advanced-api-v2.pump.fun/coins/list?sortBy=volume&limit=50
```

### 2. Graduated Coins API

**URL:** `https://advanced-api-v2.pump.fun/coins/graduated`

**Method:** GET

**Parameters:**
- `sortBy` (string): Sort order - 'creationTime', 'graduationTime'
- `limit` (integer): Number of results (1-200)

**Example Request:**
```
GET https://advanced-api-v2.pump.fun/coins/graduated?limit=100
```

## Response Format

Both APIs return JSON responses with the following structure:

```json
{
  "coins": [
    {
      "coinMint": "string",
      "name": "string",
      "symbol": "string",
      "description": "string",
      "marketCap": number,
      "volume": number,
      "numHolders": number,
      "creationTime": "ISO8601 timestamp",
      "graduationTime": "ISO8601 timestamp" // graduated coins only
    }
  ],
  "total": number,
  "hasMore": boolean
}
```

## Rate Limiting

- Recommended: 20 requests per minute
- Use appropriate delays between requests
- Implement exponential backoff for errors

## Authentication

No authentication required, but proper headers recommended:

```
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: application/json, text/plain, */*
Origin: https://pump.fun
Referer: https://pump.fun/advanced/coin?scan=true
```

---

Generated by Cipher-Spy Deep API Analysis System
