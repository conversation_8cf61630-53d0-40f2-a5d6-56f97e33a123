"""
Enhanced Pump.fun Navigator Agent

Specialized autonomous navigation agent for comprehensive pump.fun exploration.
Systematically navigates through all sections to discover maximum API endpoints.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from urllib.parse import urlparse, urljoin

from playwright.async_api import Page
from ..utils.logging import get_logger


class EnhancedPumpNavigator:
    """
    Enhanced autonomous navigator specifically designed for pump.fun.
    Systematically explores all navigation links and interactive elements.
    """
    
    def __init__(self, page: Page, network_interceptor=None):
        self.page = page
        self.network_interceptor = network_interceptor
        self.logger = get_logger(__name__)
        
        # Navigation tracking
        self.visited_urls: Set[str] = set()
        self.discovered_links: List[Dict[str, Any]] = []
        self.api_calls_by_page: Dict[str, List[Dict[str, Any]]] = {}
        self.navigation_history: List[Dict[str, Any]] = []
        
        # Pump.fun specific navigation targets
        self.pump_fun_sections = [
            # Main navigation
            {'name': 'Home', 'selector': 'a[href="/"]', 'url': '/'},
            {'name': 'Board', 'selector': 'a[href="/board"]', 'url': '/board'},
            {'name': 'Create', 'selector': 'a[href="/create"]', 'url': '/create'},
            {'name': 'Live', 'selector': 'a[href="/live"]', 'url': '/live'},
            
            # Advanced features
            {'name': 'Advanced Coin Scanner', 'selector': 'a[href*="advanced"]', 'url': '/advanced/coin'},
            
            # Documentation
            {'name': 'Terms', 'selector': 'a[href*="terms"]', 'url': '/docs/terms-and-conditions'},
            {'name': 'Privacy', 'selector': 'a[href*="privacy"]', 'url': '/docs/privacy-policy'},
            {'name': 'Fees', 'selector': 'a[href*="fees"]', 'url': '/docs/fees'},
            
            # Board views
            {'name': 'Board Grid View', 'selector': 'a[href="/board?view=grid"]', 'url': '/board?view=grid'},
            {'name': 'Board List View', 'selector': 'a[href="/board?view=list"]', 'url': '/board?view=list'},
        ]
        
        # Interactive elements to explore
        self.interactive_elements = [
            'button',
            'a[href]',
            '[role="button"]',
            '[onclick]',
            '.btn',
            '.button',
            '[data-testid*="button"]',
            'input[type="submit"]',
            'input[type="button"]'
        ]
        
        # Form elements to interact with
        self.form_elements = [
            'input[type="search"]',
            'input[type="text"]',
            'select',
            'textarea'
        ]
    
    async def comprehensive_exploration(self) -> Dict[str, Any]:
        """
        Perform comprehensive exploration of pump.fun.
        
        Returns:
            Dict containing detailed exploration results
        """
        try:
            self.logger.info("🚀 Starting comprehensive pump.fun exploration")
            
            exploration_results = {
                'start_time': datetime.utcnow(),
                'pages_explored': [],
                'api_calls_discovered': [],
                'navigation_flows': [],
                'interactive_elements_found': 0,
                'forms_discovered': 0,
                'total_api_calls': 0
            }
            
            # Phase 1: Navigate through all main sections
            self.logger.info("📍 Phase 1: Exploring main navigation sections")
            for section in self.pump_fun_sections:
                try:
                    page_result = await self._explore_section(section)
                    exploration_results['pages_explored'].append(page_result)
                    
                    # Wait between navigations
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to explore section {section['name']}: {e}")
                    continue
            
            # Phase 2: Discover and explore coin pages
            self.logger.info("🪙 Phase 2: Discovering and exploring coin pages")
            coin_pages = await self._discover_coin_pages()
            for coin_page in coin_pages[:5]:  # Explore top 5 coins
                try:
                    page_result = await self._explore_coin_page(coin_page)
                    exploration_results['pages_explored'].append(page_result)
                    await asyncio.sleep(2)
                except Exception as e:
                    self.logger.warning(f"Failed to explore coin page {coin_page}: {e}")
                    continue
            
            # Phase 3: Interactive element exploration
            self.logger.info("🎮 Phase 3: Interactive element exploration")
            await self._explore_interactive_elements()
            
            # Phase 4: Form interactions
            self.logger.info("📝 Phase 4: Form interaction exploration")
            await self._explore_forms()
            
            # Compile final results
            exploration_results['end_time'] = datetime.utcnow()
            exploration_results['total_duration'] = (
                exploration_results['end_time'] - exploration_results['start_time']
            ).total_seconds()
            
            # Get final API call count
            if self.network_interceptor:
                all_endpoints = self.network_interceptor.get_discovered_endpoints()
                exploration_results['total_api_calls'] = len(all_endpoints)
                exploration_results['api_calls_discovered'] = all_endpoints
            
            exploration_results['pages_visited'] = len(self.visited_urls)
            exploration_results['navigation_history'] = self.navigation_history
            
            self.logger.info(f"✅ Exploration complete: {len(self.visited_urls)} pages, {exploration_results['total_api_calls']} API calls")
            
            return exploration_results
            
        except Exception as e:
            self.logger.error(f"Error during comprehensive exploration: {e}")
            return {'error': str(e), 'exploration_incomplete': True}
    
    async def _explore_section(self, section: Dict[str, Any]) -> Dict[str, Any]:
        """Explore a specific pump.fun section."""
        section_name = section['name']
        section_url = section['url']
        
        self.logger.info(f"🔍 Exploring section: {section_name}")
        
        try:
            # Get API call count before navigation
            api_calls_before = self._get_api_call_count()
            
            # Navigate to section
            full_url = urljoin("https://pump.fun", section_url)
            
            if full_url not in self.visited_urls:
                await self.page.goto(full_url, wait_until="networkidle", timeout=30000)
                self.visited_urls.add(full_url)
                
                # Record navigation
                self.navigation_history.append({
                    'timestamp': datetime.utcnow(),
                    'section': section_name,
                    'url': full_url,
                    'action': 'navigation'
                })
                
                # Wait for page to load and API calls to complete
                await asyncio.sleep(3)
                
                # Get API call count after navigation
                api_calls_after = self._get_api_call_count()
                api_calls_triggered = api_calls_after - api_calls_before
                
                self.logger.info(f"📡 Section {section_name} triggered {api_calls_triggered} API calls")
                
                # Look for section-specific interactions
                await self._explore_section_specific_elements(section_name)
                
                return {
                    'section': section_name,
                    'url': full_url,
                    'api_calls_triggered': api_calls_triggered,
                    'timestamp': datetime.utcnow(),
                    'success': True
                }
            else:
                self.logger.info(f"⏭️  Section {section_name} already visited")
                return {
                    'section': section_name,
                    'url': full_url,
                    'skipped': True,
                    'reason': 'already_visited'
                }
                
        except Exception as e:
            self.logger.error(f"Error exploring section {section_name}: {e}")
            return {
                'section': section_name,
                'url': section_url,
                'error': str(e),
                'success': False
            }
    
    async def _discover_coin_pages(self) -> List[str]:
        """Discover coin pages from the board."""
        try:
            # Navigate to board if not already there
            await self.page.goto("https://pump.fun/board", wait_until="networkidle")
            await asyncio.sleep(2)
            
            # Find coin links
            coin_links = []
            
            # Look for coin links (they typically have /coin/ in the URL)
            coin_link_elements = await self.page.locator('a[href*="/coin/"]').all()
            
            for element in coin_link_elements[:10]:  # Get first 10 coins
                try:
                    href = await element.get_attribute('href')
                    if href and href not in coin_links:
                        coin_links.append(href)
                except:
                    continue
            
            self.logger.info(f"🪙 Discovered {len(coin_links)} coin pages")
            return coin_links
            
        except Exception as e:
            self.logger.error(f"Error discovering coin pages: {e}")
            return []
    
    async def _explore_coin_page(self, coin_url: str) -> Dict[str, Any]:
        """Explore a specific coin page."""
        try:
            full_url = urljoin("https://pump.fun", coin_url)
            
            if full_url in self.visited_urls:
                return {'url': full_url, 'skipped': True}
            
            api_calls_before = self._get_api_call_count()
            
            await self.page.goto(full_url, wait_until="networkidle", timeout=30000)
            self.visited_urls.add(full_url)
            
            # Wait for coin data to load
            await asyncio.sleep(3)
            
            # Look for buy/sell buttons and other interactions
            await self._explore_coin_interactions()
            
            api_calls_after = self._get_api_call_count()
            
            self.logger.info(f"🪙 Coin page triggered {api_calls_after - api_calls_before} API calls")
            
            return {
                'url': full_url,
                'api_calls_triggered': api_calls_after - api_calls_before,
                'timestamp': datetime.utcnow(),
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"Error exploring coin page {coin_url}: {e}")
            return {'url': coin_url, 'error': str(e), 'success': False}
    
    async def _explore_coin_interactions(self):
        """Explore coin-specific interactions without making actual trades."""
        try:
            # Look for buy/sell buttons (but don't click them)
            buy_buttons = self.page.locator('button:has-text("Buy"), [data-testid*="buy"]')
            sell_buttons = self.page.locator('button:has-text("Sell"), [data-testid*="sell"]')
            
            # Look for chart interactions
            chart_elements = self.page.locator('[data-testid*="chart"], .chart, #chart')
            
            # Look for comment/social features
            comment_elements = self.page.locator('button:has-text("Comment"), [data-testid*="comment"]')
            
            # Hover over elements to trigger any hover-based API calls
            try:
                if await buy_buttons.count() > 0:
                    await buy_buttons.first.hover()
                    await asyncio.sleep(1)
                
                if await chart_elements.count() > 0:
                    await chart_elements.first.hover()
                    await asyncio.sleep(1)
                    
            except:
                pass  # Hover interactions are optional
                
        except Exception as e:
            self.logger.debug(f"Error in coin interactions: {e}")
    
    async def _explore_section_specific_elements(self, section_name: str):
        """Explore elements specific to each section."""
        try:
            if section_name == "Board":
                # Try different sorting options
                await self._try_board_sorting()
            elif section_name == "Create":
                # Explore create form (without submitting)
                await self._explore_create_form()
            elif section_name == "Live":
                # Explore live trading interface
                await self._explore_live_interface()
                
        except Exception as e:
            self.logger.debug(f"Error in section-specific exploration for {section_name}: {e}")
    
    async def _try_board_sorting(self):
        """Try different sorting options on the board."""
        try:
            # Look for sort buttons/dropdowns
            sort_elements = self.page.locator('button:has-text("Sort"), select[name*="sort"], [data-testid*="sort"]')
            
            if await sort_elements.count() > 0:
                await sort_elements.first.click()
                await asyncio.sleep(2)
                
        except:
            pass
    
    async def _explore_create_form(self):
        """Explore the create token form (without submitting)."""
        try:
            # Look for form fields
            name_input = self.page.locator('input[name*="name"], input[placeholder*="name"]')
            symbol_input = self.page.locator('input[name*="symbol"], input[placeholder*="symbol"]')
            
            # Fill with test data (but don't submit)
            if await name_input.count() > 0:
                await name_input.first.fill("Test Token")
                await asyncio.sleep(1)
                
            if await symbol_input.count() > 0:
                await symbol_input.first.fill("TEST")
                await asyncio.sleep(1)
                
        except:
            pass
    
    async def _explore_live_interface(self):
        """Explore the live trading interface."""
        try:
            # Look for live trading elements
            live_elements = self.page.locator('[data-testid*="live"], .live-trade, button:has-text("Live")')
            
            if await live_elements.count() > 0:
                await live_elements.first.hover()
                await asyncio.sleep(2)
                
        except:
            pass
    
    async def _explore_interactive_elements(self):
        """Explore interactive elements across the current page."""
        try:
            for selector in self.interactive_elements:
                elements = self.page.locator(selector)
                count = await elements.count()
                
                # Interact with first few elements of each type
                for i in range(min(count, 3)):
                    try:
                        element = elements.nth(i)
                        
                        if await element.is_visible() and await element.is_enabled():
                            # Get element text to avoid dangerous actions
                            text = await element.text_content() or ""
                            text_lower = text.lower()
                            
                            # Skip dangerous actions
                            if any(word in text_lower for word in ['delete', 'remove', 'logout', 'sign out']):
                                continue
                            
                            # Hover to trigger any hover-based API calls
                            await element.hover()
                            await asyncio.sleep(0.5)
                            
                    except:
                        continue
                        
        except Exception as e:
            self.logger.debug(f"Error exploring interactive elements: {e}")
    
    async def _explore_forms(self):
        """Explore forms on the current page."""
        try:
            for selector in self.form_elements:
                elements = self.page.locator(selector)
                count = await elements.count()
                
                for i in range(min(count, 2)):
                    try:
                        element = elements.nth(i)
                        
                        if await element.is_visible() and await element.is_enabled():
                            # Fill with appropriate test data
                            if 'search' in selector:
                                await element.fill("test")
                            elif 'text' in selector:
                                await element.fill("test input")
                            
                            await asyncio.sleep(0.5)
                            
                    except:
                        continue
                        
        except Exception as e:
            self.logger.debug(f"Error exploring forms: {e}")
    
    def _get_api_call_count(self) -> int:
        """Get current API call count from network interceptor."""
        if self.network_interceptor:
            endpoints = self.network_interceptor.get_discovered_endpoints()
            return len(endpoints)
        return 0
    
    def get_exploration_summary(self) -> Dict[str, Any]:
        """Get summary of exploration results."""
        return {
            'pages_visited': len(self.visited_urls),
            'navigation_actions': len(self.navigation_history),
            'visited_urls': list(self.visited_urls),
            'navigation_history': self.navigation_history,
            'total_api_calls': self._get_api_call_count()
        }
