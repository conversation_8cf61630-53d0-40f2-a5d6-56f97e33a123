"""
API module for Cipher-Spy.

Provides REST API endpoints for managing scans, targets, findings, and exploits.
Built with FastAPI for high performance and automatic API documentation.
"""

from .dependencies import get_current_user, get_db_session
from .routes import scans, targets, findings, exploits

__all__ = [
    "get_current_user",
    "get_db_session",
    "scans",
    "targets", 
    "findings",
    "exploits"
]
