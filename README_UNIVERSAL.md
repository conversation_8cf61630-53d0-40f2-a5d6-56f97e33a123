# Cipher-Spy Universal API Discovery Framework

🔍 **Advanced API discovery and reverse engineering system that works with ANY website**

Cipher-Spy has evolved into a comprehensive universal API discovery framework that can analyze and reverse-engineer APIs from any website, while maintaining specialized capabilities for cryptocurrency platforms like pump.fun.

## 🚀 Version 2.0.0 - Universal Framework

### Major Enhancements:
- **Universal API Discovery**: Works with any website, not just pump.fun
- **Chrome Extension**: Real-time network traffic monitoring and analysis
- **Modular Architecture**: Pluggable analyzers for different website types
- **Advanced Intelligence**: AI-powered documentation generation and business analysis
- **Hybrid Discovery**: Combines autonomous agents with manual browsing capture

## ✨ Key Features

### 🌐 Universal API Discovery
- **Website-Agnostic**: Analyze APIs from any domain or platform
- **Intelligent Pattern Recognition**: Automatically detects REST, GraphQL, WebSocket APIs
- **Parameter Discovery**: Advanced fuzzing to discover hidden parameters
- **Schema Inference**: Automatic generation of API schemas and documentation

### 🔧 Chrome Extension Integration
- **Real-time Monitoring**: Capture network traffic as you browse
- **Passive Discovery**: Discover APIs through normal website usage
- **Traffic Analysis**: Intelligent filtering and categorization of API calls
- **Export Capabilities**: Multiple export formats for captured data

### 🎯 Specialized Analyzers
- **Pump.fun Analyzer**: Cryptocurrency-specific intelligence extraction
- **Universal Analyzer**: Generic API discovery for any website
- **Extensible Framework**: Easy to add new specialized analyzers

### 🤖 AI-Powered Intelligence
- **Business Logic Extraction**: Understand API business purposes
- **Competitive Analysis**: Compare APIs across different platforms
- **Documentation Generation**: Automatic OpenAPI 3.0 specification creation
- **Client Code Generation**: Auto-generate client libraries

## 🏗️ Architecture

### Universal Discovery Engine
```
Target Config → Discovery Engine → Analyzer Selection → Comprehensive Analysis
     ↓              ↓                    ↓                      ↓
Domain Input → Strategy Planning → Specialized Logic → Results & Docs
```

### Chrome Extension Flow
```
Browser Navigation → Traffic Capture → Real-time Analysis → Backend Integration
        ↓                 ↓                ↓                    ↓
    User Browsing → Network Monitor → Pattern Detection → API Discovery
```

### Core Components
- **Discovery Engine**: Central orchestrator for all analysis workflows
- **Target Configuration**: Flexible configuration system for any website
- **Universal Analyzer**: Generic API discovery strategies
- **Specialized Analyzers**: Domain-specific intelligence extraction
- **Chrome Extension**: Real-time traffic monitoring and analysis
- **Documentation Generator**: Comprehensive API documentation creation

## 🛠️ Technology Stack

### Backend Framework
- **Python 3.11+** with FastAPI
- **Universal API Discovery Engine** with pluggable analyzers
- **Advanced Parameter Fuzzing** with intelligent test generation
- **Schema Analysis** with confidence scoring
- **Business Intelligence Extraction** with AI integration

### Chrome Extension
- **Manifest V3** Chrome extension
- **Real-time Network Interception** with webRequest API
- **Traffic Analysis** with pattern recognition
- **Backend Integration** with REST API communication

### AI Integration
- **OpenRouter API** for advanced documentation generation
- **Claude/GPT Integration** for business intelligence analysis
- **Automated Report Generation** with executive summaries

## 🚀 Quick Start

### 1. Universal API Discovery

#### Analyze Any Website
```bash
# Analyze any website with universal analyzer
python universal_api_analyzer.py --target example.com

# Use specialized pump.fun analyzer
python universal_api_analyzer.py --target pump.fun --analyzer pump_fun --deep

# Compare multiple websites
python universal_api_analyzer.py --compare site1.com site2.com site3.com
```

#### Programmatic Usage
```python
from src.api_discovery.discovery_engine import DiscoveryEngine
from src.api_discovery.target_config import TargetConfig

# Initialize discovery engine
engine = DiscoveryEngine()

# Create target configuration
config = TargetConfig.create_generic_config("example.com")

# Perform discovery
result = await engine.discover_target(config, analyzer_type='universal')
```

### 2. Chrome Extension Setup

#### Installation
1. **Load Extension**
   ```bash
   # Open Chrome and go to chrome://extensions/
   # Enable "Developer mode"
   # Click "Load unpacked" and select chrome_extension/ folder
   ```

2. **Start Backend**
   ```bash
   # Start Cipher-Spy backend
   python -m src.main
   ```

3. **Start Monitoring**
   - Click the Cipher-Spy extension icon
   - Click "Start Monitoring"
   - Browse any website to capture API traffic

#### Extension Features
- **Real-time Traffic Capture**: Monitor all network requests
- **Smart Filtering**: Focus on API calls and relevant traffic
- **Visual Indicators**: See monitoring status and request counts
- **Export Options**: Download captured data in multiple formats
- **Settings Panel**: Configure capture preferences and backend URL

### 3. Hybrid Discovery Workflow

#### Combined Approach
1. **Manual Browsing**: Use Chrome extension to capture traffic
2. **Autonomous Analysis**: Run universal analyzer on discovered domains
3. **Comprehensive Documentation**: Generate complete API specifications
4. **Business Intelligence**: Extract competitive insights and opportunities

## 📁 Enhanced Project Structure

```
cipher-spy/
├── src/
│   ├── api_discovery/           # Universal API Discovery Framework
│   │   ├── analyzers/          # Specialized analyzers (pump.fun, etc.)
│   │   ├── strategies/         # Discovery strategies (REST, GraphQL, etc.)
│   │   ├── navigation/         # Website navigation patterns
│   │   └── configs/            # Target configurations
│   ├── api/routes/             # REST API endpoints
│   │   └── extension.py        # Chrome extension integration
│   ├── services/               # Core services
│   │   ├── traffic_processor.py
│   │   └── extension_bridge.py
│   └── models/                 # Data models
│       └── extension_models.py
├── chrome_extension/           # Chrome Extension
│   ├── manifest.json
│   ├── background.js
│   ├── content.js
│   └── popup/
├── universal_api_analyzer.py   # Universal CLI interface
└── deep_api_analyzer.py       # Legacy pump.fun analyzer
```

## 🔧 Configuration

### Universal Target Configuration
```python
# Generic website configuration
config = TargetConfig.create_generic_config(
    domain="example.com",
    name="Example API Discovery"
)

# Specialized pump.fun configuration
config = TargetConfig.create_pump_fun_config()

# Custom configuration
config = TargetConfig(
    domain="api.example.com",
    name="Custom API Analysis",
    known_endpoints=[
        APIEndpointConfig(
            name="user_api",
            url="https://api.example.com/users",
            method="GET",
            business_value="User management system"
        )
    ],
    parameter_config=ParameterDiscoveryConfig(
        parameter_categories={
            'pagination': ['page', 'limit', 'offset'],
            'filtering': ['search', 'filter', 'category']
        }
    )
)
```

### Chrome Extension Settings
```javascript
// Extension settings
{
  "monitoringEnabled": true,
  "captureHeaders": true,
  "capturePayloads": true,
  "filterPatterns": ["/api/", ".json", "/graphql"],
  "excludePatterns": [".css", ".js", ".png"],
  "backendUrl": "http://localhost:8000",
  "autoAnalysis": true
}
```

## 📊 Analysis Results

### Universal Discovery Output
- **OpenAPI 3.0 Specifications**: Complete API documentation
- **Business Intelligence Reports**: Competitive analysis and insights
- **Client Code**: Auto-generated libraries in multiple languages
- **Performance Analysis**: Response times and reliability metrics
- **Security Assessment**: Missing headers and vulnerabilities

### Chrome Extension Output
- **Traffic Capture**: Complete network request/response data
- **API Categorization**: Automatic classification of API calls
- **Pattern Recognition**: Identification of API patterns and structures
- **Export Formats**: JSON, HAR, CSV, and OpenAPI formats

## 🔒 Security & Privacy

### Data Protection
- **Local Processing**: All analysis performed locally
- **No Data Transmission**: Extension data stays on your machine
- **Configurable Scope**: Control what gets analyzed
- **Rate Limiting**: Respectful of target servers

### Ethical Usage
- **Authorized Testing Only**: Use only on systems you own or have permission to test
- **Responsible Disclosure**: Report vulnerabilities through proper channels
- **Rate Limiting**: Built-in protections against overloading targets

## 🧪 Advanced Usage

### Multi-Target Analysis
```bash
# Analyze multiple targets concurrently
python universal_api_analyzer.py --targets site1.com site2.com site3.com --concurrent 2

# Comparative analysis
python universal_api_analyzer.py --compare pump.fun dexscreener.com coinmarketcap.com
```

### Custom Analyzers
```python
# Create custom analyzer
class CustomAnalyzer(BaseAPIAnalyzer):
    async def discover_endpoints(self):
        # Custom endpoint discovery logic
        pass
    
    async def extract_business_logic(self, endpoint_data):
        # Custom business intelligence extraction
        pass

# Register with discovery engine
engine.register_analyzer('custom', CustomAnalyzer)
```

### Extension Integration
```python
# Process extension data
from src.services.traffic_processor import TrafficProcessor

processor = TrafficProcessor()
analysis = await processor.analyze_session(captured_requests)
```

## 📈 Roadmap

### Upcoming Features
- **WebSocket Analysis**: Real-time API discovery
- **GraphQL Schema Extraction**: Complete GraphQL API analysis
- **Machine Learning**: Improved pattern recognition
- **Multi-Language Clients**: Expanded code generation
- **Advanced Fuzzing**: ML-powered parameter discovery

### Integration Plans
- **Burp Suite Plugin**: Professional security testing integration
- **Postman Collections**: Automatic collection generation
- **CI/CD Integration**: Automated API monitoring
- **Cloud Deployment**: Scalable analysis infrastructure

## 🤝 Contributing

We welcome contributions to the Universal API Discovery Framework!

### Development Setup
```bash
# Clone repository
git clone <cipher-spy-repo>
cd cipher-spy

# Install dependencies
pip install -r requirements.txt

# Run tests
pytest tests/

# Start development server
python -m src.main
```

### Adding New Analyzers
1. Create analyzer class extending `BaseAPIAnalyzer`
2. Implement required methods (`discover_endpoints`, `extract_business_logic`)
3. Register analyzer with discovery engine
4. Add tests and documentation

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🆘 Support

- **Documentation**: Comprehensive guides in `docs/` directory
- **Issues**: GitHub Issues for bug reports and feature requests
- **Security**: Report security issues privately to maintainers

## ⚠️ Disclaimer

Cipher-Spy Universal API Discovery Framework is designed for authorized security testing and research only. Users are responsible for ensuring they have proper authorization before analyzing any systems. The developers are not responsible for any misuse of this tool.

---

**Cipher-Spy Universal** - Discover, analyze, and understand APIs across the entire web.
