"""
Logging configuration and utilities for Cipher-Spy.

Provides structured logging with different output formats for development
and production environments. Includes security-aware logging that filters
sensitive information.
"""

import logging
import logging.config
import sys
from pathlib import Path
from typing import Optional, Dict, Any

import structlog
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.console import Console

from ..config.settings import get_settings


def setup_logging(
    level: str = "INFO",
    environment: str = "development",
    log_file: Optional[Path] = None
) -> None:
    """
    Setup application logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        environment: Environment (development, production)
        log_file: Optional log file path
    """
    settings = get_settings()
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if environment == "production" 
            else structlog.dev.ConsoleRenderer(colors=True)
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Setup handlers
    handlers = []
    
    if environment == "development":
        # Rich console handler for development
        console = Console(stderr=True)
        rich_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True
        )
        rich_handler.setLevel(level)
        handlers.append(rich_handler)
    else:
        # Standard stream handler for production
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setLevel(level)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        stream_handler.setFormatter(formatter)
        handlers.append(stream_handler)
    
    # File handler if specified
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=level,
        handlers=handlers,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("neo4j").setLevel(logging.WARNING)
    logging.getLogger("playwright").setLevel(logging.WARNING)
    
    # Security: Filter sensitive information
    _setup_security_filters()


def _setup_security_filters() -> None:
    """Setup logging filters to remove sensitive information."""
    
    class SensitiveDataFilter(logging.Filter):
        """Filter to remove sensitive data from log messages."""
        
        SENSITIVE_PATTERNS = [
            "password",
            "token",
            "key",
            "secret",
            "credential",
            "auth",
            "session"
        ]
        
        def filter(self, record: logging.LogRecord) -> bool:
            """
            Filter log record to remove sensitive data.
            
            Args:
                record: Log record to filter
                
            Returns:
                bool: True to keep the record, False to drop it
            """
            # Check message for sensitive patterns
            message = str(record.getMessage()).lower()
            
            for pattern in self.SENSITIVE_PATTERNS:
                if pattern in message:
                    # Replace sensitive values with [REDACTED]
                    record.msg = self._redact_sensitive_data(str(record.msg))
                    break
            
            return True
        
        def _redact_sensitive_data(self, message: str) -> str:
            """
            Redact sensitive data from message.
            
            Args:
                message: Original message
                
            Returns:
                str: Message with sensitive data redacted
            """
            import re
            
            # Patterns for common sensitive data
            patterns = [
                (r'password["\s]*[:=]["\s]*[^"\s,}]+', 'password="[REDACTED]"'),
                (r'token["\s]*[:=]["\s]*[^"\s,}]+', 'token="[REDACTED]"'),
                (r'key["\s]*[:=]["\s]*[^"\s,}]+', 'key="[REDACTED]"'),
                (r'secret["\s]*[:=]["\s]*[^"\s,}]+', 'secret="[REDACTED]"'),
                (r'Bearer\s+[A-Za-z0-9\-._~+/]+=*', 'Bearer [REDACTED]'),
                (r'Basic\s+[A-Za-z0-9+/]+=*', 'Basic [REDACTED]'),
            ]
            
            for pattern, replacement in patterns:
                message = re.sub(pattern, replacement, message, flags=re.IGNORECASE)
            
            return message
    
    # Add filter to all handlers
    sensitive_filter = SensitiveDataFilter()
    for handler in logging.root.handlers:
        handler.addFilter(sensitive_filter)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        logging.Logger: Configured logger instance
    """
    return logging.getLogger(name)


class SecurityAwareLogger:
    """
    Security-aware logger that automatically filters sensitive information.
    
    Wraps the standard logger with additional security features for
    penetration testing tools.
    """
    
    def __init__(self, name: str):
        """
        Initialize security-aware logger.
        
        Args:
            name: Logger name
        """
        self.logger = get_logger(name)
        self.name = name
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message with security filtering."""
        self.logger.info(self._filter_message(message), **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message with security filtering."""
        self.logger.warning(self._filter_message(message), **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message with security filtering."""
        self.logger.error(self._filter_message(message), **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message with security filtering."""
        self.logger.debug(self._filter_message(message), **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message with security filtering."""
        self.logger.critical(self._filter_message(message), **kwargs)
    
    def log_scan_start(self, target_url: str, scan_id: str) -> None:
        """Log scan start with sanitized target information."""
        sanitized_url = self._sanitize_url(target_url)
        self.info(f"Starting scan {scan_id} for target: {sanitized_url}")
    
    def log_vulnerability_found(
        self,
        vulnerability: str,
        target: str,
        severity: str = "unknown"
    ) -> None:
        """Log vulnerability discovery."""
        sanitized_target = self._sanitize_url(target)
        self.warning(
            f"Vulnerability found: {vulnerability} "
            f"on {sanitized_target} (severity: {severity})"
        )
    
    def log_exploit_attempt(
        self,
        exploit_name: str,
        target: str,
        approved: bool = False
    ) -> None:
        """Log exploit attempt with approval status."""
        sanitized_target = self._sanitize_url(target)
        status = "approved" if approved else "pending approval"
        self.warning(
            f"Exploit attempt: {exploit_name} "
            f"on {sanitized_target} ({status})"
        )
    
    def _filter_message(self, message: str) -> str:
        """
        Filter sensitive information from log message.
        
        Args:
            message: Original message
            
        Returns:
            str: Filtered message
        """
        # Basic filtering - can be enhanced
        import re
        
        # Remove potential credentials from URLs
        message = re.sub(
            r'://[^:]+:[^@]+@',
            '://[REDACTED]:[REDACTED]@',
            message
        )
        
        # Remove API keys and tokens
        message = re.sub(
            r'[A-Za-z0-9]{32,}',
            '[REDACTED_TOKEN]',
            message
        )
        
        return message
    
    def _sanitize_url(self, url: str) -> str:
        """
        Sanitize URL for logging.
        
        Args:
            url: Original URL
            
        Returns:
            str: Sanitized URL
        """
        import re
        from urllib.parse import urlparse
        
        try:
            parsed = urlparse(url)
            
            # Remove credentials
            if parsed.username or parsed.password:
                sanitized = f"{parsed.scheme}://[REDACTED]@{parsed.hostname}"
                if parsed.port:
                    sanitized += f":{parsed.port}"
                sanitized += parsed.path
                return sanitized
            
            return url
            
        except Exception:
            # If URL parsing fails, apply basic sanitization
            return re.sub(r'://[^:]+:[^@]+@', '://[REDACTED]@', url)
