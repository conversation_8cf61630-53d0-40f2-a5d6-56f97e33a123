"""
Part 2 of Comprehensive Pump API System - Documentation and Report Generation
"""

import json
import requests
from datetime import datetime
from typing import Dict, Any, Optional


class ComprehensivePumpAPISystemPart2:
    """Additional methods for documentation and report generation."""
    
    async def _generate_endpoint_documentation(self, result, category: str) -> Optional:
        """Generate documentation for a single endpoint using <PERSON>."""
        
        # Prepare context for Claude
        prompt = f"""
You are a technical documentation expert. Generate comprehensive API documentation for a pump.fun API endpoint.

ENDPOINT INFORMATION:
- Name: {result.endpoint_name}
- URL: {result.url}
- Method: {result.method}
- Category: {category}
- Parameters: {json.dumps(result.parameters, indent=2)}
- Response Time: {result.response_time_ms:.0f}ms
- Response Size: {result.response_size_bytes} bytes

RESPONSE SCHEMA:
{json.dumps(result.response_schema, indent=2)}

SAMPLE RESPONSE DATA:
{json.dumps(result.response_data, indent=2)[:2000]}

Please generate documentation that includes:

1. **Description**: Clear, concise description of what this endpoint does
2. **Use Cases**: 3-5 practical use cases for this endpoint
3. **Parameters**: Detailed explanation of each parameter with data types and constraints
4. **Response Fields**: Documentation of response fields with examples
5. **Code Samples**: Python and cURL examples
6. **Error Handling**: Common errors and how to handle them
7. **Rate Limits**: Any rate limiting information observed
8. **Authentication**: Authentication requirements if any

Format your response as JSON with the following structure:
{{
    "description": "string",
    "use_cases": ["string", "string", ...],
    "parameters": {{
        "param_name": {{
            "type": "string",
            "description": "string",
            "required": boolean,
            "example": "string"
        }}
    }},
    "response_fields": {{
        "field_name": {{
            "type": "string",
            "description": "string",
            "example": "string"
        }}
    }},
    "code_samples": {{
        "python": "string",
        "curl": "string"
    }},
    "error_handling": "string",
    "rate_limits": "string",
    "authentication": "string"
}}

Be thorough and professional. Focus on practical usage for developers.
"""
        
        try:
            response = requests.post(
                'https://openrouter.ai/api/v1/chat/completions',
                headers={
                    'Authorization': f'Bearer {self.openrouter_api_key}',
                    'Content-Type': 'application/json'
                },
                json={
                    'model': 'anthropic/claude-3.5-sonnet',
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 4000,
                    'temperature': 0.1
                },
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                content = response_data['choices'][0]['message']['content']
                
                # Extract JSON from response
                try:
                    # Find JSON in the response
                    start = content.find('{')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    doc_data = json.loads(json_str)
                    
                    from dataclasses import dataclass
                    
                    @dataclass
                    class EndpointDocumentation:
                        endpoint_name: str
                        description: str
                        use_cases: list
                        parameters: dict
                        response_fields: dict
                        code_samples: dict
                        error_handling: str
                        rate_limits: Optional[str]
                        authentication: Optional[str]
                    
                    return EndpointDocumentation(
                        endpoint_name=result.endpoint_name,
                        description=doc_data.get('description', ''),
                        use_cases=doc_data.get('use_cases', []),
                        parameters=doc_data.get('parameters', {}),
                        response_fields=doc_data.get('response_fields', {}),
                        code_samples=doc_data.get('code_samples', {}),
                        error_handling=doc_data.get('error_handling', ''),
                        rate_limits=doc_data.get('rate_limits'),
                        authentication=doc_data.get('authentication')
                    )
                    
                except json.JSONDecodeError as e:
                    print(f"      ⚠️  Failed to parse Claude response as JSON: {e}")
                    return None
            else:
                print(f"      ⚠️  OpenRouter API error: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"      ⚠️  Documentation generation error: {e}")
            return None
    
    def _format_documentation_markdown(self, doc) -> str:
        """Format documentation as markdown."""
        md = f"""# {doc.endpoint_name}

## Description
{doc.description}

## Use Cases
"""
        for i, use_case in enumerate(doc.use_cases, 1):
            md += f"{i}. {use_case}\n"
        
        md += f"""
## Parameters
"""
        for param_name, param_info in doc.parameters.items():
            required = "**Required**" if param_info.get('required', False) else "*Optional*"
            md += f"- **{param_name}** ({param_info.get('type', 'string')}) - {required}\n"
            md += f"  - {param_info.get('description', '')}\n"
            if 'example' in param_info:
                md += f"  - Example: `{param_info['example']}`\n"
            md += "\n"
        
        md += f"""## Response Fields
"""
        for field_name, field_info in doc.response_fields.items():
            md += f"- **{field_name}** ({field_info.get('type', 'string')})\n"
            md += f"  - {field_info.get('description', '')}\n"
            if 'example' in field_info:
                md += f"  - Example: `{field_info['example']}`\n"
            md += "\n"
        
        if doc.code_samples.get('python'):
            md += f"""## Python Example
```python
{doc.code_samples['python']}
```

"""
        
        if doc.code_samples.get('curl'):
            md += f"""## cURL Example
```bash
{doc.code_samples['curl']}
```

"""
        
        if doc.error_handling:
            md += f"""## Error Handling
{doc.error_handling}

"""
        
        if doc.rate_limits:
            md += f"""## Rate Limits
{doc.rate_limits}

"""
        
        if doc.authentication:
            md += f"""## Authentication
{doc.authentication}

"""
        
        return md
    
    def _generate_master_documentation(self) -> str:
        """Generate master documentation file."""
        md = f"""# Pump.fun API Documentation

*Auto-generated comprehensive documentation*

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This documentation covers the pump.fun APIs discovered through autonomous navigation and reverse engineering. All endpoints have been tested and documented with real response data.

## API Categories

"""
        
        # Group docs by category
        categories = {}
        for doc in self.generated_docs.values():
            category = self._determine_category_from_name(doc.endpoint_name)
            if category not in categories:
                categories[category] = []
            categories[category].append(doc)
        
        for category, docs in categories.items():
            md += f"### {category.title()} APIs\n\n"
            for doc in docs:
                md += f"- [{doc.endpoint_name}](#{doc.endpoint_name.lower().replace('_', '-')})\n"
            md += "\n"
        
        md += """## Quick Start

```python
import requests

# Example: Get trending coins
response = requests.get(
    'https://frontend-api-v3.pump.fun/coins',
    params={
        'offset': 0,
        'limit': 50,
        'sort': 'market_cap',
        'order': 'DESC',
        'includeNsfw': 'false'
    }
)

coins = response.json()
print(f"Found {len(coins)} trending coins")
```

## Detailed Endpoint Documentation

"""
        
        # Add detailed docs for each endpoint
        for doc in self.generated_docs.values():
            md += self._format_documentation_markdown(doc)
            md += "\n---\n\n"
        
        return md
    
    def _determine_category_from_name(self, endpoint_name: str) -> str:
        """Determine category from endpoint name."""
        name = endpoint_name.lower()
        if 'trending' in name:
            return 'trending'
        elif 'for_you' in name:
            return 'recommendations'
        elif 'search' in name:
            return 'search'
        elif 'coin' in name and 'detail' in name:
            return 'coin_details'
        elif 'flag' in name:
            return 'system'
        elif 'runner' in name:
            return 'featured'
        else:
            return 'other'
    
    async def _generate_master_report(self, discovery_results: Dict[str, Any], 
                                    testing_results: Dict[str, Any],
                                    cataloging_results: Dict[str, Any], 
                                    documentation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive master report."""
        print("📋 Generating comprehensive master report...")
        
        # Calculate summary statistics
        total_endpoints_discovered = discovery_results.get('discovered_endpoints', 0)
        successful_tests = testing_results.get('successful_tests', 0)
        total_tests = testing_results.get('total_tests', 0)
        success_rate = testing_results.get('success_rate', 0)
        categories_found = cataloging_results.get('categories', 0)
        endpoints_documented = documentation_results.get('endpoints_documented', 0)
        
        # Generate executive summary
        executive_summary = self._generate_executive_summary(
            total_endpoints_discovered, successful_tests, total_tests, 
            success_rate, categories_found, endpoints_documented
        )
        
        # Generate technical summary
        technical_summary = self._generate_technical_summary()
        
        # Generate business summary
        business_summary = self._generate_business_summary()
        
        # Compile master report
        master_report = {
            'report_metadata': {
                'generated_at': datetime.now().isoformat(),
                'system_version': '1.0.0',
                'analysis_type': 'comprehensive_api_discovery'
            },
            'executive_summary': executive_summary,
            'technical_summary': technical_summary,
            'business_summary': business_summary,
            'detailed_results': {
                'discovery': discovery_results,
                'testing': testing_results,
                'cataloging': cataloging_results,
                'documentation': documentation_results
            },
            'actionable_insights': self._generate_actionable_insights(),
            'recommendations': self._generate_recommendations()
        }
        
        # Save master report
        master_report_file = self.results_dir / "reports" / "master_report.json"
        with open(master_report_file, 'w') as f:
            json.dump(master_report, f, indent=2, default=str)
        
        # Generate markdown report
        markdown_report = self._generate_markdown_report(master_report)
        markdown_report_file = self.results_dir / "reports" / "master_report.md"
        with open(markdown_report_file, 'w') as f:
            f.write(markdown_report)
        
        print(f"   📊 Master report generated: {master_report_file}")
        print(f"   📄 Markdown report generated: {markdown_report_file}")
        
        return master_report
