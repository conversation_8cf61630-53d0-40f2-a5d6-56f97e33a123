#!/usr/bin/env python3
"""
Test Network Interceptor

Quick test to verify that the network interceptor is properly capturing
and storing API endpoints.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.crawling.playwright_crawler import PlaywrightCrawler
from src.crawling.network_interceptor import NetworkInterceptor
from src.utils.logging import setup_logging


async def test_network_interceptor():
    """Test the network interceptor functionality."""
    print("🧪 Testing Network Interceptor")
    print("="*50)
    
    # Setup logging
    setup_logging(level="INFO", environment="development")
    
    # Initialize components
    interceptor = NetworkInterceptor()
    crawler = PlaywrightCrawler(headless=True, delay_ms=1000)
    
    try:
        # Start crawler
        await crawler.start()
        page = crawler.page
        
        # Setup network interception
        await interceptor.setup_page(page)
        
        print("🌐 Navigating to pump.fun...")
        
        # Navigate to pump.fun
        await page.goto("https://pump.fun", wait_until="networkidle")
        
        # Wait for requests to complete
        await asyncio.sleep(5)
        
        # Get discovered endpoints
        endpoints = interceptor.get_discovered_endpoints()
        statistics = interceptor.get_statistics()
        
        print(f"\n📊 RESULTS:")
        print(f"Total Requests: {statistics['total_requests']}")
        print(f"Total Responses: {statistics['total_responses']}")
        print(f"API Endpoints Discovered: {len(endpoints)}")
        print(f"Unique Domains: {statistics['unique_domains']}")
        
        if endpoints:
            print(f"\n🔗 DISCOVERED ENDPOINTS:")
            for i, endpoint in enumerate(endpoints[:10]):  # Show first 10
                method = endpoint.get('method', 'GET')
                url = endpoint.get('url', 'Unknown')
                print(f"  {i+1}. {method} {url}")
            
            if len(endpoints) > 10:
                print(f"  ... and {len(endpoints) - 10} more")
        else:
            print("\n❌ No endpoints discovered - there's an issue!")
        
        return len(endpoints) > 0
        
    except Exception as e:
        print(f"💥 Error: {e}")
        return False
    
    finally:
        try:
            await crawler.stop()
        except:
            pass


async def main():
    """Main entry point."""
    success = await test_network_interceptor()
    
    if success:
        print("\n✅ Network interceptor test PASSED!")
        return 0
    else:
        print("\n❌ Network interceptor test FAILED!")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
